import React, { useState, Component, useEffect, useRef, useReducer, useCallback } from 'react';
import { StyleSheet, ScrollView, Image, Text, View, TextInput, TouchableOpacity, Alert, Modal, KeyboardAvoidingView, Dimensions, ActivityIndicator, useWindowDimensions, TouchableHighlight, FlatList } from 'react-native';
// import firebase from 'firebase';
import { getFirestore, collection, query, where, getDocs, limit, onSnapshot, updateDoc, doc } from "firebase/firestore";
import { signInAnonymously, signInWithCustomToken } from "firebase/auth";
import AsyncStorage from '@react-native-async-storage/async-storage';
//import EvilIcons from 'react-native-vector-icons/EvilIcons';
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import API from '../constant/API';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import ApiClient from '../util/ApiClient';
// import AwesomeAlert from 'react-native-awesome-alerts';
import { useLinkTo, useRoute } from "@react-navigation/native";

import AntDesign from 'react-native-vector-icons/AntDesign';
import DatePicker2 from "react-horizontal-datepicker";
//import DatePicker from "react-native-datepicker";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
// import "../constant/datePicker.css";
import '../constant/reservationDate.css'
import moment, { now } from "moment";

import { prefix } from "../constant/env";

import { ReactComponent as Location } from '../svg/location.svg';
import { ReactComponent as Clock } from '../svg/clock.svg';
import { ReactComponent as Call } from '../svg/call.svg';
import { ReactComponent as Bell } from '../svg/bell.svg';
import { ReactComponent as Cldr } from '../svg/calendar.svg';
//import { ReactComponent as ChevronD } from '../svg/chevrondown.svg';

import imgLogo from '../asset/image/logo.png';
import { checkIfVisibleItem, isMobile, lazyRetry } from '../util/commonFuncs';
import { DataStore } from '../store/dataStore';
// import { color } from 'react-native-reanimated';
import { Picker } from 'react-native-web';

//import Icon from "react-native-vector-icons/MaterialIcons";

//import Dropdown from 'react-dropdown';
// import Select from 'react-select';

// import DayPickerInput from 'react-day-picker/DayPickerInput';
// import { DateUtils } from 'react-day-picker';
// import 'react-day-picker/lib/style.css';
import dateFnsFormat from 'date-fns/format';
import dateFnsParse from 'date-fns/parse';

import { Collections } from '../constant/firebase';
import { RESERVATIONS_SHIFT_TYPE, USER_RESERVATION_STATUS, ORDER_REGISTER_QR_SALT } from '../constant/common';

import Select from 'react-select';
import MultiSelect from "react-multiple-select-dropdown-lite";
import "../constant/styles.css";
import AsyncImage from "../components/asyncImage";
import Ionicons from "react-native-vector-icons/Ionicons";
import Entypo from "react-native-vector-icons/Entypo";

import loadable from '@loadable/component';
import { TempStore } from '../store/tempStore';

import Hashids from 'hashids';

import {
  APP_TYPE,
  CHARGES_TYPE,
  ORDER_TYPE,
  PRODUCT_PRICE_TYPE,
  UNIT_TYPE_SHORT,
  UPSELLING_SECTION,
  UPSELL_BY_TYPE,
  USER_ORDER_STATUS,
} from "../constant/common";
// const SelectLoadable = loadable.lib(() => import("react-select"));
import { v4 as uuidv4 } from "uuid";
import { PaymentStore } from '../store/paymentStore';
import { logEvent } from "firebase/analytics";
import { ANALYTICS, ANALYTICS_PARSED } from "../constant/analytics";

const hashids = new Hashids(ORDER_REGISTER_QR_SALT);

const MenuItemDetailModal = loadable(() => lazyRetry(() => import("../components/menuitemdetailsmodal")));

global.supplementCategoryIdPrev = [];

const ReservationScreen = props => {
  const {
    route,
  } = props;

  console.log('route');
  console.log(route);

  // this.goToLoginState = this.goToLoginState.bind(this);

  const linkTo = useLinkTo();
  // const windowDimensions = useWindowDimensions();
  const {
    width: windowWidth,
    height: windowHeight,
  } = useWindowDimensions();
  const [showAlertLogin, setShowAlertLogin] = useState(false);

  const email = UserStore.useState(s => s.email);
  const name = UserStore.useState(s => s.name);
  const googleId = UserStore.useState(s => s.googleId);
  const imageUrl = UserStore.useState(s => s.imageUrl);
  const tokenId = UserStore.useState(s => s.tokenId);

  const isAuthenticating = CommonStore.useState(s => s.isAuthenticating);
  const selectedOutlet = CommonStore.useState(s => s.selectedOutlet);

  const reservationId = CommonStore.useState(s => s.selectedReservationId);

  const selectedReservationId = CommonStore.useState(s => s.selectedReservationId);
  const selectedReservationPax = CommonStore.useState(s => s.selectedReservationPax);
  const selectedReservationPaxPrev = CommonStore.useState(s => s.selectedReservationPaxPrev);
  const userPhone = CommonStore.useState(s => s.selectedAddressUserPhone);
  const userEmail = CommonStore.useState(s => s.selectedUserEmail);
  const userFirstName = CommonStore.useState(s => s.selectedUserFirstName);
  const userLastName = CommonStore.useState(s => s.selectedUserLastName);
  const remarks = CommonStore.useState(s => s.selectedUserRemarks);
  const dRestrictions = CommonStore.useState(s => s.selectedUserDiet);
  const sOccasions = CommonStore.useState(s => s.selectedUserOccasion);

  const isLoading = CommonStore.useState(s => s.isLoading);

  const [reservationDate, setReservationDate] = useState(moment().add(1, 'day').valueOf());
  //const [supplementModal, setSupplementModal] = useState(true);

  const [selectedOutletItems, setSelectedOutletItems] = useState([]);

  const supplementModal = TempStore.useState(s => s.supplementModal);
  // const selectedOutletItemsRaw = CommonStore.useState((s) => s.selectedOutletItems);
  const selectedOutletItemsRaw = CommonStore.useState(
    (s) => s.selectedOutletItems.filter(
      (item) =>
        item.priceType === undefined ||
        item.priceType === PRODUCT_PRICE_TYPE.FIXED ||
        item.priceType === PRODUCT_PRICE_TYPE.UNIT
    )
  );
  const cartItems = CommonStore.useState(s => s.cartItems);
  const selectedReservationStartTime = CommonStore.useState(s => s.selectedReservationStartTime);

  const cartOutletId = CommonStore.useState((s) => s.cartOutletId);
  const outletSections = CommonStore.useState((s => s.selectedOutletSections));
  const overrideItemPriceSkuDict = CommonStore.useState((s) => s.overrideItemPriceSkuDict);
  const selectedOutletItemCategoriesDict = CommonStore.useState((s) => s.selectedOutletItemCategoriesDict);
  const overrideCategoryPriceNameDict = CommonStore.useState((s) => s.overrideCategoryPriceNameDict);
  const orderType = CommonStore.useState((s) => s.orderType);
  const amountOffCategoryNameDict = CommonStore.useState((s) => s.amountOffCategoryNameDict);
  const percentageOffCategoryNameDict = CommonStore.useState((s) => s.percentageOffCategoryNameDict);
  const pointsRedeemCategoryNameDict = CommonStore.useState((s) => s.pointsRedeemCategoryNameDict);
  const buy1Free1CategoryNameDict = CommonStore.useState((s) => s.buy1Free1CategoryNameDict);
  const [currUpsellingCampaign, setCurrUpsellingCampaign] = useState(null);
  const availableUpsellingCampaigns = CommonStore.useState(s => s.availableUpsellingCampaigns);
  const selectedSectionName = CommonStore.useState((s) => s.selectedReservationOutletSectionName);
  const selectedOutletItem = CommonStore.useState(s => s.selectedOutletItem);
  const cartItemsProcessed = CommonStore.useState(s => s.cartItemsProcessed);
  const currCrmUser = CommonStore.useState(s => s.currCrmUser);
  const selectedOutletCRMTagsDict = CommonStore.useState(s => s.selectedOutletCRMTagsDict);

  const [toRecommendedItems, setToRecommendedItems] = useState([]);

  const [effectiveDays, setEffectiveDays] = useState(moment().day());
  const [tempItem, setTempItem] = useState({});
  const [cartWarning, setCartWarning] = useState(false);
  const [sectionSelection, setSectionSelection] = useState(false);

  const reservationConfig = CommonStore.useState((s) => s.reservationConfig);
  const [selectedReservationTime, setSelectedReservationTime] = useState(null);
  const [isConfigLoading, setIsConfigLoading] = useState(true);
  const selectedMerchant = CommonStore.useState(s => s.selectedMerchant);
  const [filteredOutletCategories, setFilteredOutletCategories] = useState([]);

  const timeCheckItem = CommonStore.useState(s => s.timeCheckItem);

  const selectedOutletItemCategories = CommonStore.useState(
    (s) => s.selectedOutletItemCategories
  );

  // 25-07-2024 category list
  const isSwitchingOutlets = CommonStore.useState((s) => s.isSwitchingOutlets);

  const selectedOutletItemCategory = CommonStore.useState(
    (s) => s.selectedOutletItemCategory
  );

  const catScrollViewRef = useRef(null);
  const catRowScrollViewRef = useRef(null);
  global.categoryWidthByNameDict = global.categoryWidthByNameDict || {};
  global.categoryPosXByNameDict = global.categoryPosXByNameDict || {};
  const [itemHeights, setItemHeights] = useState([]);
  const [isProgrammaticScroll, setIsProgrammaticScroll] = useState(false);
  const [lastCategoryIndex, setLastCategoryIndex] = useState(null);

  const isMounted = useRef(true);


  //////////////////////////////////////////////

  const handleScroll = (event) => {
    if (!isProgrammaticScroll) {
      const yOffset = event.nativeEvent.contentOffset.y;

      for (let i = 0; i < itemHeights.length; i++) {
        const categoryHeight = itemHeights.slice(0, i).reduce((total, height) => total + height, 0);

        // Check if the yOffset is within the range for the current category
        if (yOffset >= categoryHeight - 50 && yOffset < categoryHeight + itemHeights[i] - 50) {
          if (lastCategoryIndex !== i) {
            scrollCategoryRowToCategory(i);
            setLastCategoryIndex(i); // Update the last triggered category
          }
          break; // Optional: Exit loop if you only want to handle the first matching category
        }
      }
    }
  };

  const onItemLayout = (index, event) => {
    const { height } = event.nativeEvent.layout;
    setItemHeights(prev => {
      const updatedHeights = [...prev];
      updatedHeights[index] = height;
      return updatedHeights;
    });
  };

  const scrollToItem = (index) => {
    if (catScrollViewRef.current && itemHeights.length >= index) {
      const offset = itemHeights.slice(0, index).reduce((total, height) => total + height, 0);
      setIsProgrammaticScroll(true);
      const categoryTemp = filteredOutletCategories[index];
      catScrollViewRef.current.scrollTo({ y: offset, animated: true });
      catRowScrollViewRef.current.scrollTo({ x: global.categoryPosXByNameDict[categoryTemp.name] - 100, animated: true });
    }
    setTimeout(() => {
      setIsProgrammaticScroll(false);
    }, 700);
  };

  const scrollCategoryRowToCategory = (index) => {
    if (!isProgrammaticScroll) {
      CommonStore.update((s) => {
        s.selectedOutletItemCategory = filteredOutletCategories[index];
      });
    }
    const categoryTemp = filteredOutletCategories[index];

    if (catRowScrollViewRef.current) {

      catRowScrollViewRef.current.scrollTo({ x: global.categoryPosXByNameDict[categoryTemp.name] - 100, animated: true });
    }

  };


  const refresh = () => { };

  const checkCartOutlet = () => {
    // const outletId = outletData.id
    // console.log(Cart.getOutletId() != null)
    // if (selectedOutlet && selectedOutlet.uniqueId != Cart.getOutletId() && Cart.getOutletId() != null) {
    if (
      selectedOutlet &&
      cartOutletId &&
      selectedOutlet.uniqueId !== cartOutletId
    ) {
      return true;
    }
    return false;
  };

  useEffect(() => {
    isMounted.current = true;

    return () => {
      isMounted.current = false;
    };
  }, []);

  useEffect(() => {
    if (linkTo) {
      DataStore.update((s) => {
        s.linkToFunc = linkTo;
      });

      global.linkToFunc = linkTo;
    }

    if (route.params === undefined ||
      route.params.subdomain === undefined) {
      linkTo && linkTo(`${prefix}/error`);
      console.log('rs > err > 1');
    } else {
      CommonStore.update(s => {
        s.isLoading = true;
      });

      const subdomain = route.params.subdomain;

      if (subdomain) {
        // means all got, can login this user to check all info valid or not

        try {
          // firebase
          //   .auth()
          //   .signInAnonymously()
          signInAnonymously(global.auth)
            .then((result) => {
              // TempStore.update(s => {
              //   s.firebaseAuth = true;
              // });

              const firebaseUid = result.user.uid;

              ApiClient.GET(API.getTokenKWeb + firebaseUid).then(
                async (result) => {
                  console.log("getTokenKWeb");
                  console.log(result);

                  if (result && result.token) {
                    await AsyncStorage.setItem("accessToken", result.token);
                    await AsyncStorage.setItem(
                      "refreshToken",
                      result.refreshToken
                    );

                    global.accessToken = result.token;

                    UserStore.update((s) => {
                      s.firebaseUid = result.userId;
                      s.userId = result.userId;
                      s.role = result.role;
                      s.refreshToken = result.refreshToken;
                      s.token = result.token;
                      s.name = '';
                      s.email = '';
                      s.number = '';

                      s.userGroups = ['EVERYONE'];
                    });

                    var outletSnapshot = null;

                    if (subdomain) {
                      if (subdomain === "192") {
                        // outletSnapshot = await firebase
                        //   .firestore()
                        //   .collection(Collections.Outlet)
                        //   .where(
                        //     "uniqueId",
                        //     "==",
                        //     "b422c1d9-d30b-4de7-ad49-2e601d950919"
                        //   )
                        //   .limit(1)
                        //   .get();

                        outletSnapshot = await getDocs(
                          query(
                            collection(global.db, Collections.Outlet),
                            where(
                              "uniqueId",
                              "==",
                              "b422c1d9-d30b-4de7-ad49-2e601d950919"
                            ),
                            limit(1),
                          )
                        );
                      } else {
                        // outletSnapshot = await firebase
                        //   .firestore()
                        //   .collection(Collections.Outlet)
                        //   .where("subdomain", "==", subdomain)
                        //   .limit(1)
                        //   .get();

                        outletSnapshot = await getDocs(
                          query(
                            collection(global.db, Collections.Outlet),
                            where("subdomain", "==", subdomain),
                            limit(1),
                          )
                        );
                      }
                    } else {
                      CommonStore.update(s => {
                        s.isLoading = false;
                      });

                      console.log("web scan 3");
                      linkTo && linkTo(`${prefix}/error`);
                      console.log('rs > err > 2');
                    }

                    var outlet = {};
                    if (!outletSnapshot.empty) {
                      outlet = outletSnapshot.docs[0].data();
                    }

                    if (
                      outlet &&
                      (outlet.subdomain === subdomain || subdomain === "192")
                    ) {
                      // show pax modal before proceed

                      global.subdomain = outlet.subdomain ? outlet.subdomain : '';

                      await AsyncStorage.setItem(
                        "latestOutletId",
                        outlet.uniqueId
                      );
                      await AsyncStorage.setItem("latestSubdomain", subdomain);

                      document.title = outlet.name;
                      document.getElementsByTagName("META")[2].content =
                        outlet.address;

                      CommonStore.update(
                        (s) => {
                          s.selectedOutlet = outlet;
                        }
                      );

                      ////////////////////////////////////////

                      // 2023-06-07 - Get reservation obj (if got)

                      if (route.params.reservationId) {
                        var reservationId = hashids.decodeHex(route.params.reservationId).toString().insert(8, '-').insert(13, '-').insert(18, '-').insert(23, '-');

                        // const userReservationSnapshot = await firebase.firestore().collection(Collections.UserReservation)
                        //     .where('uniqueId', '==', reservationId)
                        //     .limit(1)
                        //     .get();

                        const userReservationSnapshot = await getDocs(
                          query(
                            collection(global.db, Collections.UserReservation),
                            where('uniqueId', '==', reservationId),
                            limit(1),
                          )
                        );

                        var userReservation = null;

                        if (!userReservationSnapshot.empty) {
                          userReservation = userReservationSnapshot.docs[0].data();
                        }

                        if (userReservation) {
                          CommonStore.update(s => {
                            // s.currPage = 'Reservation';

                            s.selectedReservationId = userReservation.uniqueId;

                            ///////////////////////////////////////////////////////////////

                            // s.selectedReservationStartTime = userReservation.reservationTime;
                            // s.selectedReservationPax = typeof userReservation.pax === 'number' ? userReservation.pax.toFixed(0) : '1';
                            s.selectedReservationPaxPrev = typeof userReservation.pax === 'number' ? userReservation.pax.toFixed(0) : parseInt(userReservation.pax.toFixed(1));
                            s.selectedReservationStartTimePrev = userReservation.reservationTime;
                            // s.editUserReservation = userReservation;

                            // s.selectedAddressUserPhone = userReservation.userPhone;
                            // s.selectedUserEmail = userReservation.userEmail;
                            // s.selectedUserFirstName = userReservation.userFirstName;
                            // s.selectedUserLastName = userReservation.userLastName;
                            // s.selectedUserRemarks = userReservation.remarks;
                            // s.selectedUserDiet = userReservation.dRestrictions;
                            // s.selectedUserOccasion = userReservation.sOccasions;

                            ///////////////////////////////////////////////////////////////

                            // s.resFirstName = '';
                            // s.resLastName = '';
                            // s.resPhoneNum = '';
                            // s.resEmail = '';
                            // s.resRemarks = '';
                            // s.resDietaryRestrictions = '';
                            // s.resSpecialOccasions = '';

                            s.isPlacingReservation = false;
                            s.isPlacingReservationUpselling = false;

                            s.isDepositOnly = false;

                            // s.resShowConfirmationPage = false;
                          });
                        }
                      }
                      else {
                        CommonStore.update(s => {
                          // s.currPage = 'Reservation';

                          s.selectedReservationId = '';
                          // s.selectedReservationStartTime = null;
                          // s.selectedReservationPax = '1';
                          s.selectedReservationPaxPrev = '1';
                          s.editUserReservation = {};
                          s.selectedReservationStartTimePrev = null;

                          // s.reservationIdTemp = '';

                          // s.selectedAddressUserPhone = '';
                          // s.selectedUserEmail = '';
                          // s.selectedUserFirstName = '';
                          // s.selectedUserLastName = '';
                          // s.selectedUserRemarks = '';
                          // s.selectedUserDiet = '';
                          // s.selectedUserOccasion = '';

                          // s.resFirstName = '';
                          // s.resLastName = '';
                          // s.resPhoneNum = '';
                          // s.resEmail = '';
                          // s.resRemarks = '';
                          // s.resDietaryRestrictions = '';
                          // s.resSpecialOccasions = '';

                          s.isPlacingReservation = false;
                          s.isPlacingReservationUpselling = false;

                          s.isDepositOnly = false;

                          // s.resShowConfirmationPage = false;
                        });
                      }

                      ////////////////////////////////////////

                      // end

                      CommonStore.update(s => {
                        s.isLoading = false;
                      });

                      ////////////////////////////////////////
                    } else {
                      CommonStore.update(s => {
                        s.isLoading = false;
                      });

                      console.log("web scan 3");
                      linkTo && linkTo(`${prefix}/error`);
                      console.log('rs > err > 3');
                    }
                  } else {
                    CommonStore.update((s) => {
                      s.alertObj = {
                        title: "Error",
                        message: "Unauthorized access",
                      };

                      s.isLoading = false;
                    });

                    console.log("web scan 4");
                    linkTo && linkTo(`${prefix}/error`);
                    console.log('rs > err > 4');
                  }
                }
              ).catch(ex => {
                console.error(ex);

                CommonStore.update(s => {
                  s.isLoading = false;
                });
              });
            }).catch(ex => {
              console.error(ex);

              CommonStore.update(s => {
                s.isLoading = false;
              });
            });
        }
        catch (ex) {
          console.error(ex);

          CommonStore.update(s => {
            s.isLoading = false;
          });
        }
      } else {
        CommonStore.update(s => {
          s.isLoading = false;
        });

        console.log('rs > err > 5');
        console.loe(route);

        if (selectedOutlet && selectedOutlet.subdomain) {
          linkTo && linkTo(`${prefix}/outlet/${selectedOutlet.subdomain}`);
        }
        else {
          linkTo && linkTo(`${prefix}/error`);
        }
      }
    }
  }, [linkTo, route]);

  useEffect(() => {
    global.currPageStack = [
      ...global.currPageStack,
      'Rerservation',
    ];
  }, []);


  useEffect(() => {
    // var toRecommendedItemsTemp = [];

    if (availableUpsellingCampaigns.length > 0 && (
      cartItems.length > 0 && cartItemsProcessed.length > 0
    )) {
      /////////////////////////////////////////////////////////////

      var upsellingCampaignsReservationTemp = [];

      var toRecommendedItemsTemp = [];
      const availableUpsellingCampaignsFiltered = availableUpsellingCampaigns.filter(campaign =>
        // campaign.upsellingSection === UPSELLING_SECTION.AFTER_CHECKOUT ||
        campaign.upsellingSection === UPSELLING_SECTION.AFTER_CHECKOUT_RECOMMENDATION);

      var isSameOutlet = true;
      if (selectedOutlet && selectedOutlet.uniqueId) {
        availableUpsellingCampaignsFiltered.map(campaign => {
          if (campaign.outletId !== selectedOutlet.uniqueId) {
            isSameOutlet = false;
          }
        })
      }
      else {
        isSameOutlet = false;
      }

      if (isSameOutlet && cartItems && cartItems.length > 0 && availableUpsellingCampaignsFiltered.length > 0) {
        for (var campaignIndex = 0; campaignIndex < availableUpsellingCampaignsFiltered.length; campaignIndex++) {
          var isValidCampaign = false;

          const upsellingCampaign = availableUpsellingCampaignsFiltered[campaignIndex];

          // setCurrUpsellingCampaign(currUpsellingCampaign);

          const upsellingProductList = upsellingCampaign.productList;
          const upsellingProductIdList = upsellingCampaign.productList.map(product => product.productId);

          for (var upsellingIndex = 0; upsellingIndex < upsellingProductIdList.length; upsellingIndex++) {
            for (var i = 0; i < selectedOutletItemsRaw.length; i++) {
              if (upsellingProductIdList[upsellingIndex] === selectedOutletItemsRaw[i].uniqueId) {
                if (checkIfVisibleItem(selectedOutletItemsRaw[i])) {
                  const upsellingItem = upsellingProductList.find(product => product.productId === selectedOutletItemsRaw[i].uniqueId);
                  const priceUpselling = upsellingItem.upsellPrice;

                  var isValidOrderType = selectedOutletItemsRaw[i].hideInOrderTypes && selectedOutletItemsRaw[i].hideInOrderTypes.length > 0
                    ? (selectedOutletItemsRaw[i].hideInOrderTypes.includes(orderType)
                      ? false
                      : true)
                    : true;

                  var isValidActive = (
                    selectedOutletItemsRaw[i].isActive &&
                    (selectedOutletItemsRaw[i].isAvailableDayActive
                      ? (selectedOutletItemsRaw[i].effectiveTypeOptions.includes(effectiveDays) &&
                        selectedOutletItemsRaw[i].effectiveStartTime && selectedOutletItemsRaw[i].effectiveEndTime &&
                        moment().isSameOrAfter(
                          moment(selectedOutletItemsRaw[i].effectiveStartTime)
                            .year(moment().year())
                            .month(moment().month())
                            .date(moment().date())
                        )
                        &&
                        moment().isBefore
                          (moment(selectedOutletItemsRaw[i].effectiveEndTime)
                            .year(moment().year())
                            .month(moment().month())
                            .date(moment().date())
                          ))
                      : true) &&
                    (selectedOutletItemsRaw[i].isOnlineMenu !== undefined ? selectedOutletItemsRaw[i].isOnlineMenu : true) &&
                    (selectedOutletItemsRaw[i].isStockCountActive !== undefined &&
                      selectedOutletItemsRaw[i].isStockCountActive !== false &&
                      selectedOutletItemsRaw[i].stockCount !== undefined &&
                      selectedOutletItemsRaw[i].toSellIgnoreStock !== undefined
                      ? selectedOutletItemsRaw[i].isStockCountActive &&
                      selectedOutletItemsRaw[i].stockCount > 0 &&
                      (selectedOutletItemsRaw[i].toSellIgnoreStock !== undefined
                        ? selectedOutletItemsRaw[i].toSellIgnoreStock
                        : true)
                      : true)
                  );

                  if (isValidOrderType && isValidActive) {
                    var existingItem = cartItems.find(item => item.itemId === selectedOutletItemsRaw[i].uniqueId);
                    // var existingItem = null;
                    // if (selectedOutletItemsRaw[i].uniqueId === selectedOutletItem.uniqueId) {
                    //   existingItem = selectedOutletItem;
                    // }

                    if (!existingItem) {
                      // didn't existed in cart, continue

                      ////////////////////////////////////////////////////////////////

                      // by customer tags

                      if (upsellingItem.upsellByType === UPSELL_BY_TYPE.CUSTOMER) {
                        if (upsellingItem.tagIdList && upsellingItem.tagIdList.length > 0) {
                          // try compare with tags

                          if (currCrmUser && currCrmUser.uniqueId) {
                            if (upsellingItem.tagIdList && upsellingItem.tagIdList.length > 0) {
                              for (var j = 0; j < upsellingItem.tagIdList.length; j++) {
                                var userTagId = upsellingItem.tagIdList[j];

                                if (selectedOutletCRMTagsDict[userTagId] && selectedOutletCRMTagsDict[userTagId].uniqueId) {
                                  var userTag = selectedOutletCRMTagsDict[userTagId];

                                  if (
                                    (userTag.emailList && userTag.emailList.includes(currCrmUser.userEmail))
                                    ||
                                    (userTag.phoneList && userTag.phoneList.includes(currCrmUser.userNumber))
                                  ) {
                                    // toRecommendedItemsTemp.push(selectedOutletItemsRaw[i]);

                                    // toRecommendedItemsTemp.push({
                                    //     ...selectedOutletItemsRaw[i],
                                    //     price: priceUpselling,

                                    //     priceUpselling: priceUpselling,
                                    //     upsellingCampaignId: upsellingCampaign.uniqueId,
                                    // });

                                    isValidCampaign = true;

                                    break;
                                  }
                                }
                              }
                            }
                          }
                        }
                        else {
                          if (
                            // toRecommendedItemsTemp.find(findItem => findItem.uniqueId === selectedOutletItemsRaw[i].uniqueId)
                            upsellingCampaignsReservationTemp.find(findItem => findItem.uniqueId === upsellingCampaign.uniqueId)
                          ) {
                            // means in the recommended list already, no need add
                          }
                          else {
                            // toRecommendedItemsTemp.push({
                            //     ...selectedOutletItemsRaw[i],
                            //     price: priceUpselling,

                            //     priceUpselling: priceUpselling,
                            //     upsellingCampaignId: upsellingCampaign.uniqueId,
                            // });

                            isValidCampaign = true;
                          }
                        }
                      }
                      else if (upsellingItem.upsellByType === UPSELL_BY_TYPE.ORDER_ITEM || upsellingItem.upsellByType === undefined) {
                        if (upsellingItem.tagIdList && upsellingItem.tagIdList.length > 0) {
                          // try compare with tags

                          if (upsellingItem.tagIdList && upsellingItem.tagIdList.length > 0) {
                            var cartOutletItemList = cartItems
                              .filter(item => {
                                var outletItem = selectedOutletItemsRaw.find(outletItem => outletItem.uniqueId === item.itemId);

                                return outletItem ? true : false;
                              })
                              .map(item => {
                                var outletItem = selectedOutletItemsRaw.find(outletItem => outletItem.uniqueId === item.itemId);

                                return outletItem;
                              });

                            //////////////////////////

                            // 2024-03-01 - crash prevention

                            if (cartOutletItemList.find(findItem => !findItem === undefined)) {
                              continue;
                            }

                            //////////////////////////

                            var isValid = false;

                            for (var cartItemIndex = 0; cartItemIndex < cartOutletItemList.length; cartItemIndex++) {
                              if (cartOutletItemList[cartItemIndex] && cartOutletItemList[cartItemIndex].crmUserTagIdList && cartOutletItemList[cartItemIndex].crmUserTagIdList.length > 0) {
                                for (var j = 0; j < cartOutletItemList[cartItemIndex].crmUserTagIdList.length; j++) {
                                  var productTagId = cartOutletItemList[cartItemIndex].crmUserTagIdList[j];

                                  if (upsellingItem.tagIdList.includes(productTagId)) {
                                    isValid = true;
                                    break;
                                  }
                                }
                              }

                              if (cartOutletItemList[cartItemIndex] && selectedOutletItemCategoriesDict && selectedOutletItemCategoriesDict[cartOutletItemList[cartItemIndex].categoryId] &&
                                selectedOutletItemCategoriesDict[cartOutletItemList[cartItemIndex].categoryId].crmUserTagIdList &&
                                selectedOutletItemCategoriesDict[cartOutletItemList[cartItemIndex].categoryId].crmUserTagIdList.length > 0) {
                                for (var j = 0; j < selectedOutletItemCategoriesDict[cartOutletItemList[cartItemIndex].categoryId].crmUserTagIdList.length; j++) {
                                  var categoryIdTagId = selectedOutletItemCategoriesDict[cartOutletItemList[cartItemIndex].categoryId].crmUserTagIdList[j];

                                  if (upsellingItem.tagIdList.includes(categoryIdTagId)) {
                                    isValid = true;
                                    break;
                                  }
                                }
                              }

                              if (isValid) {
                                break;
                              }
                            }

                            if (!isValid) {
                              // means no matched tag, try to find by category

                              // for (var cartItemIndex = 0; cartItemIndex < cartOutletItemList.length; cartItemIndex++) {
                              //   if (cartOutletItemList[cartItemIndex].categoryId === selectedOutletItemsRaw[i].categoryId) {
                              //     isValid = true;
                              //     break;
                              //   }
                              // }
                            }
                          }

                          if (isValid) {
                            if (
                              // toRecommendedItemsTemp.find(findItem => findItem.uniqueId === selectedOutletItemsRaw[i].uniqueId)
                              upsellingCampaignsReservationTemp.find(findItem => findItem.uniqueId === upsellingCampaign.uniqueId)
                            ) {
                              // means in the recommended list already, no need add
                            }
                            else {
                              // toRecommendedItemsTemp.push({
                              //     ...selectedOutletItemsRaw[i],
                              //     price: priceUpselling,

                              //     priceUpselling: priceUpselling,
                              //     upsellingCampaignId: upsellingCampaign.uniqueId,
                              // });

                              isValidCampaign = true;
                            }
                          }
                        }
                        else {
                          if (
                            // toRecommendedItemsTemp.find(findItem => findItem.uniqueId === selectedOutletItemsRaw[i].uniqueId)
                            upsellingCampaignsReservationTemp.find(findItem => findItem.uniqueId === upsellingCampaign.uniqueId)
                          ) {
                            // means in the recommended list already, no need add
                          }
                          else {
                            // toRecommendedItemsTemp.push({
                            //     ...selectedOutletItemsRaw[i],
                            //     price: priceUpselling,

                            //     priceUpselling: priceUpselling,
                            //     upsellingCampaignId: upsellingCampaign.uniqueId,
                            // });

                            isValidCampaign = true;
                          }
                        }
                      }
                    }
                  }
                }
              }

              if (isValidCampaign) {
                break;
              }
            }

            if (isValidCampaign) {
              break;
            }
          }

          // if (toRecommendedItemsTemp.length >= 3) {
          //     break;
          // }

          if (isValidCampaign) {
            if (upsellingCampaign.upsellingSection === UPSELLING_SECTION.AFTER_CHECKOUT) {
              // upsellingCampaignsReservationTemp.push(upsellingCampaign);
            }
            else if (upsellingCampaign.upsellingSection === UPSELLING_SECTION.AFTER_CHECKOUT_RECOMMENDATION) {
              upsellingCampaignsReservationTemp.push(upsellingCampaign);
            }
          }
        }
      }

      global.upsellingCampaignsReservation = upsellingCampaignsReservationTemp;

      // setToRecommendedItems(toRecommendedItemsTemp.slice(0, 3));
      CommonStore.update(s => {
        s.upsellingCampaignsReservation = upsellingCampaignsReservationTemp;
      });

      /////////////////////////////////////////////////////////////
    }

    // setToRecommendedItems(toRecommendedItemsTemp.slice(0, 3));

    // setToRecommendedItems(toRecommendedItemsTemp);

    // CommonStore.update(s => {
    //   s.reservationUpsellingItems = toRecommendedItemsTemp;
    // });
  }, [selectedOutletItemsRaw, cartItems, cartItemsProcessed, currCrmUser, selectedOutletCRMTagsDict, orderType, availableUpsellingCampaigns, selectedOutletItemCategoriesDict, selectedOutlet]);

  ////////////////////////////////////////////////////////////////////////////

  useEffect(() => {
    var filteredOutletCategoriesTemp = selectedOutletItemCategories.filter((category) => {
      return category.hideInOrderTypes && category.hideInOrderTypes.length > 0
        ? category.hideInOrderTypes.includes(orderType)
          ? false
          : true
        : true;
    }).filter(category => {
      let isValidZone = true;
      // if (orderType === ORDER_TYPE.PICKUP) {
      //   // do nothing
      // }
      // else if (orderType === ORDER_TYPE.DINEIN) {
      //   if (category.hideOutletSectionIdList &&
      //     category.hideOutletSectionIdList.length > 0) {
      //     if (category.hideOutletSectionIdList.includes(
      //       selectedOutletSectionId
      //     )) {
      //       isValidZone = false;
      //     }
      //   }
      //   else {
      //     // do nothing
      //   }
      // }

      if (
        isValidZone &&
        (category.isActive || category.isActive === undefined) &&
        (category.isAvailableDayActive
          ? (category.effectiveTypeOptions.includes(effectiveDays) &&
            category.effectiveStartTime && category.effectiveEndTime &&
            moment().isSameOrAfter(
              moment(category.effectiveStartTime)
                .year(moment().year())
                .month(moment().month())
                .date(moment().date())
            )
            &&
            moment().isBefore
              (moment(category.effectiveEndTime)
                .year(moment().year())
                .month(moment().month())
                .date(moment().date())
              )
          )
          : true)) {
        return true;
      }
    });

    filteredOutletCategoriesTemp = filteredOutletCategoriesTemp.sort((a, b) => {
      return (
        (a.orderIndex
          ? a.orderIndex
          : filteredOutletCategoriesTemp.length) -
        (b.orderIndex
          ? b.orderIndex
          : filteredOutletCategoriesTemp.length)
      );
    });

    setFilteredOutletCategories(
      filteredOutletCategoriesTemp
    );
  }, [selectedOutletItemCategories, timeCheckItem,
    // selectedOutletSectionId,
    orderType]);

  const timestampOutletCategory = CommonStore.useState(s => s.timestampOutletCategory);

  // useEffect(() => {
  //   if (filteredOutletCategories.length > 0 && selectedOutletItemCategory && !filteredOutletCategories.find(category => category.uniqueId === selectedOutletItemCategory.uniqueId)) {
  //     InteractionManager.runAfterInteractions(() => {
  //       // global.selectedOutletItemCategory = filteredOutletCategories[0];

  //       console.log('to update selectedOutletItemCategory 1');
  //       console.log(filteredOutletCategories[0]);

  //       CommonStore.update(s => {
  //         s.selectedOutletItemCategory = filteredOutletCategories[0];
  //       });
  //     });
  //   }
  // }, [filteredOutletCategories]);

  // useEffect(() => {
  //   if (filteredOutletCategories.length > 0) {
  //     if (selectedOutletItemCategory && !filteredOutletCategories.find(category => {
  //       if (category.uniqueId === selectedOutletItemCategory.uniqueId) {
  //         return true;
  //       }
  //     })) {
  //       InteractionManager.runAfterInteractions(() => {
  //         // global.selectedOutletItemCategory = filteredOutletCategories[0];

  //         console.log('to update selectedOutletItemCategory 2');
  //         console.log(filteredOutletCategories[0]);

  //         CommonStore.update(s => {
  //           s.selectedOutletItemCategory = filteredOutletCategories[0];
  //         });
  //       });
  //     }
  //   }
  // }, [timestampOutletCategory, filteredOutletCategories]);

  useEffect(() => {
    if (
      // isPlacingReservation
      false
    ) {

    } else {
      // const searchLowerCase = search.toString().toLowerCase();
      const searchLowerCase = '';

      let selectedOutletItemsTemp = [];

      if (searchLowerCase !== '') {
        selectedOutletItemsTemp = selectedOutletItemsRaw.filter((item) => {
          var category = null;

          for (var i = 0; i < filteredOutletCategories.length; i++) {
            if (filteredOutletCategories[i].uniqueId === item.categoryId) {
              category = filteredOutletCategories[i];
            }
          }

          if (category) {
            let isValidZone = true;
            // if (orderType === ORDER_TYPE.PICKUP) {
            //   // do nothing
            // }
            // else if (orderType === ORDER_TYPE.DINEIN) {
            //   if (category.hideOutletSectionIdList &&
            //     category.hideOutletSectionIdList.length > 0) {
            //     if (category.hideOutletSectionIdList.includes(
            //       selectedOutletSectionId
            //     )) {
            //       isValidZone = false;
            //     }
            //   }
            //   else {
            //     // do nothing
            //   }
            // }

            if (
              isValidZone
              &&
              (
                category.hideInOrderTypes && category.hideInOrderTypes.length > 0
                  ? category.hideInOrderTypes.includes(orderType)
                    ? false
                    : true
                  : true
              )
              &&
              (
                (category.isActive || category.isActive === undefined) &&
                (category.isAvailableDayActive
                  ? (category.effectiveTypeOptions.includes(effectiveDays) &&
                    category.effectiveStartTime && category.effectiveEndTime &&
                    moment().isSameOrAfter(
                      moment(category.effectiveStartTime)
                        .year(moment().year())
                        .month(moment().month())
                        .date(moment().date())
                    )
                    &&
                    moment().isBefore
                      (moment(category.effectiveEndTime)
                        .year(moment().year())
                        .month(moment().month())
                        .date(moment().date())
                      )
                  )
                  : true)
              )
            ) {
              if (searchLowerCase !== '') {
                if ((item.name.toLowerCase().includes(searchLowerCase) || (item.barcode && item.barcode.serialNumber.includes(searchLowerCase)))) {
                  return true;
                }
                else {
                  return false;
                }
              }
              else {
                return true;
              }
            }
            else {
              return false;
            }
          }
          else {
            return false;
          }
        });
      }
      else {
        selectedOutletItemsTemp = selectedOutletItemsRaw;
      }

      ////////////////////////////////////////

      // 2024-03-11 - sort items, based on category index

      let orderIndexDefault = 0;

      const filteredOutletCategoriesDict = Object.assign({}, ...filteredOutletCategories.map(obj => ({
        [obj.uniqueId]: obj,

        orderIndex: (obj.orderIndex !== undefined) ? obj.orderIndex : ++orderIndexDefault,
      })));

      selectedOutletItemsTemp = selectedOutletItemsTemp.filter(item => filteredOutletCategoriesDict[item.categoryId] !== undefined).sort((a, b) => {
        const aOrderIndex = filteredOutletCategoriesDict[a.categoryId] &&
          filteredOutletCategoriesDict[a.categoryId].orderIndex ?
          filteredOutletCategoriesDict[a.categoryId].orderIndex : filteredOutletCategories.length;

        const bOrderIndex = filteredOutletCategoriesDict[b.categoryId] &&
          filteredOutletCategoriesDict[b.categoryId].orderIndex ?
          filteredOutletCategoriesDict[b.categoryId].orderIndex : filteredOutletCategories.length;

        return (
          (aOrderIndex) -
          (bOrderIndex)
        );
      });

      ////////////////////////////////////////

      setSelectedOutletItems(selectedOutletItemsTemp);
    }

    // if (isPlacingReservation && isDepositOnly) {
    //   // redirect to cart screen

    //   linkTo && linkTo(`${prefix}/outlet/${selectedOutlet.subdomain}/cart`);
    // }
  }, [
    selectedOutletItemsRaw,
    filteredOutletCategories,
    // isPlacingReservation,
    // isDepositOnly,
    timeCheckItem,
    // search,

    // selectedOutletSectionId,
    orderType,
  ]);

  /////////////////////////////////////////////////////////////////////////

  useEffect(() => {
    // Check if reservationConfig is available
    if (reservationConfig) {
      setIsConfigLoading(false);
    }
  }, [reservationConfig]);

  const [displayDetails, setDisplayDetails] = useState(false);
  const [displayWaitlist, setDisplayWaitlist] = useState(false);
  const [dineInType, setDineInType] = useState(false);

  const [paxValue, setPaxValue] = useState('1');

  const [selectedDate, setSelectedDate] = useState(moment().startOf("day"));
  const [dateSelectedTimestamp, setDateSelectedTimestamp] = useState(moment().startOf('day').valueOf());

  const [outletTables, setOutletTables] = useState([]);
  const [userReservation, setUserReservation] = useState([]);
  const [allReservationAvailabilityList, setAllReservationAvailabilityList] = useState([]);

  // reservation availability list for the selected date
  const [reservationAvailabilityToday, setReservationAvailabilityToday] = useState([]);
  const [reservationAvailabilityTodayNames, setReservationAvailabilityTodayNames] = useState([]);

  // for waitlist modal state
  const [startTime, setStartTime] = useState('');
  const [endTime, setEndTime] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [phoneNum, setPhoneNum] = useState('');
  const [waitlistEmail, setWaitlistEmail] = useState('');

  //const [isClicked, setIsClicked] = useState(false);

  //Greg - Reservation fix
  const [timeSelected, setTimeSelected] = useState('');
  const [resHour, setResHour] = useState([]);
  const [resMin, setResMin] = useState([]);

  useEffect(() => {
    var hourTemp = [];
    var hour = 0;

    for (var i = 0; i < 25; i++) {
      hourTemp.push(hour);
      hour++;
    }
    setResHour(hourTemp);

    setSelectedHour(moment().hour());

    var minTemp = [];
    var min = 0;

    while (min != 60) {
      minTemp.push(min);
      min = min + 5;
    }
    setResMin(minTemp);

    setSelectedMin(moment().minute());
  }, []);

  const [selectedHour, setSelectedHour] = useState(
    moment().endOf().format('HH'),
  );
  const [selectedMin, setSelectedMin] = useState(
    moment().endOf().format('mm'),
  );

  const [outletPax, setOutletPax] = useState([]);
  // const [outletMaxPax, setOutletMaxPax] = useState(selectedOutlet ? selectedOutlet.maxPax : 20);

  // useEffect(() => {
  //   const timeTemp1 = selectedHour + ':' + selectedMin;
  //   setTimeSelected(moment(timeTemp1, ["h:mm A"]).format('hh:mm A'));
  // }), [selectedHour, selectedMin]

  //////////////////////////////////////////////////////////////////////

  useEffect(() => {
    if (linkTo) {
      DataStore.update(s => {
        s.linkToFunc = linkTo;
      });

      global.linkToFunc = linkTo;
    }
  }, [linkTo]);

  useEffect(() => {
    CommonStore.update(s => {
      s.routeName = route.name;
    });
  }, [route]);

  useEffect(() => {
    PaymentStore.update(s => {
      s.cartItemsPayment = [];
    });

    console.log('[stamp] clear');

    CommonStore.update(s => {
      s.currPage = 'Reservation';

      s.orderType = ORDER_TYPE.DINEIN;

      s.cartItems = [];
      s.cartItemsProcessed = [];

      s.cartItemsReservation = [];
      s.cartItemsProcessedReservation = [];

      s.tcCartItems = [];
      s.tcCartItemsProcessed = [];

      s.selectedOutletItem = {};
      s.selectedOutletItemAddOn = {};
      s.selectedOutletItemAddOnChoice = {};
      s.onUpdatingCartItem = null;

      s.cachedAddOnChoiceIdDict = {};

      s.molpayResult = null;

      s.payHybridBody = null;
      s.paymentDetails = null;
      s.orderIdCreated = '';

      s.payTopupCreditBody = null;

      s.menuItemDetailModal = false;

      // s.selectedReservationId = '';
      // s.selectedReservationStartTime = null;
      // s.selectedReservationPax = '1';

      // s.editUserReservation = {};

      // // s.selectedAddressUserPhone = '';
      // // s.selectedUserEmail = '';
      // // s.selectedUserFirstName = '';
      // // s.selectedUserLastName = '';
      // s.selectedUserRemarks = '';
      // s.selectedUserDiet = '';
      // s.selectedUserOccasion = '';

      // // s.resFirstName = '';
      // // s.resLastName = '';
      // // s.resPhoneNum = '';
      // // s.resEmail = '';
      // s.resRemarks = '';
      // s.resDietaryRestrictions = '';
      // s.resSpecialOccasions = '';

      s.isPlacingReservation = false;
      s.isPlacingReservationUpselling = false;

      s.isDepositOnly = false;

      s.resShowConfirmationPage = false;

      s.selectedTaggableVoucher = null;
      s.selectedBundleTaggableVoucher = null;

      s.userLoyaltyStamps = [];
      s.redeemableStackedLoyaltyStamps = [];
      s.toRedeemStackedLoyaltyStamp = {};
    });

    CommonStore.update(s => {
      s.upsellingCampaignsReservation = [];
    });

    const parent = props.navigation.dangerouslyGetParent();
    parent.setOptions({
      tabBarVisible: false,
    });
    return () =>
      parent.setOptions({
        tabBarVisible: true,
      });
  }, []);

  // useEffect(() => {
  //   firebase.auth().onAuthStateChanged((user) => {
  //     if (user) {
  //       // User is signed in, see docs for a list of available properties
  //       // https://firebase.google.com/docs/reference/js/firebase.User

  //       console.log('auth changed!');
  //       console.log(user);

  //       var uid = user.uid;
  //       // ...
  //     } else {
  //       // User is signed out
  //       // ...
  //     }
  //   });
  // }, []);

  // use effect to get the outlet tables from the firestore
  // useEffect(() => {
  //   try {

  //     const unsubscribe =
  //       onSnapshot(
  //         query(
  //           collection(global.db, Collections.OutletTable),
  //           where('outletId', '==', selectedOutlet.uniqueId),
  //         ),
  //         async (snapshot) => {
  //           const outletTables = [];
  //           if (!snapshot.empty) {
  //             snapshot.forEach((doc) => {
  //               const docData = doc.data();

  //               outletTables.push({
  //                 ...docData,
  //               });
  //             });
  //             setOutletTables(outletTables);
  //           }
  //           console.log('Outlet Table list', outletTables);
  //         }
  //       );
  //     return (() => {
  //       // unsubscribe the listener
  //       unsubscribe()
  //     })
  //   }
  //   catch (e) {
  //     console.log(e);
  //   }
  // }, [selectedOutlet]);

  // use effect to get the user reservation from the firestore
  // useEffect(() => {
  //   try {
  //     // const unsubscribe = firebase.firestore().collection(Collections.UserReservation)
  //     //   .where('outletId', '==', selectedOutlet.uniqueId)
  //     //   .onSnapshot(async (snapshot) => {

  //     const unsubscribe =
  //       onSnapshot(
  //         query(
  //           collection(global.db, Collections.UserReservation),
  //           where('outletId', '==', selectedOutlet.uniqueId),
  //           // limit(1),
  //         ),
  //         async (snapshot) => {
  //           const userReservation = [];
  //           if (!snapshot.empty) {
  //             snapshot.forEach((doc) => {
  //               const docData = doc.data();

  //               userReservation.push({
  //                 ...docData,
  //               });
  //             });
  //             setUserReservation(userReservation);
  //           }
  //           console.log('User Reservation list', userReservation);
  //         });
  //     return (() => {
  //       // unsubscribe the listener
  //       unsubscribe()
  //     })
  //   }
  //   catch (e) {
  //     console.log(e);
  //   }
  // }, [selectedOutlet]);

  // use effect to get the data from the firestore
  // useEffect(() => {
  //   try {
  //     // const unsubscribe = firebase.firestore().collection(Collections.ReservationAvailability)
  //     //   // .where('merchantId', '==', merchantId)
  //     //   .where('outletId', '==', selectedOutlet.uniqueId)
  //     //   .onSnapshot(async (snapshot) => {

  //     const unsubscribe =
  //       onSnapshot(
  //         query(
  //           collection(global.db, Collections.ReservationAvailability),
  //           where('outletId', '==', selectedOutlet.uniqueId),
  //           // limit(1),
  //         ),
  //         async (snapshot) => {
  //           const reservationAvailability = [];
  //           if (!snapshot.empty) {
  //             snapshot.forEach((doc) => {
  //               const docData = doc.data();

  //               reservationAvailability.push({
  //                 ...docData,
  //               });
  //             });
  //             setAllReservationAvailabilityList(reservationAvailability);
  //           }
  //           console.log('Reservation Availability List', reservationAvailability)
  //         });
  //     // unsubscribe the listener
  //     return unsubscribe
  //   }
  //   catch (e) {
  //     console.log(e);
  //   }
  // }, [selectedOutlet]);

  // when date changed, filter the time slot available to display
  useEffect(() => {
    let tempAvailabilityList = [];
    let tempAvailabilityNames = [];

    // repeat logic
    allReservationAvailabilityList.forEach((availability) => {
      let dailyHours = [];  // store start time and end time from weekdaysTimeRepeat
      let start;
      let end;
      // make sure the weekdaysTimeRepeat time is length 4 (0830)

      // check if there is stop date and already pass the date
      if (availability.dateStopRepeat &&
        moment(dateSelectedTimestamp).startOf('day').isAfter(moment(availability.dateStopRepeat).startOf('day'))) {
        console.log('after end date')
        return;
      }

      // push unique names only
      if (!tempAvailabilityNames.includes(availability.reservationName)) {
        tempAvailabilityNames.push(availability.reservationName);
      }

      if (availability.repeatShiftType === RESERVATIONS_SHIFT_TYPE.DOES_NOT_REPEAT) {
        // DOES NOT REPEAT

        // check if dateStartSingle is within this day
        if (moment(availability.dateStartSingle).isBetween(
          moment(dateSelectedTimestamp).startOf('day'),
          moment(dateSelectedTimestamp).endOf('day'))) {
          dailyHours = availability.weekdaysTimeRepeat.split('-');
          start = moment(availability.dateStartSingle).startOf('day')
            .add(dailyHours[0].slice(0, 2), 'hours')
            .add(dailyHours[0].slice(2, 4), 'minutes');
          end = moment(availability.dateStartSingle).startOf('day')
            .add(dailyHours[1].slice(0, 2), 'hours')
            .add(dailyHours[1].slice(2, 4), 'minutes');

          tempAvailabilityList.push(...getAllAvailableTimeSlot(start, end, availability, userReservation, paxValue, outletTables,))
        }
      } else if (availability.repeatShiftType === RESERVATIONS_SHIFT_TYPE.DAILY) {
        // DAILY

        dailyHours = availability.weekdaysTimeRepeat.split('-');
        start = moment(dateSelectedTimestamp).startOf('day')
          .add(dailyHours[0].slice(0, 2), 'hours')
          .add(dailyHours[0].slice(2, 4), 'minutes');
        end = moment(dateSelectedTimestamp).startOf('day')
          .add(dailyHours[1].slice(0, 2), 'hours')
          .add(dailyHours[1].slice(2, 4), 'minutes');

        tempAvailabilityList.push(...getAllAvailableTimeSlot(start, end, availability, userReservation, paxValue, outletTables,))
      } else if (availability.repeatShiftType === RESERVATIONS_SHIFT_TYPE.WEEKLY) {
        // WEEKLY

        let timeLapse = 0;
        // repeatEveryShiftPeriod to check if it intersects the calendar week
        while (true) {
          const repeatedWeekDate = moment(availability.dateStartSingle)
            .isoWeekday(1)
            .add(availability.repeatEveryShiftPeriod * timeLapse, 'weeks')

          // if past calendar week range then break
          if (moment(repeatedWeekDate).isAfter(moment(dateSelectedTimestamp).endOf('isoweek'))) {
            break;
          }

          // check if it is in the calendar week range
          if (moment(repeatedWeekDate).isBetween(
            moment(dateSelectedTimestamp).startOf('isoweek'),
            moment(dateSelectedTimestamp).endOf('isoweek'))) {

            // specify the start day of weekday (Mon is 0)
            const day = moment(dateSelectedTimestamp).isoWeekday() - 1;

            // check which day is true to repeat [mon,tue,wed,thu,fri,sat,sun]
            if (availability.repeatOnWeekdays[day] === true) {
              if (availability.isWeekdaysTimeSame) {
                // repeating day is same time (weekdaysTimeRepeat)
                dailyHours = availability.weekdaysTimeRepeat.split('-');
                start = moment(dateSelectedTimestamp)
                  .add(dailyHours[0].slice(0, 2), 'hours')
                  .add(dailyHours[0].slice(2, 4), 'minutes');
                end = moment(dateSelectedTimestamp)
                  .add(dailyHours[1].slice(0, 2), 'hours')
                  .add(dailyHours[1].slice(2, 4), 'minutes');

                tempAvailabilityList.push(...getAllAvailableTimeSlot(start, end, availability, userReservation, paxValue, outletTables,))
              } else {
                // repeating day is different time
                // use weekdaysTime and find the time for current day
                dailyHours = availability.weekdaysTime[day].split('-');
                start = moment(dateSelectedTimestamp)
                  .add(dailyHours[0].slice(0, 2), 'hours')
                  .add(dailyHours[0].slice(2, 4), 'minutes');
                end = moment(dateSelectedTimestamp)
                  .add(dailyHours[1].slice(0, 2), 'hours')
                  .add(dailyHours[1].slice(2, 4), 'minutes');

                tempAvailabilityList.push(...getAllAvailableTimeSlot(start, end, availability, userReservation, paxValue, outletTables,))
              }
            }
            return;
          }

          timeLapse++;
          if (timeLapse > 100) {
            console.log('%cdangerous INFINITE LOOP return ########################',
              "color: blue; font-family:monospace; font-size: 20px")
            break;
          }
          if (availability.repeatEveryShiftPeriod === 0) {
            break;
          }
        }
      }
    });

    // sort the list by start time
    tempAvailabilityList = tempAvailabilityList.sort((a, b) => a.startTimeMilli - b.startTimeMilli);

    setReservationAvailabilityToday(tempAvailabilityList);
    setReservationAvailabilityTodayNames(tempAvailabilityNames);

    console.log('tempList', tempAvailabilityList);
    // console.table(tempAvailabilityNames);
  }, [dateSelectedTimestamp, allReservationAvailabilityList, paxValue, userReservation, outletTables]);

  // useEffect(() => {
  //   const fetchData = async () => {
  //     var tempData = null
  //     const subdomain = route.params.subdomain;
  //     const outletSnapshot = await getDocs(
  //       query(
  //         collection(global.db, Collections.Outlet),
  //         where("subdomain", "==", subdomain),
  //         limit(1),
  //       )
  //     );

  //     if (!outletSnapshot.empty) {
  //       tempData = outletSnapshot.docs[0].data();
  //     }

  //     setOutletMaxPax(tempData.maxPax ? tempData.maxPax : 20)
  //   };

  //   fetchData();

  // }, []);

  useEffect(() => {
    if (selectedOutlet) {
      let outletMaxPax = selectedOutlet.outletMaxPax ? selectedOutlet.outletMaxPax : 20;
      var tempArray = [];

      for (var i = 1; i <= outletMaxPax; i++) {
        if (i == outletMaxPax) {
          tempArray.push(
            {
              label: `${i}+ person`,
              value: i
            });
        }
        else {
          tempArray.push(
            {
              label: `${i} person`,
              value: i
            });
        }
      }
      setOutletPax(tempArray);
    }
  }, [selectedOutlet]);

  // function here
  const responseGoogle = e => {
    console.log(e);

    if (e && e.profileObj && e.profileObj.email) {
      const domainName = e.profileObj.email.slice(e.profileObj.email.indexOf('@') + 1);

      console.log(domainName);

      if (domainName === 'mykoodoo.com' || domainName === 'perksense.com') {
        console.log('valid');

        CommonStore.update(s => {
          s.isAuthenticating = true;
        });

        UserStore.update(s => {
          s.email = e.profileObj.email;
          s.name = e.profileObj.name;
          s.googleId = e.profileObj.googleId;
          s.imageUrl = e.profileObj.imageUrl;

          s.tokenId = e.tokenId;
        });

        try {
          // firebase.auth().signInAnonymously()
          signInAnonymously(global.auth)
            .then((result) => {
              // Signed in..

              console.log('signed in!');
              console.log(result);

              var body = {
                email: e.profileObj.email,
                name: e.profileObj.name,
                googleId: e.profileObj.googleId,
                imageUrl: e.profileObj.imageUrl,
                tokenId: e.profileObj.tokenId,

                firebaseUid: result.user.uid,
              };

              ApiClient.POST(API.loginKoodooCRMByGoogleAccount, body).then(async result => {
                // if (result === true)
                // getOrderHistory()
                //console.log('getOrderHistory');
                //console.log(getOrderHistory);

                console.log('loginKoodooCRMByGoogleAccount');
                console.log(result);

                if (result && result.customToken) {
                  var firebaseToken = result.idToken;

                  try {
                    // result = await firebase.auth().signInWithCustomToken(result.customToken);
                    result = await signInWithCustomToken(global.auth, result.customToken);
                  }
                  catch (error) {
                    console.log('Failed to login with custom token');

                    CommonStore.update(s => {
                      s.alertObj = {
                        title: 'Error',
                        message: 'Unauthorized access',
                      };

                      s.isAuthenticating = false;
                    });
                  }

                  // ApiClient.GET(API.getTokenKCRM + firebaseToken).then(async (result) => {
                  ApiClient.GET(API.getTokenKCRM + e.profileObj.email).then(async (result) => {
                    console.log('getTokenKCRM');
                    console.log(result);

                    if (result && result.token) {
                      await AsyncStorage.setItem('accessToken', result.token);
                      await AsyncStorage.setItem('refreshToken', result.refreshToken);

                      global.accessToken = result.token;

                      UserStore.update(s => {
                        s.userId = result.userId;
                        s.role = result.role;
                        s.refreshToken = result.refreshToken;
                        s.token = result.token;
                      });

                      CommonStore.update(s => {
                        s.isAuthenticating = false;
                      });

                      linkTo('/genagmt/dashboard');
                    }
                    else {
                      CommonStore.update(s => {
                        s.alertObj = {
                          title: 'Error',
                          message: 'Unauthorized access',
                        };

                        s.isAuthenticating = false;
                      });
                    }
                  });
                }
              }).catch(err => {
                console.error(err);

                // setShowAlertLogin(false);

                CommonStore.update(s => {
                  s.alertObj = {
                    title: 'Error',
                    message: 'Unauthorized access',
                  };

                  s.isAuthenticating = false;
                });
              });
            })
            .catch((error) => {
              var errorCode = error.code;
              var errorMessage = error.message;
              // ...

              CommonStore.update(s => {
                s.alertObj = {
                  title: 'Error',
                  message: 'Unauthorized access',
                };

                s.isAuthenticating = false;
              });
            });
        }
        catch (ex) {
          console.error(ex);

          CommonStore.update(s => {
            s.isAuthenticating = false
          });
        }
      }
      else {
        console.log('invalid');

        // setShowAlertLogin(true);

        CommonStore.update(s => {
          s.alertObj = {
            title: 'Error',
            message: 'Unauthorized access',
          };
        });
      }
    }
    else {
      console.log('invalid');
    }
  };

  const getAllAvailableTimeSlot = (startInput, end, availability, userReservation, partyamount, outletTables,) => {
    let tempAvailabilityList = [];
    let start = moment(startInput);

    // interval, add start time with each interval
    while (end >= start.add(availability.intervalMin, 'minutes')) {
      // get the interval start time
      const intervalStartTime = moment(start).subtract(availability.intervalMin, 'minutes');

      // check if the intervalStartTime is before the current time
      if (moment(intervalStartTime).isBefore(moment())) {
        continue;
      }

      let guestNum = 0;
      userReservation.forEach((eachUserReservation) => {
        if (eachUserReservation.status === USER_RESERVATION_STATUS.ACCEPTED &&
          moment(eachUserReservation.reservationTime).isSame(
            moment(intervalStartTime))) {
          guestNum += eachUserReservation.pax;
        }
      });

      // skip current interval if the number of guest is more than the max
      if (guestNum + partyamount > availability.defaultGuestLimit) {
        continue;
      }

      let haveSpace = false;

      // for each table look for a user reservation at that time
      outletTables.forEach((table) => {
        if (haveSpace) return;

        let isReserved = false;

        userReservation.forEach((eachUserReservation) => {
          // exit if already have space or table is reserved
          if (haveSpace || isReserved) return;

          // if table is reserved by the reservation
          if (eachUserReservation.tableId &&
            eachUserReservation.status === USER_RESERVATION_STATUS.ACCEPTED &&
            eachUserReservation.tableId === table.uniqueId) {

            // check if the reservation is between each interval
            if (moment(eachUserReservation.reservationTime).isBetween(
              moment(intervalStartTime), moment(start), 'minutes', '[]')) {
              isReserved = true;
            }

            // check if there is a table turn time clashed
            if (moment(intervalStartTime).isBetween(
              moment(eachUserReservation.reservationTime),
              moment(eachUserReservation.reservationTime).add(table.turnTime || 60, 'minutes'),
              'minutes', '[]')) {
              isReserved = true;
            }
          }
        });

        if (!isReserved) {
          // if there is no reservation, check the pax size
          let maxPax = table.paxMax || 9999;
          let minPax = table.paxMin || 1;

          // check if it can fit and the pax is greater than the min
          if (table.capacity >= (isNaN(+partyamount) ? 10 : +partyamount) &&
            maxPax >= (isNaN(+partyamount) ? 10 : +partyamount) &&
            minPax <= (isNaN(+partyamount) ? 10 : +partyamount)) {
            // if pax size is less than the table size add timeslot to the list
            haveSpace = true;
          }
        }
      });

      if (haveSpace) {
        tempAvailabilityList.push({
          ...availability,
          startTime: intervalStartTime.format('hh:mm A'),
          endTime: start.format('hh:mm A'),
          startTimeMilli: intervalStartTime.valueOf(),
        });
      }
    }

    return tempAvailabilityList;
  }

  const selectedDay = (val) => {
    setSelectedDate(moment(val));
    setDateSelectedTimestamp(moment(val).startOf('day'));

    console.log('val', val)
  };

  const options = [
    { label: '1 person', value: '1' },
    { label: '2 person', value: '2' },
    { label: '3 person', value: '3' },
    { label: '4 person', value: '4' },
    { label: '5 person', value: '5' },
    { label: '6 person', value: '6' },
    { label: '7 person', value: '7' },
    { label: '8 person', value: '8' },
    { label: '9+ person', value: '9' },
  ];

  const BreakFastTimeSelection = [
    { timeSection: 'Break Fast', time: '8:30 AM' },
    { timeSection: 'Break Fast', time: '9:00 AM' },
    { timeSection: 'Break Fast', time: '9:30 AM' },
    { timeSection: 'Break Fast', time: '10:00 AM' },
    { timeSection: 'Break Fast', time: '10:30 AM' },
    { timeSection: 'Break Fast', time: '11:00 AM' },
    { timeSection: 'Break Fast', time: '11:30 AM' },
  ];
  const LunchTimeSelection = [
    { timeSection: 'Lunch', time: '12:00 PM' },
    { timeSection: 'Lunch', time: '12:30 PM' },
    { timeSection: 'Lunch', time: '1:00 PM' },
    { timeSection: 'Lunch', time: '1:30 PM' },
    { timeSection: 'Lunch', time: '2:00 PM' },
    { timeSection: 'Lunch', time: '2:30 PM' },
    { timeSection: 'Lunch', time: '3:00 PM' },
    { timeSection: 'Lunch', time: '3:30 PM' },
    { timeSection: 'Lunch', time: '4:00 PM' },
    { timeSection: 'Lunch', time: '4:30 PM' },
    { timeSection: 'Lunch', time: '5:00 PM' },
    { timeSection: 'Lunch', time: '5:30 PM' },
    { timeSection: 'Lunch', time: '6:00 PM' },
    { timeSection: 'Lunch', time: '6:30 PM' },
  ];
  const DinnerTimeSelection = [
    { timeSection: 'Dinner', time: '7:00 PM' },
    { timeSection: 'Dinner', time: '7:30 PM' },
    { timeSection: 'Dinner', time: '8:00 PM' },
    { timeSection: 'Dinner', time: '8:30 PM' },
    { timeSection: 'Dinner', time: '9:00 PM' },
    { timeSection: 'Dinner', time: '9:30 PM' },
    { timeSection: 'Dinner', time: '10:00 PM' },
  ];

  const timeSlotSelection = [
    { section: 'Breakfast', list: ['8.30 AM', '9:00 AM', '9:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM'] },
    { section: 'Lunch', list: ['12:00 PM', '12:30 PM', '1:00 PM', '1:30 PM', '2:00 PM', '2:30 PM', '3:00 PM', '3:30 PM', '4:00 PM', '4:30 PM', '5:00 PM', '5:30 PM', '6:00 PM', '6:30 PM'] },
    { section: 'Dinner', list: ['7:00 PM', '7:30 PM', '8:00 PM', '8:30 PM', '9:00 PM', '9:30 PM', '10:00 PM'] }
  ];

  const dummySupplements = [
    { name: 'testing1', description: 'supplements description testing', price: 200 },
    { name: 'testing2', description: 'supplements description testing price start from RM 200++ will contact to get more details', price: 200 },
    { name: 'testing3', description: 'supplements description testing', price: 200 },
    { name: 'testing4', description: 'supplements description testing', price: 200 },
  ]

  const country = [
    { label: '+60', value: 'MY' },
  ];

  const handleChange = (event) => {
    // setPaxValue(event.value);
    if (event) {
      // if (event == outletPax.length) {
      //   window.confirm(`For reservation with more than ${event} pax, please contact the restaurant.`)
      // }
      // else {
      setPaxValue(event);
      // }
    }
  };

  const countrySelect = {
    control: (base, state) => ({
      ...base,
      background: "#ECECEC",
      borderRadius: 5,
      //borderWidth: 1,
      //borderColor: '#000',
      width: 75,
      height: 40,
      //marginTop: 30,
      borderTopRightRadius: 0,
      borderBottomRightRadius: 0,
      borderColor: 'transparent',
    }),
    menu: base => ({
      ...base,
      // match border radius
      borderRadius: 5,
      // remove the gap on top of list
      marginTop: 0,
      background: "#ECECEC",
      zIndex: 1,
    }),
    menuList: base => ({
      ...base,
      // removes white space on first and last option
      padding: 0
    }),
    indicatorSeparator: base => ({
      ...base,
      display: 'none'
    }),
    dropdownIndicator: base => ({
      ...base,
      color: "black"
    }),
  };

  const noGuestSelect = {
    control: (base, state) => ({
      ...base,
      background: "#ECECEC",
      borderRadius: 5,
      //borderWidth: 1,
      //borderColor: '#000',
      width: windowWidth < 426 ? 150 : 200,
      height: 40,
    }),
    menu: base => ({
      ...base,
      // match border radius
      borderRadius: 5,
      // remove the gap on top of list
      marginTop: 0,
      background: "#ECECEC",
      zIndex: 1,
    }),
    menuList: base => ({
      ...base,
      // removes white space on first and last option
      padding: 0
    }),
    indicatorSeparator: base => ({
      ...base,
      display: 'none'
    }),
    dropdownIndicator: base => ({
      ...base,
      color: "black"
    }),
  };

  const timeSelect = {
    control: (base, state) => ({
      ...base,
      background: "#ECECEC",
      borderRadius: 5,
      //borderWidth: 1,
      //borderColor: '#000',
      width: 80,
      height: 40,
      zIndex: 999,
    }),
    menu: base => ({
      ...base,
      // match border radius
      borderRadius: 5,
      // remove the gap on top of list
      marginTop: 0,
      background: "#ECECEC",
      zIndex: 999,
    }),
    menuList: base => ({
      ...base,
      // removes white space on first and last option
      padding: 0,
      zIndex: 999,
    }),
    indicatorSeparator: base => ({
      ...base,
      display: 'none'
    }),
    dropdownIndicator: base => ({
      ...base,
      color: "black",
      zIndex: 999,
    }),
    menuPortal: base => ({
      ...base,
      zIndex: 9999
    }),
  };

  const renderTimeSlot = ({ item }) => {
    return (
      <View style={{
        width: windowWidth * 0.42,
        maxWidth: 200,
        height: 50,
        paddingVertical: 10,
        borderRadius: 5,
        alignItems: 'center',
        justifyContent: 'center',
        marginVertical: 10,
        marginHorizontal: 5,
        backgroundColor: Colors.primaryColor,

        position: 'relative',
        zIndex: 1,
      }}>
        {/* {sectionSelection && (selectedReservationStartTime === moment(`${moment(reservationDate).format('DD MMM YYYY')} ${item.time}`, 'DD MMM YYYY hh:mm A').valueOf()) ?
          <View
            style={{
              position: 'absolute',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 0,
              top: -windowHeight * 0.2,
            }}
          >
            <View
              style={{
                height: windowHeight * 0.2,
                width: windowWidth * 0.45,
                backgroundColor: Colors.whiteColor,
                borderRadius: 12,
                padding: 20,
                justifyContent: 'center'
              }}
            >
              {outletSections.length === 0 ?
                <View style={{
                  zIndex: -1,
                  // width: windowWidth * 0.9,
                  // height: '100%',
                  alignSelf: 'center',
                  // marginTop: 20,
                  alignItems: 'center',
                  justifyContent: 'center',

                  // marginTop: windowHeight * 0.1,
                  // backgroundColor: 'red',
                }}>
                  <ActivityIndicator color={Colors.primaryColor} size={"large"} />
                </View>
                :
                <View
                  style={{
                    // alignItems: 'center',
                    justifyContent: 'center',
                    marginTop: 10,
                  }}>
                  <ScrollView style={{ height: windowHeight * 0.18, paddingBottom: 5, }}>
                    <FlatList
                      data={outletSections}
                      renderItem={renderOutletSection}
                      keyExtractor={(item, index) => index}
                      contentContainerStyle={{ height: windowHeight * 0.2 }}
                    />
                  </ScrollView>
                </View>
              }
            </View>
          </View>
          : <></>} */}

        <TouchableOpacity
          style={{}}
          onPress={async () => {
            const parsedDateTime = moment(`${moment(reservationDate).format('DD MMM YYYY')} ${item}`, 'DD MMM YYYY hh:mm A').valueOf();
            const parsedTime = item;

            CommonStore.update((s) => {
              // s.selectedReservationId = reservationId;
              s.selectedReservationStartTime = parsedDateTime;
              // s.selectedReservationPax = paxValue;

              // s.selectedAddressUserPhone = userPhone;
              // s.selectedUserEmail = userEmail;
              // s.selectedUserFirstName = userFirstName;
              // s.selectedUserLastName = userLastName;
              // s.selectedUserRemarks = remarks;
              // s.selectedUserDiet = dRestrictions;
              // s.selectedUserOccasion = sOccasions;
            });
            setSelectedReservationTime(parsedTime);

            // if (selectedReservationId) {
            //   // if is existed reservation, just skip

            //   const subdomain = await AsyncStorage.getItem("latestSubdomain");

            //   linkTo && linkTo(`${prefix}/outlet/${subdomain}/reservation-info`);

            //   TempStore.update(s => {
            //     s.supplementModal = false;
            //   });
            // }
            // else {
            //   TempStore.update(s => {
            //     s.supplementModal = true;
            //   });
            // }
            setSectionSelection(true)

            // console.log('=========outlet section check========', outletSections);

            // const subdomain = await AsyncStorage.getItem("latestSubdomain");
            // linkTo && linkTo(`${prefix}/outlet/${subdomain}/reservation-info/${reservationId}`);
          }}
        >
          <View style={{ position: 'relative' }}>
            <Text style={{ fontSize: 14, fontFamily: 'NunitoSans-Regular', color: Colors.whiteColor }}>
              {item}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    )
  };

  const calculateOperationTime = () => {
    if (reservationConfig && reservationConfig.timeSlotList) {
      const allTimes = reservationConfig.timeSlotList.flatMap(slot => slot.list);
      const earliestTime = allTimes.reduce((earliest, time) =>
        moment(time, 'hh:mm A').isBefore(moment(earliest, 'hh:mm A')) ? time : earliest
      );
      const latestTime = allTimes.reduce((latest, time) =>
        moment(time, 'hh:mm A').isAfter(moment(latest, 'hh:mm A')) ? time : latest
      );
      return `${earliestTime} - ${latestTime}`;
    } else if (timeSlotSelection) {
      const allTimes = timeSlotSelection.flatMap(slot => slot.list);
      const earliestTime = allTimes[0];
      const latestTime = allTimes[allTimes.length - 1];
      return `${earliestTime} - ${latestTime}`;
    }
    return '8:30 AM - 10:30 PM'; // fallback if no data available
  };

  const renderOutletSection = ({ item }) => {
    return (
      <TouchableOpacity
        style={{ zIndex: 1, }}
        onPress={async () => {
          console.log('selectedOutlet.daysBefore', selectedOutlet.daysBefore);
          console.log('seldaysBefore', (moment(reservationDate).diff(moment(), 'days')));

          if (moment().isSame(reservationDate, 'day') && (moment().isAfter(moment(reservationConfig.cutOffTime, 'hh:mm A')))) {
            window.confirm(`The booking time is only available before ${reservationConfig.cutOffTime}.`);
            setSectionSelection(false);
            return;
          }

          if ((moment(reservationDate).diff(moment(), 'days')) < selectedOutlet.daysBefore) {
            window.confirm(`Your reservation date must be ${selectedOutlet.daysBefore} days before. Please select a different date.`);
            setSectionSelection(false);
            return;
          }

          if (paxValue == outletPax.length) {
            window.confirm(`For reservation with more than ${paxValue} pax, please contact the restaurant.`);
            setSectionSelection(false);
          }
          else {
            // const parsedDateTime = moment(`${moment(reservationDate).format('DD MMM YYYY')} ${item.time}`, 'DD MMM YYYY hh:mm A').valueOf();
            const selectedSectionId = item.uniqueId;
            const selectedSectionName = item.sectionName

            CommonStore.update((s) => {
              s.selectedReservationId = reservationId;
              // s.selectedReservationStartTime = parsedDateTime;
              s.selectedReservationPax = paxValue;

              s.selectedAddressUserPhone = userPhone;
              s.selectedUserEmail = userEmail;
              s.selectedUserFirstName = userFirstName;
              s.selectedUserLastName = userLastName;
              s.selectedUserRemarks = remarks;
              s.selectedUserDiet = dRestrictions;
              s.selectedUserOccasion = sOccasions;
              s.selectedReservationOutletSectionId = selectedSectionId;
              s.selectedReservationOutletSectionName = selectedSectionName;
            });

            if (selectedReservationId) {
              // if is existed reservation, just skip

              const subdomain = await AsyncStorage.getItem("latestSubdomain");

              linkTo && linkTo(`${prefix}/outlet/${subdomain}/reservation-info`);

              TempStore.update(s => {
                s.supplementModal = false;
              });
            }
            else {
              TempStore.update(s => {
                s.supplementModal = true;
              });
            }
            console.log('=========outlet section check========', outletSections);

            setSectionSelection(false);
            // const subdomain = await AsyncStorage.getItem("latestSubdomain");
            // linkTo && linkTo(`${prefix}/outlet/${subdomain}/reservation-info/${reservationId}`);
          }

        }}>
        <Text
          style={{
            fontSize: 17,
            fontFamily: 'NunitoSans-SemiBold',
            padding: 10,
            textAlign: 'center',
            borderBottomWidth: 1,
            borderBottomColor: '#cbcbcb'
          }}>
          {item.sectionName}
        </Text>
      </TouchableOpacity>
    )
  };



  const renderSupplement = ({ item, index }) => {

    var itemNameFontSize = 15;

    if (windowWidth <= 360) {
      itemNameFontSize = 13;
      //console.log(windowWidth)
    }

    const itemNameTextScale = {
      fontSize: itemNameFontSize,
    };

    var overrideCategoryPrice = undefined;

    if (
      selectedOutletItemCategoriesDict[item.categoryId] &&
      overrideCategoryPriceNameDict[
      selectedOutletItemCategoriesDict[item.categoryId].name
      ] !== undefined
    ) {
      overrideCategoryPrice =
        overrideCategoryPriceNameDict[
          selectedOutletItemCategoriesDict[item.categoryId].name
        ].overridePrice;
    }

    if (
      // item.categoryId === selectedOutletItemCategory.uniqueId &&
      (item.hideInOrderTypes && item.hideInOrderTypes.length > 0
        ? item.hideInOrderTypes.includes(orderType)
          ? false
          : true
        : true)
    ) {
      var amountOffCategory = undefined;
      if (
        selectedOutletItemCategoriesDict[item.categoryId] &&
        amountOffCategoryNameDict[
        selectedOutletItemCategoriesDict[item.categoryId].name
        ] !== undefined
      ) {
        amountOffCategory =
          amountOffCategoryNameDict[
          selectedOutletItemCategoriesDict[item.categoryId].name
          ];
      }

      var percentageOffCategory = undefined;
      if (
        selectedOutletItemCategoriesDict[item.categoryId] &&
        percentageOffCategoryNameDict[
        selectedOutletItemCategoriesDict[item.categoryId].name
        ] !== undefined
      ) {
        percentageOffCategory =
          percentageOffCategoryNameDict[
          selectedOutletItemCategoriesDict[item.categoryId].name
          ];
      }

      var pointsRedeemCategory = undefined;
      if (
        selectedOutletItemCategoriesDict[item.categoryId] &&
        pointsRedeemCategoryNameDict[
        selectedOutletItemCategoriesDict[item.categoryId].name
        ] !== undefined
      ) {
        pointsRedeemCategory =
          pointsRedeemCategoryNameDict[
          selectedOutletItemCategoriesDict[item.categoryId].name
          ];
      }

      var buy1Free1Category = undefined;
      if (
        selectedOutletItemCategoriesDict[item.categoryId] &&
        buy1Free1CategoryNameDict[
        selectedOutletItemCategoriesDict[item.categoryId].name
        ] !== undefined
      ) {
        buy1Free1Category =
          buy1Free1CategoryNameDict[
          selectedOutletItemCategoriesDict[item.categoryId].name
          ];
      }

      var extraPrice = 0;
      if (
        orderType === ORDER_TYPE.DELIVERY &&
        selectedOutlet &&
        selectedOutlet.deliveryPrice
      ) {
        extraPrice = selectedOutlet.deliveryPrice;
      } else if (
        orderType === ORDER_TYPE.PICKUP &&
        selectedOutlet &&
        selectedOutlet.pickUpPrice
      ) {
        extraPrice = selectedOutlet.pickUpPrice;
      }

      if (orderType === ORDER_TYPE.DELIVERY) {
        extraPrice = item.deliveryCharges || 0;

        if (
          extraPrice &&
          item.deliveryChargesType === CHARGES_TYPE.PERCENTAGE_BASED
        ) {
          extraPrice = (item.price * extraPrice) / 100;
        }

        if (!item.deliveryChargesActive) {
          extraPrice = 0;
        }
      }

      if (orderType === ORDER_TYPE.PICKUP) {
        extraPrice = item.pickUpCharges || 0;

        if (
          extraPrice &&
          item.pickUpChargesType === CHARGES_TYPE.PERCENTAGE_BASED
        ) {
          extraPrice = (item.price * extraPrice) / 100;
        }

        if (!item.pickUpChargesActive) {
          extraPrice = 0;
        }
      }

      ///////////////////////////////////////////

      // 2024-03-11 - category header changes

      // const toShowHeader = global.supplementCategoryIdPrev.includes(item.categoryId) ? false : true;

      // if(!global.supplementCategoryIdPrev.includes(item.categoryId)){
      //   global.supplementCategoryIdPrev.push(item.categoryId);
      // }

      // console.log("id: ", global.supplementCategoryIdPrev);
      // let categoryName = '';
      // if (toShowHeader) {
      //   const foundCategory = filteredOutletCategories.find(category => category.uniqueId === item.categoryId);

      //   if (foundCategory && foundCategory.uniqueId) {
      //     categoryName = foundCategory.name;

      //   }

      // }

      ///////////////////////////////////////////

      return (
        <View
          style={{
            width: '100%',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <TouchableOpacity
            onPress={async () => {
              if (checkCartOutlet()) {
                // setState({ cartWarning: true, })

                setTempItem(item);

                setCartWarning(true);
              } else {
                if (
                  item.isActive &&
                  (item.isAvailableDayActive
                    ? (item.effectiveTypeOptions.includes(effectiveDays) &&
                      item.effectiveStartTime && item.effectiveEndTime &&
                      moment().isSameOrAfter(
                        moment(item.effectiveStartTime)
                          .year(moment().year())
                          .month(moment().month())
                          .date(moment().date())
                      )
                      &&
                      moment().isBefore
                        (moment(item.effectiveEndTime)
                          .year(moment().year())
                          .month(moment().month())
                          .date(moment().date())
                        ))
                    : true) &&
                  (item.isOnlineMenu !== undefined ? item.isOnlineMenu : true) &&
                  (item.isStockCountActive !== undefined &&
                    item.isStockCountActive !== false &&
                    item.stockCount !== undefined &&
                    item.toSellIgnoreStock !== undefined
                    ? item.isStockCountActive &&
                    item.stockCount > 0 &&
                    (item.toSellIgnoreStock !== undefined
                      ? item.toSellIgnoreStock
                      : true)
                    : true)
                ) {
                  // var priceUpselling = item.price;
                  // if (currUpsellingCampaign && currUpsellingCampaign.uniqueId) {
                  //     for (var upsellingIndex = 0; upsellingIndex < currUpsellingCampaign.productList.length; upsellingIndex++) {
                  //         if (currUpsellingCampaign.productList[upsellingIndex].productId === item.uniqueId) {

                  //         }
                  //     }
                  // }

                  CommonStore.update((s) => {
                    s.selectedOutletItem = item;

                    // s.selectedAddOnIdForChoiceQtyDict = {};
                  });

                  // const subdomain = await AsyncStorage.getItem("latestSubdomain");

                  // if (!subdomain) {
                  //   linkTo && linkTo(`${prefix}/outlet/menu/item`);
                  // } else {
                  //   linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu/item`);
                  // }

                  // linkTo && linkTo(`${prefix}/outlet/menu/item`);

                  // global.isFromRecommendedItems = true;

                  global.currUpsellingCampaign = currUpsellingCampaign;

                  if ((isMobile() || !isMobile()) && selectedOutlet && true) {
                    CommonStore.update((s) => {
                      s.menuItemDetailModal = true;
                    });
                    global.redirectFromPage = 'Reservation';

                    global.menuItemDetailModal = true;

                    window.history.pushState({
                      page: 'menuItemDetailModal',
                    }, '');

                    // window.history.pushState(null, '', window.location.href);
                    // window.history.forward();

                    // const subdomain = await AsyncStorage.getItem("latestSubdomain");

                    // if (!subdomain) {
                    //     global.linkToFunc && global.linkToFunc(`${prefix}/outlet/menu/item`);
                    // } else {
                    //     global.linkToFunc && global.linkToFunc(`${prefix}/outlet/${subdomain}/menu/item`);
                    // }

                    // props.navigation.navigate(
                    //     "Product Details - KooDoo Web Order",
                    //     {
                    //         refresh: refresh.bind(this),
                    //         menuItem: item,
                    //         outletData: selectedOutlet,
                    //     }
                    // );
                  }
                  else {
                    props.navigation.navigate(
                      "Product Details - KooDoo Web Order",
                      {
                        refresh: refresh.bind(this),
                        menuItem: item,
                        outletData: selectedOutlet,
                      }
                    );
                  }
                } else {
                  window.confirm(
                    'Info\nSorry, this product is not available for now.',
                  );

                  // CommonStore.update((s) => {
                  //   s.alertObj = {
                  //     title: "Info",
                  //     message: "Sorry, this product is not available for now.",
                  //   };
                  // });
                }
              }
            }}
          >
            <View
              style={{
                flexDirection: "column",
                paddingBottom: 15,
                display: "flex",
                alignContent: "center",
                width: isMobile() ? windowWidth * 0.85 : '100%',
                alignSelf: 'center',
                alignItems: 'center',
                ...(isMobile() ? {} : {
                  //width: windowWidth * 0.17,
                  //width:'flex-start',
                  //width:'auto',
                })
              }}
            >
              <View
                style={{
                  flexDirection: "column",
                  alignContent: "center",
                  alignItems: "center",
                  width: '100%',
                  display: "flex",
                  justifyContent: "flex-start",
                  //backgroundColor: 'blue',
                  alignItems: 'center',

                  ...(isMobile() ? {} : {
                    //width: windowWidth * 0.85,
                  })
                }}
              >
                <View style={{ marginBottom: 15 }}>
                  <View
                    style={[
                      {
                        // backgroundColor: Colors.secondaryColor,
                        // width: 60,
                        // height: 60,
                        width: isMobile()
                          ? windowWidth * 0.89
                          : windowWidth * 0.89,

                        height: Dimensions.get('window').height * 0.2,

                        ...(isMobile() ? {} : {
                          //width: windowWidth * 0.85,
                          alignItems: 'center',
                        })
                      },
                    ]}
                  >
                    {item.image ? (
                      <AsyncImage
                        source={{ uri: item.image }}
                        item={item}
                        hideLoading={true}
                        style={{
                          width: windowWidth * 0.89,
                          height: Dimensions.get('window').height * 0.2,

                          // borderTopLeftRadius: 10,
                          // borderTopRightRadius: 10,
                          marginTop: 10,

                          alignItems: "center",
                          justifyContent: "center",

                          marginBottom: 15,
                        }}
                      />
                    ) : (
                      // <Ionicons name="fast-food-outline" size={50} />
                      <View
                        style={{
                          width: windowWidth * 0.89,
                          height: Dimensions.get('window').height * 0.2,
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          backgroundColor: Colors.secondaryColor,
                          marginTop: 10,

                          // borderTopLeftRadius: 10,
                          // borderTopRightRadius: 10,

                          alignItems: "center",
                          justifyContent: "center",

                          marginBottom: 15,
                        }}
                      >
                        <Ionicons
                          name="fast-food-outline"
                          // size={45}
                          size={
                            isMobile() ? windowWidth * 0.1 : windowWidth * 0.02
                          }
                        />
                      </View>
                    )}

                    {!item.isActive ||
                      !(item.isAvailableDayActive
                        ? (item.effectiveTypeOptions.includes(effectiveDays) &&
                          item.effectiveStartTime && item.effectiveEndTime &&
                          moment().isSameOrAfter(
                            moment(item.effectiveStartTime)
                              .year(moment().year())
                              .month(moment().month())
                              .date(moment().date())
                          )
                          &&
                          moment().isBefore
                            (moment(item.effectiveEndTime)
                              .year(moment().year())
                              .month(moment().month())
                              .date(moment().date())
                            ))
                        : true) ||
                      !(item.isOnlineMenu !== undefined
                        ? item.isOnlineMenu
                        : true) ||
                      !(item.isStockCountActive !== undefined &&
                        item.isStockCountActive !== false &&
                        item.stockCount !== undefined &&
                        item.toSellIgnoreStock !== undefined
                        ? item.isStockCountActive &&
                        item.stockCount > 0 &&
                        (item.toSellIgnoreStock !== undefined
                          ? item.toSellIgnoreStock
                          : true)
                        : true) ? (
                      <View
                        style={{
                          position: "absolute",
                          zIndex: 3,
                          ...(isMobile() ? {} : {
                            //alignSelf:'flex-start',
                            //width: windowWidth * 1,
                          })
                        }}
                      >
                        <View
                          style={{
                            // width: 120,
                            width: isMobile()
                              ? windowWidth * 0.33
                              : windowWidth * 0.06,
                            left: isMobile()
                              ? -windowWidth * 0.001
                              : -windowWidth * 0.01,
                            padding: 0,
                            paddingLeft: isMobile()
                              ? windowWidth * 0.02
                              : windowWidth * 0.005,
                            justifyContent: "center",
                            alignItems: "center",
                            backgroundColor: Colors.tabRed,
                            height: 20,
                            borderTopRightRadius: 2,
                            borderBottomRightRadius: 2,
                            bottom: -10,
                            ...(!item.image && {
                              left: isMobile()
                                ? 0
                                : -windowWidth * 0.005,
                              bottom: isMobile()
                                ? -10
                                : -10,
                            }),
                            ...(isMobile() ? {} : {
                              width: windowWidth * 0.2,
                              alignSelf: 'flex-start',
                              // marginLeft: 0,
                            })
                          }}
                        >
                          <Text
                            style={{
                              color: "#FFF",
                              fontFamily: "NunitoSans-Bold",
                              fontSize: 10,
                              bottom: 1,
                            }}
                          >
                            Not available
                          </Text>
                        </View>
                      </View>
                    ) : (
                      <></>
                    )}

                  </View>
                </View>

                <View
                  style={{
                    marginLeft: -20,
                    width: isMobile() ? "100%" : '65%',
                    paddingLeft: isMobile() ? 1 : 0,

                    ...(isMobile() ? {} : {
                      width: windowWidth * 0.85,
                      alignItems: 'center',
                    })
                  }}
                >

                  <Text
                    // numberOfLines={1}
                    style={[
                      itemNameTextScale,
                      {
                        fontSize: 14,
                        textTransform: "uppercase",
                        fontFamily: "NunitoSans-Bold",
                        textAlign: 'left',
                        //marginBottom: 10,
                        marginTop: 5,
                        //height: isMobile() ? 60 : 40,
                        marginLeft: 10,

                        alignItems: 'center',
                        ...(isMobile() ? {
                          alignSelf: 'flex-start',
                        } : {
                          alignSelf: 'flex-start',
                        })
                      },
                    ]}
                    numberOfLines={isMobile() ? 3 : 2}
                  >
                    {item.name}
                  </Text>

                  <Text
                    // numberOfLines={1}
                    style={[
                      itemNameTextScale,
                      {
                        fontSize: 14,
                        //textTransform: "uppercase",
                        fontFamily: "NunitoSans-SemiBold",
                        textAlign: 'left',
                        marginBottom: 5,
                        //marginTop: -10,
                        marginLeft: 10,
                        //height: isMobile() ? 60 : 40,
                        ...(isMobile() ? {
                          alignSelf: 'flex-start',
                        } : {
                          alignSelf: 'flex-start',
                        })
                      },
                    ]}
                    numberOfLines={isMobile() ? 3 : 2}
                  >
                    {item.description.trim()}
                  </Text>

                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      marginLeft: 10,
                      marginTop: -5,
                      justifyContent: 'space-between',

                      ...(isMobile() ? {} : {
                        alignSelf: 'flex-start',
                        width: '100%',
                      })
                    }}
                  >
                    {/* {console.log('check override')}
                    {console.log(overrideItemPriceSkuDict[item.sku])}
                    {console.log(overrideCategoryPrice)} */}
                    <View style={{
                      flexDirection: 'row', paddingTop: 9,
                      ...(isMobile() ? {} : {
                        alignItems: 'center',
                        marginRight: 15,
                      })
                    }}>
                      <Text
                        style={{
                          color: Colors.primaryColor,
                          fontFamily: "NunitoSans-Bold",
                          paddingTop: 0,
                          fontSize: 14,
                          textDecorationLine:
                            (overrideItemPriceSkuDict[item.sku] !== undefined ||
                              overrideCategoryPrice !== undefined)
                              ? "line-through"
                              : "none",

                          // ...((!(overrideItemPriceSkuDict[item.sku] !== undefined || overrideCategoryPrice !== undefined))) && {
                          //   fontSize: 10,
                          //   paddingTop: 3,
                          //   textDecorationLine: 'line-through'
                          // },

                          ...(overrideItemPriceSkuDict[item.sku] !== undefined ||
                            overrideCategoryPrice !== undefined) && {
                            fontSize: 10,
                            paddingTop: 4.5,
                          },
                        }}
                      >
                        RM{parseFloat(extraPrice + item.price).toFixed(2)}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[item.unitType]}` : ''}
                      </Text>

                      {/* {((!(overrideItemPriceSkuDict[item.sku] !== undefined || overrideCategoryPrice !== undefined))) ?
                      <Text
                        style={{
                          color: Colors.secondaryColor,
                          fontFamily: "NunitoSans-Bold",
                          paddingTop: 0,
                          fontSize: 14,
                          marginLeft: 5,
                        }}
                      >
                        RM{parseFloat(extraPrice + item.price).toFixed(2)}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[item.unitType]}` : ''}
                      </Text> :
                      <></>} */}

                      {overrideItemPriceSkuDict[item.sku] !== undefined ||
                        overrideCategoryPrice !== undefined ? (
                        <Text
                          style={{
                            color: Colors.secondaryColor,
                            fontFamily: "NunitoSans-Bold",
                            //paddingTop: 5,
                            fontSize: 14,
                            //marginLeft: 5,
                          }}
                        >
                          {' RM'}
                          {overrideItemPriceSkuDict[item.sku] &&
                            overrideItemPriceSkuDict[item.sku].overridePrice !==
                            undefined
                            ? parseFloat(
                              overrideItemPriceSkuDict[item.sku].overridePrice
                            ).toFixed(2)
                            : parseFloat(overrideCategoryPrice).toFixed(2)}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[item.unitType]}` : ''}
                        </Text>
                      ) : (
                        <></>
                      )}
                    </View>
                    <Entypo name="squared-plus" size={25} />
                  </View>
                </View>
              </View>
            </View>
          </TouchableOpacity >
        </View>
      );
    }
  };

  const createWaitList = () => {
    if (startTime.length === 0 || endTime.length === 0 || firstName.length === 0 || lastName.length === 0 || phoneNum.length === 0 || waitlistEmail.length === 0) {
      CommonStore.update(s => {
        s.alertObj = {
          title: 'Info',
          message: 'Please fill in the following fields:\n\n1. First Name\n2. Last Name\n3. Phone Number\n4. Email Address',
        };
      });
      setDisplayWaitlist(false);
      return;
    }
    // validate phone number
    if ((phoneNum.length !== 10 && phoneNum.length != 11) || !phoneNum.match(/^[0-9]+$/)) {
      CommonStore.update(s => {
        s.alertObj = {
          title: 'Error',
          message: 'Incorrect Phone number format',
        };
      });
      setDisplayWaitlist(false);
      return;
    }

    const body = {
      date: selectedDate.valueOf(),
      startTime: startTime,
      endTime: endTime,
      firstName: firstName,
      lastName: lastName,
      phoneNum: phoneNum,
      waitlistEmail: waitlistEmail,
      pax: paxValue,
    }

    console.log('body', body);

    ApiClient.POST(API.createWaitlist, body)
      .then((response) => {
        console.log(response);
        CommonStore.update(s => {
          s.alertObj = {
            title: 'Success',
            message: 'Waitlist request sent',
          };
        });
        setDisplayWaitlist(false);
      })
      .catch((error) => {
        console.log(error);
        CommonStore.update(s => {
          s.alertObj = {
            title: 'Error',
            message: 'Waitlist request failed',
          };
        });
        setDisplayWaitlist(false);
      });
  }

  const countDaysBefore = (selectedDate) => {
    // Get the current date
    const currentDate = moment();
    const daysBefore = parseInt(selectedOutlet.daysBefore)

    // Get the date 3 days before the current date
    const numDaysBefore = moment().subtract(daysBefore, 'days');

    // Check if the selected date is 3 days before the current date
    return moment(selectedDate).isSameOrBefore(currentDate) && moment(selectedDate).isSameOrAfter(numDaysBefore);
  };


  // function end

  return (
    <ScrollView showsHorizontalScrollIndicator={false}>
      <Modal
        visible={displayDetails}
        transparent={true}
        supportedOrientations={['portrait', 'landscape']}
        animationType="fade"
        onRequestClose={() => { setDisplayDetails(false) }}
      >
        <View style={{
          backgroundColor: 'rgba(0,0,0,0.5)',
          height: windowHeight,
          justifyContent: 'center',
        }}>
          <View style={{
            alignSelf: 'center',
            width: windowWidth * 0.8,
            height: windowHeight * 0.3,
            backgroundColor: 'white',

            borderRadius: 10,
            borderColor: '#E5E5E5',

            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: 0.22,
            shadowRadius: 3.22,
            elevation: 1,
            zIndex: -1,

            marginTop: 40,
          }}>
            <TouchableOpacity
              onPress={() => { setDisplayDetails(false) }}
            >
              <View style={{
                marginTop: 10,
                marginRight: 10,
                alignSelf: 'flex-end',
                height: 20,
              }}>
                <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
              </View>
            </TouchableOpacity>
            <Text style={{
              fontFamily: 'NunitoSans-Bold',
              color: 'black',
              fontSize: 20,
              textAlign: 'center',
              marginTop: 0,
              // width: '100%',
            }}>
              {selectedOutlet ? selectedOutlet.name : ''}
            </Text>
            <View
              style={{
                marginLeft: '6%'
              }}>

              <View style={{
                //justifyContent: 'center',
                alignItems: 'center',
                flexDirection: 'row',
                marginTop: 10,
                marginLeft: '-1%'
                //width: isMobile() ? windowWidth : windowWidth * 1,
              }}>
                <Location
                  width={25}
                  height={25}
                  color={'black'}
                />
                <Text style={{
                  fontFamily: 'NunitoSans-Regular',
                  color: 'black',
                  fontSize: 16,
                  //textAlign: 'center',
                  marginTop: 0,
                  marginLeft: 5,
                  // width: '100%',
                }}>
                  {selectedOutlet ? selectedOutlet.address : ''}
                </Text>
              </View>
              <View style={{
                //justifyContent: 'center',
                alignItems: 'center',
                flexDirection: 'row',
                //width: isMobile() ? windowWidth : windowWidth * 1,
                marginTop: '3%'
              }}>
                <Call
                  width={20}
                  height={20}
                  color={'black'}
                />
                <Text style={{
                  fontFamily: 'NunitoSans-Regular',
                  color: 'black',
                  fontSize: 16,
                  //textAlign: 'center',
                  marginTop: 0,
                  marginLeft: 5,
                  // width: '100%',
                }}>
                  {selectedOutlet ? selectedOutlet.phone : ''}
                </Text>
              </View>

            </View>
          </View>
        </View>
      </Modal>
      <Modal
        visible={displayWaitlist}
        transparent={true}
        supportedOrientations={['portrait', 'landscape']}
        animationType="fade"
        onRequestClose={() => { setDisplayWaitlist(false) }}
      >
        <View style={{
          backgroundColor: 'rgba(0,0,0,0.5)',
          height: windowHeight,
          justifyContent: 'center',
        }}>
          <View style={{
            alignSelf: 'center',
            // width: 800,
            width: windowWidth * 1,
            height: windowHeight * 0.9,
            backgroundColor: 'white',

            borderRadius: 10,
            borderColor: '#E5E5E5',

            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: 0.22,
            shadowRadius: 3.22,
            elevation: 1,
            zIndex: -1,

            marginTop: 40,
          }}>
            <TouchableOpacity
              onPress={() => { setDisplayWaitlist(false) }}
            >
              <View style={{
                marginTop: 10,
                marginRight: windowWidth < 750 ? windowWidth * 0.07 : windowWidth * 0.01,
                alignSelf: 'flex-end',
                height: 20,
              }}>
                <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
              </View>
            </TouchableOpacity>
            <Text style={{
              fontFamily: 'NunitoSans-Bold',
              color: 'Black',
              fontSize: 16,
              textAlign: 'center',
            }}>
              Don't see what you're looking for?
            </Text>
            <Text style={{
              fontFamily: 'NunitoSans-Regular',
              color: 'grey',
              fontSize: 16,
              textAlign: 'center',
              marginTop: 5
            }}>
              Choose a preferred time frame, and we'll contact <br />
              you when a table becomes available.
            </Text>
            <View style={{ flexDirection: 'row', alignSelf: 'center', marginTop: 25 }}>
              <Text style={{
                fontFamily: 'NunitoSans-Bold',
                color: 'Black',
                fontSize: 20,
                textAlign: 'center',
              }}>
                {selectedDate.format('dddd, MMMM Do YYYY')}
              </Text>
            </View>
            <View style={{
              justifyContent: 'center',
              alignContent: 'center',
              alignSelf: 'center',
              flexDirection: 'row',

              // borderWidth: 1,
              // borderRadius: 5,
              // borderColor: 'grey',

              // width: windowWidth < 750 ? windowWidth * 0.95 : 500,
              // height: 40,
              marginTop: 30,
            }}>
              <View style={{
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 1,

                borderRadius: 5,
              }}>
                <TextInput
                  style={{
                    height: 40,
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    //textAlign: 'center',
                    paddingLeft: 5,
                  }}
                  placeholder="9:00 AM"
                  placeholderTextColor="grey"
                  value={startTime}
                  onChangeText={(text) => setStartTime(text)}
                />
              </View>
              <Text style={{
                fontFamily: 'NunitoSans-Regular',
                color: 'black',
                fontSize: 16,
                textAlign: 'center',

                justifyContent: 'center',
                alignContent: 'center',

                marginTop: 9,
                marginLeft: 50,
                marginRight: 50,
              }}>
                to:
              </Text>
              <View style={{
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 1,

                borderRadius: 5,
              }}>
                {/* <Select
                                    options={timeEnd}
                                    value={value}
                                    onChange={handleChange}
                                    styles={timeSelect}
                                    placeholder="Select end time"
                                /> */}
                <TextInput
                  style={{
                    height: 40,
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    //textAlign: 'center',
                    paddingLeft: 5,
                  }}
                  placeholder="11:00 AM"
                  placeholderTextColor="grey"
                  value={endTime}
                  onChangeText={(text) => setEndTime(text)}
                />
              </View>
            </View>
            <View style={{
              justifyContent: 'center',
              alignContent: 'center',
              alignSelf: 'center',

              //borderWidth: 1,
              borderRadius: 5,
              //borderColor: 'grey',

              width: windowWidth < 750 ? windowWidth * 0.95 : 500,
              height: 40,
              backgroundColor: '#ECECEC',
              marginTop: 30,

              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 1,
            }}>
              <TextInput
                style={{
                  height: 40,
                  width: windowWidth < 750 ? windowWidth * 0.95 : 500,
                  fontFamily: 'NunitoSans-Regular',
                  fontSize: 16,
                  //textAlign: 'center',
                  paddingLeft: 5,
                }}
                placeholder="First Name"
                value={firstName}
                onChangeText={(text) => setFirstName(text)}
              />
            </View>
            <View style={{
              justifyContent: 'center',
              alignContent: 'center',
              alignSelf: 'center',

              //borderWidth: 1,
              borderRadius: 5,
              // borderColor: 'grey',

              width: windowWidth < 750 ? windowWidth * 0.95 : 500,
              height: 40,
              backgroundColor: '#ECECEC',
              marginTop: 30,

              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 1,
            }}>
              <TextInput
                style={{
                  height: 40,
                  width: windowWidth < 750 ? windowWidth * 0.95 : 500,
                  fontFamily: 'NunitoSans-Regular',
                  fontSize: 16,
                  //textAlign: 'center',
                  paddingLeft: 5,
                }}
                placeholder="Last Name"
                value={lastName}
                onChangeText={(text) => setLastName(text)}
              />
            </View>
            <View style={{
              //justifyContent: 'center',
              //alignContent: 'center',
              alignSelf: 'center',
              flexDirection: 'row',

              //borderWidth: 1,
              borderRadius: 5,
              //borderColor: 'grey',

              width: windowWidth < 750 ? windowWidth * 0.95 : 500,
              height: 40,
              backgroundColor: '#ECECEC',
              marginTop: 30,
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 1,
            }}>
              {/* <SelectLoadable fallback={<></>}>
                {({ default: Select }) =>
                  <Select
                    options={country}
                    styles={countrySelect}
                    placeholder="+60"
                    isDisabled={true}
                  />
                }
              </SelectLoadable> */}

              <Select
                options={country}
                styles={countrySelect}
                placeholder="+60"
                isDisabled={true}
              />

              <TextInput
                style={{
                  height: 40,
                  width: windowWidth < 750 ? windowWidth * 0.95 : 500,
                  fontFamily: 'NunitoSans-Regular',
                  fontSize: 16,
                  //textAlign: 'center',
                  paddingLeft: 5,
                }}
                placeholder="Phone Number"
                value={phoneNum}
                onChangeText={(text) => setPhoneNum(text)}
              />
            </View>
            <View style={{
              justifyContent: 'center',
              alignContent: 'center',
              alignSelf: 'center',

              //borderWidth: 1,
              borderRadius: 5,
              //borderColor: 'grey',

              width: windowWidth < 750 ? windowWidth * 0.95 : 500,
              height: 40,
              backgroundColor: '#ECECEC',
              marginTop: 30,

              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 1,
            }}>
              <TextInput
                style={{
                  height: 40,
                  width: windowWidth < 750 ? windowWidth * 0.95 : 500,
                  fontFamily: 'NunitoSans-Regular',
                  fontSize: 16,
                  //textAlign: 'center',
                  paddingLeft: 5,
                }}
                placeholder="Email Address"
                value={waitlistEmail}
                onChangeText={(text) => setWaitlistEmail(text)}
              />
            </View>
            <TouchableOpacity
              style={[styles.btnGreen, {
                marginTop: 30,
                width: 250,
                alignSelf: 'center',
              }]}
              onPress={() => {
                createWaitList();
              }}
            >
              <View>
                <View style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignContent: 'center',
                }}>
                  <Bell
                    width={20}
                    height={20}
                    color={Colors.whiteColor}
                  />
                  <Text style={{
                    fontSize: 18,
                    fontFamily: 'NunitoSans-Regular',
                    textAlign: 'center',
                    color: Colors.whiteColor,
                  }}>
                    Add me to the Waitlist
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
      <Modal
        visible={dineInType}
        transparent={true}
        supportedOrientations={['portrait', 'landscape']}
        animationType="fade"
        onRequestClose={() => { setDineInType(false) }}
      >
        <View style={{
          backgroundColor: 'rgba(0,0,0,0.5)',
          height: windowHeight,
          justifyContent: 'center',
        }}>
          <View style={{
            alignSelf: 'center',
            width: 500,
            height: 500,
            backgroundColor: 'white',

            borderRadius: 10,
            borderColor: '#E5E5E5',

            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: 0.22,
            shadowRadius: 3.22,
            elevation: 1,
            zIndex: -1,

            marginTop: 40,
          }}>
            <TouchableOpacity
              onPress={() => { setDineInType(false) }}
            >
              <View style={{
                marginTop: 10,
                marginRight: windowWidth < 750 ? windowWidth * 0.07 : windowWidth * 0.01,
                alignSelf: 'flex-end',
                height: 20,
              }}>
                <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
              </View>
            </TouchableOpacity>
            <Text style={{
              fontFamily: 'NunitoSans-Bold',
              color: 'Black',
              fontSize: 30,
              textAlign: 'center',
              marginTop: 30,
            }}>
              Full Dine In
            </Text>
            <Text style={{
              fontFamily: 'NunitoSans-Regular',
              color: 'Black',
              fontSize: 16,
              textAlign: 'center',
              marginHorizontal: 30,
              marginTop: 10
            }}>
              Laughter is Brightest in the place where the Food is !!! Take your time and enjoy a full dine in experience with us !
            </Text>
            <TouchableOpacity
              style={[styles.btnGreen, {
                marginTop: 20,
                width: 420,
                alignSelf: 'center',
              }]}
              onPress={() => {
                setDineInType(false);
                linkTo && linkTo(`${prefix}/reservationdetails`);
              }}
            >
              <View>
                <View style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignContent: 'center',
                }}>
                  <Text style={{
                    fontSize: 18,
                    fontFamily: 'NunitoSans-Regular',
                    textAlign: 'center',
                    color: Colors.whiteColor,
                  }}>
                    2 Hours
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
            <Text style={{
              fontFamily: 'NunitoSans-Bold',
              color: 'Black',
              fontSize: 30,
              textAlign: 'center',
              marginTop: 30,
            }}>
              Quick Drop In
            </Text>
            <Text style={{
              fontFamily: 'NunitoSans-Regular',
              color: 'Black',
              fontSize: 16,
              textAlign: 'center',
              marginHorizontal: 30,
              marginTop: 10
            }}>
              Here for a quick drink? Elevate your drinking scene with our homemade brew. Bottoms up!
            </Text>
            <TouchableOpacity
              style={[styles.btnGreen, {
                marginTop: 20,
                width: 420,
                alignSelf: 'center',
              }]}
              onPress={() => { }}
            >
              <View>
                <View style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignContent: 'center',
                }}>
                  <Text style={{
                    fontSize: 18,
                    fontFamily: 'NunitoSans-Regular',
                    textAlign: 'center',
                    color: Colors.whiteColor,
                  }}>
                    1 hour 15 minutes
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
      <Modal
        style={{ flex: 1 }}
        visible={supplementModal}
        transparent={false}
        animationType="none"
      >
        <View
          style={{
            backgroundColor: Colors.whiteColor,
            flex: 1,
            minHeight: windowHeight,
          }}
        >
          <TouchableOpacity onPress={() => {
            // setSupplementModal(false)
            TempStore.update(s => {
              s.supplementModal = false;
            });
          }}>
            <View
              style={{
                position: "absolute",
                left: 15,
                top: 17,
                index: 9000,
              }}
            >
              <AntDesign name="leftcircle" size={20} color={Colors.blackColor} />
            </View>
          </TouchableOpacity>
          <View style={{ paddingHorizontal: 25, paddingTop: 15, paddingBottom: 5, zIndex: -1 }}>
            <View style={{ justifyContent: 'center', alignItems: 'center', }}>
              <Text style={{ fontSize: 18, marginBottom: 20 }}>Try our reservation supplements</Text>
              <View style={{ marginBottom: 10, flexDirection: 'row', flexWrap: 'wrap' }}>
                <Text style={{
                  fontFamily: 'NunitoSans-Regular',
                  //color: Colors.whiteColor,
                  fontSize: 18,
                  textAlign: 'center',
                }}>
                  Reservation for <b>{selectedReservationPax}</b> people at <b>{moment(selectedReservationStartTime).format('hh:mm A')},</b>
                  <br /><b>{moment(selectedReservationStartTime).format('MMMM Do YYYY')}</b>
                  <br />in the <b>{selectedSectionName ? selectedSectionName : ''}</b> at <b>{selectedOutlet ? selectedOutlet.name : ''}</b>
                </Text>
              </View>
            </View>
            {/* <View style={{ flexDirection: 'row', marginBottom: 10, alignItems: 'center' }}>
              <Text style={{ fontSize: 20 }}>Decorations for Celebration</Text>
              <Text style={{ fontSize: 14, marginLeft: 10 }}>Optional</Text>
            </View> */}
          </View>

          <View style={{ width: '100%' }}>

            {/* category list */}
            <ScrollView
              ref={catRowScrollViewRef}
              testID="categoryList"
              showsHorizontalScrollIndicator={isMobile() ? false : true}
              alwaysBounceHorizontal={true}
              horizontal={true}
              contentContainerStyle={{
                paddingLeft: 20,
              }}
              style={[
                styles.infoTab,
                {
                  zIndex: -1,
                  ...(!isMobile() && {
                    width: windowWidth,
                  }),
                },
              ]}
            >

              {!isSwitchingOutlets &&
                filteredOutletCategories
                  .map((item, index) => {
                    return (
                      <TouchableOpacity
                        onPress={() => {

                          CommonStore.update((s) => {
                            s.selectedOutletItemCategory = item;
                          });

                          scrollToItem(index);
                        }}
                        style={{
                          justifyContent: 'center'
                        }}
                        onLayout={(event) => {
                          const { width, height, x, y, } = event.nativeEvent.layout;

                          global.categoryWidthByNameDict[item.name] = width;
                          global.categoryPosXByNameDict[item.name] = x;
                        }}

                      >
                        <View style={[styles.category, {},]} >
                          <View
                            style={{
                              borderBottomColor:
                                (selectedOutletItemCategory && selectedOutletItemCategory.name == item.name)
                                  ? Colors.primaryColor
                                  : null,
                              borderBottomWidth:
                                (selectedOutletItemCategory && selectedOutletItemCategory.name == item.name)
                                  ? 3
                                  : 0,
                            }}
                          >
                            <Text
                              testID={`categoryName-${index}`}
                              style={{
                                textTransform: "capitalize",
                                paddingVertical: 12,
                                fontFamily:
                                  selectedOutletItemCategory.name == item.name
                                    ? "NunitoSans-Bold"
                                    : "NunitoSans-Regular",
                                color:
                                  selectedOutletItemCategory.name == item.name
                                    ? Colors.primaryColor
                                    : Colors.mainTxtColor,
                                fontSize: 16,
                                textAlign: "center",
                              }}
                            >
                              {item.name}
                            </Text>
                          </View>
                        </View>
                      </TouchableOpacity>
                    );
                  })}
            </ScrollView>
          </View>

          <ScrollView style={{ paddingBottom: 50 }} ref={catScrollViewRef} onScroll={handleScroll} scrollEventThrottle={16}>
            {!isSwitchingOutlets &&
              filteredOutletCategories
                .map((cat, index) => {
                  return (
                    <View
                      onLayout={(event) => onItemLayout(index, event)}
                      style={{ alignItems: 'center', width: '100%' }}
                    >
                      <View
                        style={{
                          backgroundColor: Colors.primaryColor,
                          width: '95%',
                          flexDirection: 'column',
                          alignItems: 'center',
                          justifyContent: 'center',
                          marginTop: 10,
                          marginBottoM: 5,
                        }}>
                        <Text
                          style={{
                            textTransform: "capitalize",
                            paddingVertical: 12,
                            fontFamily: "NunitoSans-Bold",
                            color: Colors.whiteColor,
                            fontSize: 16,
                            textAlign: 'center',

                          }}
                        >
                          {cat.name.toUpperCase()}
                        </Text>
                      </View>
                      <FlatList
                        data={selectedOutletItems.filter(item => {
                          if (item.categoryId === cat.uniqueId && (item.isReservationMenu !== true)) {
                            return true;
                          }
                          else {
                            return false;
                          }
                        })
                        }
                        renderItem={renderSupplement}
                        keyExtractor={(item, index) => index}
                        contentContainerStyle={{
                          paddingBottom: 30,
                        }}
                        style={{
                          paddingHorizontal: 25,
                        }}
                      />
                    </View>
                  );
                })}

          </ScrollView>

          <View style={{ position: "absolute", bottom: 80, width: windowWidth, borderTopWidth: 2, borderColor: '#707070', opacity: 0.3 }} />
          <View style={{ position: "absolute", bottom: 0, left: 0, flexDirection: 'row', backgroundColor: Colors.whiteColor, width: windowWidth, height: 80, alignItems: 'center', paddingHorizontal: 20, paddingVertical: 7.5, }}>
            <TouchableOpacity style={{
              ...(isMobile() ? {} : {
                position: 'static',
                marginLeft: 'auto',
                marginRight: 'auto',
              })
            }} onPress={async () => {
              let isValidToProceed = false;

              if (reservationConfig && reservationConfig.minSpent !== undefined) {
                const currSpent = cartItems.reduce((accum, item) => accum + item.priceTemp, 0);

                if (currSpent >= reservationConfig.minSpent) {
                  isValidToProceed = true;
                }
                else {
                  isValidToProceed = false;
                  window.confirm(`Info\nMinimum spent to proceed is RM ${reservationConfig.minSpent.toFixed(2)}, currently is RM ${currSpent.toFixed(2)}`);
                }
              }
              else {
                isValidToProceed = true;
              }

              if (isValidToProceed) {
                const subdomain = await AsyncStorage.getItem("latestSubdomain");
                if (cartItems.length > 0) {
                  linkTo && linkTo(`${prefix}/outlet/${selectedOutlet.subdomain}/novoucher-cart`);
                } else {
                  linkTo && linkTo(`${prefix}/outlet/${subdomain}/reservation-info`);
                }

                // if (cartItems && cartItems.length > 0) {
                //   linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu`);
                //   linkTo && linkTo(`${prefix}/outlet/${subdomain}/cart`);
                // }
                // else {
                //   // linkTo && linkTo(`${prefix}/outlet/${subdomain}/reservation-info/${reservationId}`);
                //   linkTo && linkTo(`${prefix}/outlet/${subdomain}/reservation-info`);
                // }

                TempStore.update(s => {
                  s.supplementModal = false;
                });
              }
            }}>
              <View style={{ flexDirection: 'row', justifyContent: 'space-between', padding: 15, backgroundColor: Colors.primaryColor, borderRadius: 5, alignItems: 'center', width: windowWidth * 0.9 }}>
                <Text style={{ fontSize: 16, fontFamily: 'NunitoSans-Bold', color: Colors.whiteColor }}>Proceed to next page</Text>
                {/* <Text style={{ fontSize: 16, fontFamily: 'NunitoSans-Regular', marginLeft: 10, }}>(RM price)</Text> */}
                <Text style={{ fontSize: 16, fontFamily: 'NunitoSans-Regular', marginLeft: 10, color: Colors.whiteColor }}>{`${cartItems.length} ${cartItems.length > 1 ? 'items' : 'item'}`}</Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>

      </Modal>
      <Modal
        style={{ flex: 1 }}
        visible={sectionSelection}
        transparent
        animationType="none"
      >
        <TouchableOpacity
          activeOpacity={1}
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: Colors.modalBgColor,
          }}
          onPress={() => setSectionSelection(false)}
        >
          <View
            style={{
              width: windowWidth * 0.7,
              height: windowHeight * 0.3,
              backgroundColor: Colors.whiteColor,
              borderRadius: 5,
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 1,
            }}
          >
            {/* <TouchableOpacity
              disabled={isLoading}
              style={{
                position: 'absolute',
                right: windowWidth * 0.02,
                top: windowWidth * 0.02,
                elevation: 1000,
                zIndex: 1000,
              }}
              onPress={() => {
                setSectionSelection(false);
              }}
            >
              <AntDesign
                name="closecircle"
                size={25}
                color={Colors.fieldtTxtColor}
              />
            </TouchableOpacity> */}
            {outletSections.length === 0 ? (
              <View
                style={{
                  zIndex: -1,
                  alignSelf: 'center',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginTop: windowHeight * 0.1,
                }}
              >
                <ActivityIndicator
                  color={Colors.primaryColor}
                  size="large"
                />
              </View>
            ) : (
              <View
                style={{
                  justifyContent: 'center',
                }}
              >
                <ScrollView
                  showsVerticalScrollIndicator={false}
                  style={{
                    height: windowHeight * 0.3,
                  }}
                >
                  <FlatList
                    data={outletSections}
                    renderItem={renderOutletSection}
                    keyExtractor={(item, index) => index}
                    contentContainerStyle={{}}
                  />
                </ScrollView>
              </View>
            )}
          </View>
        </TouchableOpacity>
      </Modal>


      <MenuItemDetailModal />

      <View style={{
        width: isMobile() ? windowWidth : windowWidth,
        height: windowHeight,
        //backgroundColor: 'blue'
      }}>
        <View style={{
          width: isMobile() ? windowWidth : windowWidth,
          // height: 160,
          backgroundColor: Colors.darkBgColor,
          paddingHorizontal: 10,
          paddingBottom: 20,
        }}>
          <View style={{ marginTop: 10, }}>
            <Image
              source={selectedMerchant && selectedMerchant.logo !== ""
                ? { uri: selectedMerchant.logo }
                : require('../asset/image/logo.png')}
              style={{
                width: 100,
                height: 100,
                marginBottom: 0,
                marginTop: 0,
                alignSelf: "center",
              }}
              onError={(e) => console.log("Error loading image:", e.nativeEvent.error)}
            />
          </View>
          <Text style={{
            fontFamily: 'NunitoSans-SemiBold',
            color: Colors.whiteColor,
            fontSize: 25,
            textAlign: 'center',
            marginTop: 0,
            fontWeight: 400,
            // width: '100%',
          }}>
            {selectedOutlet ? selectedOutlet.name : ''}
          </Text>
          <View style={{
            justifyContent: 'center',
            alignItems: 'flex-start',
            flexDirection: 'row',
            marginTop: 10,
            //width: isMobile() ? windowWidth : windowWidth * 1,
          }}>
            <Ionicons name='location-outline' size={14} color={Colors.whiteColor} />
            {/* <Location
              width={20}
              height={20}
              color={Colors.whiteColor}
            /> */}
            <Text style={{
              fontFamily: 'NunitoSans-Regular',
              color: Colors.whiteColor,
              fontSize: 12,
              textAlign: 'center',
              marginTop: 0,
              // width: '100%',
              paddingLeft: 10,
            }}>
              {selectedOutlet ? selectedOutlet.address : ''}
            </Text>
          </View>
          <View style={{
            justifyContent: 'center',
            alignItems: 'center',
            flexDirection: 'row',
            marginTop: 15,
            //width: isMobile() ? windowWidth : windowWidth * 1,
          }}>
            <View style={{ backgroundColor: '#284a3a', paddingHorizontal: 8, paddingVertical: 6, borderRadius: 5 }}>
              <TouchableOpacity
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  flexDirection: 'row',
                  //width: isMobile() ? windowWidth : windowWidth * 1,
                }}
                onPress={() => { setDisplayDetails(true) }}
              >
                <AntDesign name="clockcircleo" size={13} color={Colors.whiteColor} />
                {/* <Clock
                  width={15}
                  height={15}
                  color={Colors.whiteColor}
                /> */}
                < Text style={{
                  fontFamily: 'NunitoSans-Regular',
                  color: Colors.whiteColor,
                  fontSize: 13,
                  // textAlign: 'center',
                  marginTop: 0,
                  marginLeft: 5,
                  // width: '100%',
                }}>
                  {/* operation time */}
                  {calculateOperationTime()}
                </Text>
              </TouchableOpacity>
            </View>
            <View style={{ backgroundColor: '#284a3a', paddingHorizontal: 8, paddingVertical: 6, borderRadius: 5, marginLeft: 10, }}>
              <TouchableOpacity>
                <View style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  flexDirection: 'row',
                  //width: isMobile() ? windowWidth : windowWidth * 1,
                }}>
                  <Call
                    width={16}
                    height={16}
                    color={Colors.whiteColor}
                  />
                  <Text style={{
                    fontFamily: 'NunitoSans-Regular',
                    color: Colors.whiteColor,
                    fontSize: 13,
                    textAlign: 'center',
                    marginTop: 0,
                    marginLeft: 5,
                    // width: '100%',
                  }}>
                    {selectedOutlet ? selectedOutlet.phone : ''}
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        <View style={{
          // justifyContent: 'center',
          // alignItems: 'center',

          width: isMobile() ? windowWidth : windowWidth,
          height: windowHeight,
        }}>
          <View style={{
            height: 50,

            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'center',

            borderWidth: 1,
            borderColor: '#E5E5E5',

            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: 0.22,
            shadowRadius: 3.22,
            elevation: 1,
            zIndex: -1,
          }}>
            <Text style={{
              fontFamily: 'NunitoSans-Bold',
              color: Colors.primaryColor,
              fontSize: 16,
              textAlign: 'center',
            }}>
              Reservations
            </Text>
          </View>
          <View style={{
            //backgroundColor: 'blue',
            //height: windowHeight * 0.3,
            justifyContent: 'space-evenly',
            alignContent: 'center',
            flexDirection: 'row',
            marginTop: 20,
            width: windowWidth,
            zIndex: 3,
          }}>
            <View style={{
              //backgroundColor: 'blue',
              //height: windowHeight * 0.3,
              //justifyContent: 'center',
              //alignContent: 'center',
              flexDirection: 'row',
              zIndex: 3,
            }}>
              <View
                style={{
                  borderRadius: 5,
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 1,
                  zIndex: 3,
                  width: windowWidth * 0.9,
                }}
              >
                <MultiSelect
                  defaultValue={paxValue}
                  singleSelect={true}
                  options={outletPax}
                  onChange={handleChange}
                  // styles={noGuestSelect}
                  className='msl-vars3'
                  placeholder={paxValue ? `${paxValue}` + ' person' : '1 person'}
                  style={{ color: 'black' }}
                />
              </View>
            </View>
          </View>
          <View style={{
            alignSelf: 'center',
            justifyContent: 'center',
            alignItems: 'center',
            marginTop: 10,
            borderWidth: 1,
            borderColor: '#9c9c9c',
            borderRadius: 5,

            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: 0.22,
            shadowRadius: 3.22,
            elevation: 1,
            width: windowWidth * 0.9,
            height: 40,
          }}>
            <DatePicker
              inputProps={{ readOnly: true }}
              selected={reservationDate}
              onChange={(date) => setReservationDate(date.getTime())}
              placeholderText="Select date"
              placeholderTextColor="black"
              minDate={new Date()}
              maxDate={moment().add(1, "month").toDate()}
              className="datepicker-input"
            />
          </View>

          {
            isConfigLoading
              ?
              <View style={{
                zIndex: -1,
                width: windowWidth * 0.9,
                // height: '100%',
                alignSelf: 'center',
                marginTop: 20,
                alignItems: 'center',
                justifyContent: 'center',

                marginTop: windowHeight * 0.1,
                // backgroundColor: 'red',
              }}>
                <ActivityIndicator color={Colors.primaryColor} size={"large"} />
              </View>
              :
              // <View style={{ zIndex: -1, width: windowWidth * 0.9, alignSelf: 'center', marginTop: 20, alignItems: 'center', justifyContent: 'center', }}>
              //   <View style={{ justifyContent: 'flex-start', alignItems: 'flex-start', alignSelf: 'flex-start', paddingLeft: 5, }}>
              //     <Text style={{ fontSize: 18, fontWeight: 400, fontFamily: 'NunitoSans-SemiBold', }}>Break Fast</Text>
              //   </View>
              //   <FlatList
              //     data={BreakFastTimeSelection}
              //     renderItem={renderTimeSlot}
              //     keyExtractor={(item, index) => String(index)}
              //     contentContainerStyle={{
              //       paddingBottom: 10,
              //       flexDirection: 'row',
              //       flexWrap: 'wrap',
              //       width: windowWidth * 0.9,
              //     }}
              //   />
              //   <View style={{ justifyContent: 'flex-start', alignItems: 'flex-start', alignSelf: 'flex-start', paddingLeft: 5, }}>
              //     <Text style={{ fontSize: 18, fontWeight: 400, fontFamily: 'NunitoSans-SemiBold', }}>Lunch</Text>
              //   </View>
              //   <FlatList
              //     data={LunchTimeSelection}
              //     renderItem={renderTimeSlot}
              //     keyExtractor={(item, index) => String(index)}
              //     contentContainerStyle={{
              //       paddingBottom: 10,
              //       flexDirection: 'row',
              //       flexWrap: 'wrap',
              //       width: windowWidth * 0.9,
              //     }}
              //   />
              //   <View style={{ justifyContent: 'flex-start', alignItems: 'flex-start', alignSelf: 'flex-start', paddingLeft: 5, }}>
              //     <Text style={{ fontSize: 18, fontWeight: 400, fontFamily: 'NunitoSans-SemiBold', }}>Dinner</Text>
              //   </View>
              //   <FlatList
              //     data={DinnerTimeSelection}
              //     renderItem={renderTimeSlot}
              //     keyExtractor={(item, index) => String(index)}
              //     contentContainerStyle={{
              //       paddingBottom: 10,
              //       flexDirection: 'row',
              //       flexWrap: 'wrap',
              //       width: windowWidth * 0.9,
              //     }}
              //   />
              // </View>
              <View style={{ zIndex: -1, width: windowWidth * 0.9, alignSelf: 'center', marginTop: 20, alignItems: 'center', justifyContent: 'center' }}>
                {console.log('Rendering time slots. reservationConfig:', reservationConfig)}
                {console.log('selectedOutlet:', selectedOutlet)}
                {console.log('timeSlotSelection:', timeSlotSelection)}
                {selectedOutlet && reservationConfig && reservationConfig.outletId === selectedOutlet.uniqueId && reservationConfig.timeSlotList
                  ?
                  <>
                    {console.log('Using reservationConfig.timeSlotList:', reservationConfig.timeSlotList)}
                    {reservationConfig.timeSlotList.map(({ section, list }) => (
                      <View key={section}>
                        <View style={{ justifyContent: 'flex-start', alignItems: 'flex-start', alignSelf: 'flex-start', paddingLeft: 5 }}>
                          <Text style={{ fontSize: 18, fontWeight: 400, fontFamily: 'NunitoSans-SemiBold' }}>{section}</Text>
                        </View>
                        <FlatList
                          data={list}
                          renderItem={renderTimeSlot}
                          keyExtractor={(item, index) => String(index)}
                          contentContainerStyle={{
                            paddingBottom: 10,
                            flexDirection: 'row',
                            flexWrap: 'wrap',
                            width: windowWidth * 0.9,
                          }}
                        />
                      </View>
                    ))}
                  </>
                  :
                  <>
                    {console.log('Using timeSlotSelection:', timeSlotSelection)}
                    {timeSlotSelection.map(({ section, list }) => (
                      <View key={section}>
                        <View style={{ justifyContent: 'flex-start', alignItems: 'flex-start', alignSelf: 'flex-start', paddingLeft: 5 }}>
                          <Text style={{ fontSize: 18, fontWeight: 400, fontFamily: 'NunitoSans-SemiBold' }}>{section}</Text>
                        </View>
                        <FlatList
                          data={list}
                          renderItem={renderTimeSlot}
                          keyExtractor={(item, index) => String(index)}
                          contentContainerStyle={{
                            paddingBottom: 10,
                            flexDirection: 'row',
                            flexWrap: 'wrap',
                            width: windowWidth * 0.9,
                          }}
                        />
                      </View>
                    ))}
                  </>
                }
              </View>
          }

          {/* <View style={{
            marginHorizontal: 20,
            flexDirection: 'row',
            alignSelf: 'center',
          }}>
            <View>
              <View style={{
                marginTop: 20,
                marginHorizontal: 20,
                flexDirection: 'row',
                // justifyContent: 'space-evenly',
                width: windowWidth * 0.9,
                zIndex: 3,

                // backgroundColor: 'red',
              }}>
                <DatePicker2
                testID='DATE'
                  getSelectedDay={selectedDay}
                  endDate={120}
                  selectDate={new Date(selectedDate)}
                  //labelFormat={"MMM"}
                  color={Colors.primaryColor}
                // style={{
                //   margin: 0,
                // }}
                />

                {/* <View style={{
                  marginLeft: 25,
                  marginTop: 40,
                }}>
                  <TouchableOpacity>
                    <Cldr
                      width={25}
                      height={25}
                      color={Colors.primaryColor}
                    />
                  </TouchableOpacity>
                </View> *
              </View>

            </View>

          </View> */}

          {/* <View style={{
            backgroundColor: Colors.descriptionColor,
            // height: windowHeight * 0.8,
            width: windowWidth <= 1586 ? windowWidth * 0.93 : windowWidth * 0.980,
            //margin: 15,
            borderRadius: 50,
            alignSelf: 'center',
            // marginLeft: 20,
            // marginRight: 40,
          }}>
            <Text style={{
              fontSize: 14,
              fontFamily: 'NunitoSans-Regular',
              padding: 40,
              textAlign: 'justify',
            }}>
              <h3>Before you reserve</h3>
              POPUP TIME: 𝐂𝐇𝐑𝐈𝐒𝐓𝐌𝐀𝐒 𝐎𝐍 𝐂𝐀𝐍𝐀𝐋 𝐒𝐓. - 𝕀𝕤 𝕓𝕒𝕔𝕜!!!!!!!
              <br /><br />
              The entire Deadfall (ground floor) has been transformed into a full on 𝑯𝒐𝒍𝒊𝒅𝒂𝒚 𝑾𝒐𝒏𝒅𝒆𝒓𝒍𝒂𝒏𝒅. A new version of the special food menu and of course your favourite holiday cocktails are back, even better than last year!!!
              <br /><br />
              Please note that the normal operations menu items are sadly not available during this time but festive variations of everyone's favourites will be available the entire month of December.
              <br /><br />
              Singapore may not have snow for you to slosh through, but we can get you sloshed through the entire season none the less!!
              <br /><br />
              We look forward to sharing this holiday season with you and yours.
              <br /><br />
              xoxoxo,
              <br /><br />
              𝓣𝓮𝓪𝓶 𝓑𝓪𝓻𝓫𝓪𝓻𝔂 𝓒𝓸𝓪𝓼𝓽
              <br /><br />

              𝐔𝐏𝐃𝐀𝐓𝐄𝐃 𝐂𝐎𝐕𝐈𝐃 𝐑𝐄𝐒𝐓𝐑𝐈𝐂𝐓𝐈𝐎𝐍𝐒
              <br /><br />
              𝕌ℕ𝕍𝔸ℂℂ𝕀ℕ𝔸𝕋𝔼𝔻 𝔾𝕌𝔼𝕊𝕋𝕊
              We are so sorry we cannot accommodate you at this moment in time. Please be safe and we hope to host you soon!!
              <br /><br />
              𝕍𝔸ℂℂ𝕀ℕ𝔸𝕋𝔼𝔻 𝔾𝕌𝔼𝕊𝕋𝕊
              Fully Vaccinated Guests can seat together up to 5pax<br />
              "Vaccination" is valid only after 2-week incubation period<br />
              All guests must show Trace Together App for verification<br />
              Trace Together Token users will need to open app as token is not valid by itself<br />
              PET: guests with negative test results may enter up until the 24hr mark of the test. If the test expires during visit we will sadly have to ask you to depart the premises
              <br /><br />
              𝐓𝐡𝐚𝐧𝐤 𝐲𝐨𝐮 𝐟𝐨𝐫 𝐲𝐨𝐮𝐫 𝐩𝐚𝐭𝐢𝐞𝐧𝐜𝐞 𝐚𝐧𝐝 𝐬𝐮𝐩𝐩𝐨𝐫𝐭. 𝐇𝐨𝐩𝐞𝐟𝐮𝐥𝐥𝐲 𝐭𝐡𝐢𝐬 𝐰𝐢𝐥𝐥 𝐩𝐚𝐬𝐬 𝐬𝐨𝐨𝐧 𝐚𝐧𝐝 𝐰𝐞 𝐜𝐚𝐧 𝐚𝐥𝐥 𝐠𝐞𝐭 𝐛𝐚𝐜𝐤 𝐭𝐨 𝐧𝐨𝐫𝐦𝐚𝐥
              <br /><br />
              𝐘𝐎𝐔 𝐖𝐈𝐋𝐋 𝐑𝐄𝐂𝐄𝐈𝐕𝐄 𝐀𝐍 𝐄𝐌𝐀𝐈𝐋 𝐂𝐎𝐍𝐅𝐈𝐑𝐌𝐀𝐓𝐈𝐎𝐍.<br />
              (𝐎𝐔𝐑 𝐓𝐄𝐀𝐌 𝐖𝐈𝐋𝐋 𝐌𝐄𝐒𝐒𝐀𝐆𝐄 𝐕𝐈𝐀 𝐖𝐇𝐀𝐓𝐒𝐀𝐏𝐏 𝐈𝐅 𝐀𝐍𝐘 𝐂𝐇𝐀𝐍𝐆𝐄𝐒)
              <br /><br />
              -For same day reservation changes please send whatsapp to +𝟞𝟝 𝟠𝟠𝟞𝟡 𝟜𝟟𝟡𝟠
              <br /><br />
              -Please note that due to circuit breaker measures, our last order will be at 10pm.
              <br /><br />
              -All reservations are subject to a strict 2 hour return time. We will endeavour to extend your reservation time and/or secure another seat in one of the two venues, however, we are unable to make promises at this time.
              <br /><br />
              We thank you for your support and understanding!
            </Text>
          </View> */}

          {/* hide old design jun 5 time picker */}
          {/* <View style={{
            margin: 15,
          }}>
            <Text style={{
              fontSize: 14,
              fontFamily: 'NunitoSans-Bold',
              marginLeft: 5,
            }}>
              <h2>Select your time</h2>
            </Text>

            <View style={{
              // width: windowWidth <= 1586 ? windowWidth * 0.98 : windowWidth,
              width: '100%',

              marginBottom: 30,
              flexDirection: 'row',
              flexWrap: 'wrap',
              //width: windowWidth <=1586 ? windowWidth * 0.93 : windowWidth * 0.980,
            }}>
              <View style={{
                //backgroundColor: 'blue',
                //height: windowHeight * 0.3,
                justifyContent: 'center',
                alignContent: 'center',
                flexDirection: 'row',
                marginTop: 20,
                width: '100%',
                zIndex: 3,
              }}>
                <View style={{
                  //backgroundColor: 'blue',
                  //height: windowHeight * 0.3,
                  //justifyContent: 'center',
                  //alignContent: 'center',
                  flexDirection: 'row',
                  zIndex: 3,
                }}>
                  <View
                    style={{
                      borderRadius: 5,
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: 3,
                    }}
                  >
                    <Select
                      menuPortalTarget={document.body}
                      options={Object.entries(resHour).map(([key, value]) => {
                        return {
                          label: value.toString(),
                          value: value.toString(),
                        };
                      })}
                      onChange={(item) => { setSelectedHour(item) }}
                      styles={timeSelect}
                      placeholder={selectedHour}
                    />
                  </View>
                </View>
                <View
                  style={{
                    marginTop: 8,
                    zIndex: 3,
                  }}
                >
                  <Text> : </Text>
                </View>
                <View
                  style={{
                    borderRadius: 5,
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                    zIndex: 3,
                  }}
                >
                  <Select
                    menuPortalTarget={document.body}
                    options={Object.entries(resMin).map(([key, value]) => {
                      return {
                        label: value.toString(),
                        value: value.toString(),
                      };
                    })}
                    onChange={(item) => { setSelectedMin(item) }}
                    styles={timeSelect}
                    placeholder={selectedMin}
                  />
                </View>
              </View>
            </View>
          </View> */}

          {/* <View style={{
            margin: 15,
          }}>
            <View style={{
              // width: windowWidth <= 1586 ? windowWidth * 0.98 : windowWidth,
              width: '100%',

              // backgroundColor: 'red',

              marginBottom: 10,
              flexDirection: 'row',
              flexWrap: 'wrap',

              zIndex: -2,

              justifyContent: 'center',
              alignItems: 'center',

              // marginTop: -50,

              //width: windowWidth <=1586 ? windowWidth * 0.93 : windowWidth * 0.980,
            }}> */}
          {/* find the item in the list and map the content */}
          {/* {reservationAvailabilityToday.map((item, index) => {
                    if (item.reservationName === reservationAvailabilityTodayNames[nameIndex]) {
                      return ( */}
          {/* <TouchableOpacity
                onPress={async () => {
                  if (selectedOutlet && selectedOutlet.uniqueId) {
                    var timeTemp1 = selectedHour + ':' + selectedMin;

                    const formattedDate = moment(selectedDate).format('DD MMM YYYY');
                    // const newRevDateTime1 = timeTemp1 + ' ' + formattedDate;

                    const parsedDateTime = moment(`${formattedDate} ${timeTemp1}`, 'DD MMM YYYY HH:mm').valueOf();

                    const subdomain = await AsyncStorage.getItem("latestSubdomain");
                    // setDineInType(true);
                    CommonStore.update((s) => {
                      // s.selectedReservationId = reservationId;
                      s.selectedReservationStartTime = parsedDateTime;
                      s.selectedReservationPax = paxValue;

                      s.selectedAddressUserPhone = userPhone;
                      s.selectedUserEmail = userEmail;
                      s.selectedUserFirstName = userFirstName;
                      s.selectedUserLastName = userLastName;
                      s.selectedUserRemarks = remarks;
                      s.selectedUserDiet = dRestrictions;
                      s.selectedUserOccasion = sOccasions;
                    });
                    //linkTo && linkTo(`${prefix}/reservationdetails`);
                    linkTo && linkTo(`${prefix}/outlet/${subdomain}/reservation-info/${reservationId}`);
                  }
                }}
                style={{
                  backgroundColor: Colors.primaryColor,
                  borderRadius: 10,
                  //height: 50,
                  justifyContent: 'center',
                  alignContent: 'center',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 3,
                  zIndex: -1,
                  margin: 5,
                  // width: windowWidth <= 1586 ? windowWidth * 0.225 : windowWidth * 0.24,
                  padding: 28,
                  paddingVertical: 12,
                }}>
                <View>
                  <Text style={{
                    fontSize: 18,
                    fontFamily: 'NunitoSans-Regular',
                    textAlign: 'center',
                    color: Colors.whiteColor,
                  }}>
                    CONTINUE
                  </Text>
                </View>
              </TouchableOpacity> */}
          {/* </View>
          </View> */}

          {/* <View style={{
            // backgroundColor: 'green',
            // height: windowHeight * 0.8,
            margin: 15,
            marginTop: -30,
          }}>
            <Text style={{
              fontSize: 14,
              fontFamily: 'NunitoSans-Bold',
              marginLeft: 5,
            }}>
              <h2>Waitlist</h2>
            </Text>
            <Text style={{
              fontSize: 14,
              fontFamily: 'NunitoSans-Regular',
              marginLeft: 5,
              marginTop: -20,
            }}>
              Don't see the time you're looking for? Add yourself to the waitlist and we'll let you know if a table opens up
            </Text>
            <TouchableOpacity
              style={[styles.btnWhite, {
                margin: 5,
                // width: windowWidth * 0.225,

                paddingLeft: 10,
                paddingRight: 10,
              }]}
              onPress={() => { setDisplayWaitlist(true) }}
            >
              <View>
                <View style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignContent: 'center',
                }}>
                  <Bell
                    width={20}
                    height={20}
                    color={Colors.primaryColor}
                  />
                  <Text style={{
                    fontSize: 18,
                    fontFamily: 'NunitoSans-Regular',
                    textAlign: 'center',
                    color: Colors.primaryColor,
                  }}>
                    Waitlist
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
          </View> */}

          {/* <View style={{
            // backgroundColor: 'green',
            // height: windowHeight * 0.8,
            margin: 15,
            //marginTop: -30,
          }}>
            <Text style={{
              fontSize: 14,
              fontFamily: 'NunitoSans-Bold',
              marginLeft: 5,
            }}>
              <h2>Private Events</h2>
            </Text>
            <Text style={{
              fontSize: 14,
              fontFamily: 'NunitoSans-Regular',
              marginLeft: 5,
              marginTop: -20,
            }}>
              Want to celebrate a birthday, anniversary, or a private event? Let us know here and we’ll get in touch to discuss the details
            </Text>
            <TouchableOpacity
            testID='REQUEST'
              style={[styles.btnGreen, {
                margin: 5,
                // width: windowWidth * 0.225,
                paddingLeft: 10,
                paddingRight: 10,
              }]}
              onPress={() => { setDisplayDetails(true) }}
            >
              <View>
                <View style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignContent: 'center',
                }}>
                  <Text style={{
                    fontSize: 18,
                    fontFamily: 'NunitoSans-Regular',
                    textAlign: 'center',
                    color: Colors.whiteColor,
                  }}>
                    Request Information
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
            <View style={{ margin: 10 }} />
          </View> */}

        </View>
      </View>
    </ScrollView >
  );
};

const styles = StyleSheet.create({
  btnWhite: {
    borderColor: Colors.primaryColor,
    borderWidth: 1,
    borderRadius: 5,
    height: 50,
    justifyContent: 'center',
    alignContent: 'center',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
    zIndex: -1,
  },
  btnGreen: {
    backgroundColor: Colors.primaryColor,
    borderRadius: 5,
    height: 50,
    justifyContent: 'center',
    alignContent: 'center',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
    zIndex: -1,
  },
  daysWrd: {
    fontSize: 16,
    fontFamily: 'NunitoSans-Regular',
    textAlign: 'center',
  },
  dateWrd: {
    fontSize: 18,
    fontFamily: 'NunitoSans-Regular',
    textAlign: 'center',
    color: Colors.primaryColor,
  },
  category: {
    paddingHorizontal: 10,
    justifyContent: "center",
    alignItems: "center",
  },
  infoTab: {
    backgroundColor: Colors.fieldtBgColor,
  },
})

export default ReservationScreen;

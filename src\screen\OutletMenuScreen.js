import React, { Component, useState, useEffect, useCallback, useRef } from "react";
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  TouchableOpacity,
  Alert,
  Dimensions,
  Modal,
  ActivityIndicator,
  FlatList,
  BackHandler,
  useWindowDimensions,
  InteractionManager,
  Animated,
  Easing,
  Linking,
} from "react-native";
// import { useBackButton } from '@react-navigation/core';
import Colors from "../constant/Colors";
import Styles from "../constant/Styles";
import ApiClient from "../util/ApiClient";
import API from "../constant/API";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import Entypo from "react-native-vector-icons/Entypo";
import Ionicons from "react-native-vector-icons/Ionicons";
import FontAwesome from "react-native-vector-icons/FontAwesome";
import MaterialIcons from "react-native-vector-icons/MaterialIcons";
import { ReactComponent as ArrowForward } from "../asset/svg/Arrow-forward-ios.svg";
import { ReactComponent as ArrowBack } from "../asset/svg/Arrow-back-ios.svg";
import * as Cart from "../util/Cart";
import Back from "react-native-vector-icons/EvilIcons";
// import { FlatList } from 'react-native-gesture-handler';
// import StickyParallaxHeader from 'react-native-sticky-parallax-header'
import AntDesign from "react-native-vector-icons/AntDesign";
import { CommonStore } from "../store/commonStore";
import {
  APP_TYPE,
  CHARGES_TYPE,
  ORDER_TYPE,
  PRODUCT_PRICE_TYPE,
  UNIT_TYPE_SHORT,
  UPSELLING_SECTION,
  UPSELL_BY_TYPE,
  USER_ORDER_STATUS,
} from "../constant/common";
import { UserStore } from "../store/userStore";

//import { useRef } from 'react';

import AsyncStorage from "@react-native-async-storage/async-storage";
import AsyncImage from "../components/asyncImage";
import { prefix } from "../constant/env";
import { useFocusEffect, useLinkTo } from "@react-navigation/native";
import { DataStore } from "../store/dataStore";
import { checkIfVisibleItem, getClaimableVoucher, isMobile, lazyRetry, signInWithPhoneForCRMUser } from "../util/commonFuncs";
import { TempStore } from "../store/tempStore";
import moment from "moment";
import { Platform, TextInput } from "react-native-web";
// import firebase from "firebase/app";
import { getFirestore, collection, query, where, getDocs, limit, deleteDoc, doc, orderBy } from "firebase/firestore";
import { logEvent } from "firebase/analytics";
import { signInAnonymously, signInWithEmailAndPassword } from "firebase/auth";
import { Collections } from "../constant/firebase";
import Feather from 'react-native-vector-icons/Feather';
import RecommendedItems from "../components/recommendedItems";
import { TableStore } from "../store/tableStore";
// import MenuItemDetailModal from "../components/menuitemdetailsmodal";

import imgLogo from "../asset/image/logo.png";

import loadable from '@loadable/component';
import { PaymentStore } from "../store/paymentStore";

import LinearGradient from 'react-native-web-linear-gradient';

import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import { ANALYTICS, ANALYTICS_PARSED } from "../constant/analytics";
import { PROMOTION_TYPE_VARIATION } from '../constant/promotions';
import { LOYALTY_CAMPAIGN_TYPE } from "../constant/loyalty";
import RedeemVoucherModal from "../components/redeemVoucherModal";
import html2canvas from "html2canvas";
import { idbDel, idbGet, safelyExecuteIdb } from "../util/db";
import Toastify from 'toastify-js';
import { CheckBox } from "react-native-web";
// import Switch from 'react-switch'
import VoucherPromotionItemsRedeem from "../components/voucherPromotionItemsRedeem";
// import { jsPDF } from "jspdf";

const MenuItemDetailModal = loadable(() => lazyRetry(() => import("../components/menuitemdetailsmodal")));
const RewardsModal = loadable(() => lazyRetry(() => import("../components/rewardsModal")));
const RewardsModalEU = loadable(() => lazyRetry(() => import("../components/rewardsModalEU")));
const VoucherBundle = loadable(() => lazyRetry(() => import("../components/voucherBundle")));

/**
 * OutletMenuScreenr
 * function
 * *display list of menu for purchase
 * *access to cart screen
 *
 * route.params
 * *outletData: array of data of the current outlet
 * *orderType: type of order being made (takeaway, pick up, dine in)
 * *test: ???
 * *navFrom: the screen stack this route is on
 */

let startX;
let startY;

global.isTestingMode = false;
global.debugText = '';

global.rotateScrollSpinnerTimeoutId = null;

global.selectedOutletItemCategoryTimeoutId = null;

global.canSwipeItemList = true;
global.canSwitchCategory = true;

global.categoryWidthByNameDict = {};
global.categoryPosXByIdDict = {};

global.categoryPosYByIdDict = {};

global.pageYOffSetPrev = 0;

const OutletMenuScreen = (props) => {
  const { navigation, route } = props;

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();
  const linkTo = useLinkTo();

  const tickboxConsent = TempStore.useState(s => s.tickboxConsent);

  // const { orderType } = route.params;
  // const outletDataParam = route.params.outletData;
  // const testParam = route.params.test;
  // const navFromParam = route.params.navFrom;

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        style={{
          opacity: 1,
          display: 'none',
        }}
        onPress={() => {
          linkTo && linkTo(`${prefix}/outlet/${selectedOutlet.subdomain}/`);
        }}
      >
        <View
          style={{
            marginLeft: 10,
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "flex-start",
          }}
        >
          <Ionicons
            name="chevron-back"
            size={26}
            color={Colors.fieldtTxtColor}
            style={{}}
          />

          <Text
            style={{
              color: Colors.fieldtTxtColor,
              fontSize: 16,
              textAlign: "center",
              fontFamily: "NunitoSans-Regular",
              lineHeight: 22,
              marginTop: 1,
            }}
          >
            Back
          </Text>
        </View>
      </TouchableOpacity>
    ),
    headerRight: () => (
      <View style={{ flexDirection: "row" }}>
        {/* <TouchableOpacity
          onPress={() => {
            // props.navigation.navigate("Profile");
            linkTo && linkTo(`${prefix}/outlet/order-details`);
          }}
          style={{
            opacity: 0,
          }}
        >
          <View style={{ marginRight: 15 }}>
            <Text
              style={{
                fontSize: 20,
                lineHeight: 25,
                textAlign: "center",
                fontFamily: "NunitoSans-Bold",
                color: Colors.mainTxtColor,
              }}
            >
              Reservation
            </Text>
          </View>
        </TouchableOpacity> */}

        {/* <TouchableOpacity
          onPress={() => {
            linkTo && linkTo(`${prefix}/outlet/voucher-list`);
          }}

          style={{}}>
          <View style={{ marginRight: 25 }}>
            <Text
              style={{
                fontSize: 20,
                lineHeight: 25,
                textAlign: "center",
                fontFamily: "NunitoSans-Bold",
                color: Colors.mainTxtColor,
              }}
            >
              Vouchers
            </Text>
          </View>
        </TouchableOpacity> */}

        {/* {orderType !== ORDER_TYPE.DINEIN ? (
          <TouchableOpacity
            onPress={() => {
              // props.navigation.navigate("Profile");
              if (orderType === "DELIVERY") {
                linkTo &&
                  linkTo(
                    `${prefix}/outlet/${selectedOutlet.subdomain}/takeaway`
                  );
              } else {
                linkTo &&
                  linkTo(
                    `${prefix}/outlet/${selectedOutlet.subdomain}/delivery`
                  );
              }
            }}
            style={{
              opacity: 1,
            }}
          >
            <View style={{ marginRight: 15 }}>
              <Text
                style={{
                  fontSize: 20,
                  lineHeight: 25,
                  textAlign: "center",
                  fontFamily: "NunitoSans-Bold",
                  color: Colors.mainTxtColor,
                }}
              >
                {orderType === ORDER_TYPE.DELIVERY ? "Pick Up" : "Delivery"}
              </Text>
            </View>
          </TouchableOpacity>
        ) : (
          <></>
        )} */}

        {(selectedOutlet && true) ? (
          // true
          <TouchableOpacity
            testID="notificationBell"
            onPress={async () => {
              // if (selectedTableOrders.length > 0) {
              if (orderType === ORDER_TYPE.DINEIN &&
                userOrdersDineInActions.length > 0) {
                const latestSubdomain = await AsyncStorage.getItem(
                  "latestSubdomain"
                );

                if (latestSubdomain && prefix) {
                  linkTo && linkTo(`${prefix}/outlet/${latestSubdomain}/order-notification`);

                  // linkTo &&
                  //   linkTo(`${prefix}/outlet/${latestSubdomain}/order-history`);
                }
              }
              else if (orderType === ORDER_TYPE.PICKUP &&
                userOrdersTakeawayActions.length > 0) {
                const latestSubdomain = await AsyncStorage.getItem(
                  "latestSubdomain"
                );

                if (latestSubdomain && prefix) {
                  linkTo &&
                    linkTo(`${prefix}/outlet/${latestSubdomain}/order-notification`);
                }
              }
              else {
                alert("No notifications for now.");
              }
            }}
            style={{
              opacity: 1,
            }}
          >
            <View style={{
              marginRight: 0,
              // width: 60,
              // height: 60,              
            }}>
              <View style={{
                position: 'relative',
              }}>
                <Ionicons
                  name={"notifications-outline"}
                  size={isMobile() ? 22 : 25}
                  color={Colors.primaryColor}
                  style={{
                    // alignSelf: "center",
                    marginRight: isMobile() ? 10 : 15,
                    marginTop: 1,
                  }}
                />
                <View style={[styles.cartCount, {
                  right: 2,
                }]}>
                  <Text style={{
                    color: Colors.whiteColor,
                    fontSize: 12,
                    fontFamily: "NunitoSans-Bold"
                  }}>
                    {
                      orderType === ORDER_TYPE.DINEIN
                        ?
                        userOrdersDineInActions.length
                        :
                        userOrdersTakeawayActions.length
                    }
                  </Text>
                </View>
              </View>
            </View>
          </TouchableOpacity>
        ) : (
          <></>
        )}

        {orderType === ORDER_TYPE.DINEIN ? (
          // true
          <TouchableOpacity
            testID="cartIconTop"
            onPress={async () => {
              if (selectedTableOrders.length > 0) {
                const latestSubdomain = await AsyncStorage.getItem(
                  "latestSubdomain"
                );

                if (latestSubdomain) {
                  linkTo &&
                    linkTo(`${prefix}/outlet/${latestSubdomain}/order-history`);
                }
              } else {
                alert("No orders placed for now.");
              }
            }}
            style={{
              opacity: 1,
            }}
          >
            <View style={{
              marginRight: 15,
              // width: 60,
              // height: 60,              
            }}>
              <View style={{
                position: 'relative',
              }}>
                <Ionicons
                  name={"receipt-outline"}
                  size={isMobile() ? 22 : 25}
                  color={Colors.primaryColor}
                  style={{
                    // alignSelf: "center",
                    marginRight: 15,
                    top: 1,
                    position: 'relative',
                  }}
                />
                <View style={styles.cartCount}>
                  <Text style={{
                    color: Colors.whiteColor,
                    fontSize: 12,
                    fontFamily: "NunitoSans-Bold"
                  }}>
                    {selectedTableOrders.filter(order => {
                      let validStatus = false;

                      // if (
                      //     order.userIdAnonymous === userIdAnonymous ||
                      //     order.userIdAnonymous === '' ||
                      //     order.userIdAnonymous === undefined
                      // ) {
                      //     validStatus = true;
                      // }

                      validStatus = true;

                      // if (order.userPhone) {
                      //   if (order.userPhone !== userNumber) {
                      //     // means should be other people orders

                      //     if (
                      //       order.taggableVoucherId ||
                      //       (order.promotionIdList &&
                      //         order.promotionIdList.length > 0)
                      //       ||
                      //       (order.promoCodePromotionIdList &&
                      //         order.promoCodePromotionIdList.length > 0)
                      //       ||
                      //       (order.cartPromotionIdList &&
                      //         order.cartPromotionIdList.length > 0)
                      //     ) {
                      //       // means is promotional order, needs separated

                      //       validStatus = false;

                      //       // selectedTableOtherPeoplePromoVoucherOrdersTemp.push(order);
                      //     }
                      //   }
                      // }
                      // else if (selectedOutlet.dineInRequiredAuthorization &&
                      //   order.orderType === ORDER_TYPE.DINEIN &&
                      //   order.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED) {
                      //   // means need wait merchant approved first, then only can show

                      //   validStatus = false;

                      //   // selectedTablePendingApprovalOrdersTemp.push(order);
                      // }

                      return validStatus;
                    }).length}
                  </Text>
                </View>
              </View>
            </View>
          </TouchableOpacity>
        ) : (
          <></>
        )}

        {orderType === ORDER_TYPE.PICKUP ? (
          // true
          <TouchableOpacity
            testID="cartIconTop"
            onPress={async () => {
              // if (selectedTableOrders.length > 0) {
              if (global.takeawayOrders.length > 0 ||
                selectedUserOrderAnonymousTakeaway.length > 0) {
                const latestSubdomain = await AsyncStorage.getItem(
                  "latestSubdomain"
                );

                if (latestSubdomain) {
                  linkTo &&
                    linkTo(`${prefix}/outlet/${latestSubdomain}/order-history-t`);
                }
              } else {
                alert("No orders placed for now.");
              }
            }}
            style={{
              opacity: 1,
            }}
          >
            <View style={{
              marginRight: 15,
              // width: 60,
              // height: 60,              
            }}>
              <View style={{
                position: 'relative',
              }}>
                <Ionicons
                  name={"receipt-outline"}
                  size={isMobile() ? 22 : 25}
                  color={Colors.primaryColor}
                  style={{
                    // alignSelf: "center",
                    marginRight: 15,
                    top: 1,
                    position: 'relative',
                  }}
                />
                <View style={styles.cartCount}>
                  <Text style={{
                    color: Colors.whiteColor,
                    fontSize: 12,
                    fontFamily: "NunitoSans-Bold"
                  }}>
                    {selectedUserOrderAnonymousTakeaway.length}
                  </Text>
                </View>
              </View>
            </View>

            {/* <View style={{ marginRight: 15 }}>
              {isMobile() ? (
                <Ionicons
                  name={"restaurant-outline"}
                  size={20}
                  color={Colors.primaryColor}
                  style={{
                    alignSelf: "center",
                    marginRight: 15,
                  }}
                />
              ) : (
                <Text
                  style={{
                    fontSize: 20,
                    lineHeight: 25,
                    textAlign: "center",
                    fontFamily: "NunitoSans-Bold",
                    color: Colors.mainTxtColor,
                  }}
                >
                  {"View Orders"}
                </Text>
              )}
            </View> */}
          </TouchableOpacity>
        ) : (
          <></>
        )}
      </View>
    ),
    headerTitle: () => (
      <View
        style={{
          justifyContent: "center",
          alignItems: "center",
          bottom: -1,
        }}
      >
        <Text
          style={{
            fontSize: 20,
            lineHeight: 25,
            textAlign: "center",
            fontFamily: "NunitoSans-Bold",
            color: Colors.mainTxtColor,
          }}
        >
          {/* {orderType == ORDER_TYPE.DINEIN ? "Dine In" : orderType == ORDER_TYPE.DELIVERY ? "Delivery" : "Takeaway"} */}
          {selectedOutlet
            ? isMobile()
              ? trimmedOutletName
              : selectedOutlet.name
            : ""}
        </Text>
      </View>
    ),
    headerTintColor: Colors.blackColor,
  });

  const [rotateScrollSpinnerStyle, setRotateScrollSpinnerStyle] = useState(0);
  const [opacityItemListStyle, setOpacityItemListStyle] = useState(1);
  const [opacityTransitionItemListStyle, setOpacityTransitionItemListStyle] = useState('opacity 0.1s');
  const [debugText, setDebugText] = useState('');

  // const [outletData, setOutletData] = useState(outletDataParam);
  const [outletMenu, setOutletMenu] = useState([]);
  const [category, setCategory] = useState("");
  const [menu, setMenu] = useState([]);
  const [cartIcon, setCartIcon] = useState(false);
  const [reverse, setReverse] = useState(false);
  const [cartItem, setCartItem] = useState([]);
  const [categoryOutlet, setCategoryOutlet] = useState([]);
  // const [test, setTest] = useState(testParam);
  const [currentMenu, setCurrentMenu] = useState([]);
  const [productList2, setProductList2] = useState([]);
  const [productList, setProductList] = useState([]);
  const [choice, setChoice] = useState(null);
  const [categoryIndex, setCategoryIndex] = useState(0);
  // const [navFrom, setNavFrom] = useState(navFromParam);
  const [isInfoTabHitTop, setIsInfoTabHitTop] = useState(false);
  const [onStartVisible, setOnStartVisible] = useState(false);
  const [cartWarning, setCartWarning] = useState(false);
  const [cartProceed, setCartProceed] = useState([]);

  const [tempItem, setTempItem] = useState({});

  const [selectedOutletItems, setSelectedOutletItems] = useState([]);

  const selectedOutletItemsRaw = CommonStore.useState(
    (s) => s.selectedOutletItems.filter(
      (item) =>
        item.priceType === undefined ||
        item.priceType === PRODUCT_PRICE_TYPE.FIXED ||
        item.priceType === PRODUCT_PRICE_TYPE.UNIT
    )
  );
  const selectedOutletItemCategories = CommonStore.useState(
    (s) => s.selectedOutletItemCategories
  );
  const selectedOutletItemCategory = CommonStore.useState(
    (s) => s.selectedOutletItemCategory
  );

  const selectedOutletItemCategoriesDict = CommonStore.useState(
    (s) => s.selectedOutletItemCategoriesDict
  );

  const selectedOutletSectionId = CommonStore.useState(
    (s) => s.selectedOutletSectionId
  );

  ///////////////////////////////////////////

  // 2024-02-28 - loyalty voucher support

  const selectedOutletTaggableVouchersAll = CommonStore.useState(
    (s) => s.selectedOutletTaggableVouchersAll
  );

  const availableLoyaltyCampaigns = CommonStore.useState(
    (s) => s.availableLoyaltyCampaigns
  );

  const anonymousDt = UserStore.useState(
    (s) => s.anonymousDt
  );

  const userEmail = UserStore.useState(
    (s) => s.email
  );

  ///////////////////////////////////////////

  const isSwitchingOutlets = CommonStore.useState((s) => s.isSwitchingOutlets);

  const cartItems = CommonStore.useState((s) => s.cartItems);
  const orderType = CommonStore.useState((s) => s.orderType);

  const selectedOutlet = CommonStore.useState((s) => s.selectedOutlet);
  const cartOutletId = CommonStore.useState((s) => s.cartOutletId);

  const firebaseUid = UserStore.useState((s) => s.firebaseUid);
  const email = UserStore.useState((s) => s.email);
  const userName = UserStore.useState((s) => s.name);
  const userNumber = UserStore.useState((s) => s.number);

  const scannedQrData = CommonStore.useState((s) => s.scannedQrData);

  const userCart = CommonStore.useState((s) => s.userCart);

  const [outletItemsDisplay, setOutletItemsDisplay] = useState([]);

  const [effectiveDays, setEffectiveDays] = useState(moment().day());

  const overrideItemPriceSkuDict = CommonStore.useState(
    (s) => s.overrideItemPriceSkuDict
  );
  const amountOffItemSkuDict = CommonStore.useState(
    (s) => s.amountOffItemSkuDict
  );
  const percentageOffItemSkuDict = CommonStore.useState(
    (s) => s.percentageOffItemSkuDict
  );
  const buy1Free1ItemSkuDict = CommonStore.useState(
    (s) => s.buy1Free1ItemSkuDict
  );

  const overrideCategoryPriceNameDict = CommonStore.useState(
    (s) => s.overrideCategoryPriceNameDict
  );
  const amountOffCategoryNameDict = CommonStore.useState(
    (s) => s.amountOffCategoryNameDict
  );
  const percentageOffCategoryNameDict = CommonStore.useState(
    (s) => s.percentageOffCategoryNameDict
  );
  const buy1Free1CategoryNameDict = CommonStore.useState(
    (s) => s.buy1Free1CategoryNameDict
  );

  const pointsRedeemItemSkuDict = CommonStore.useState(
    (s) => s.pointsRedeemItemSkuDict
  );
  const pointsRedeemCategoryNameDict = CommonStore.useState(
    (s) => s.pointsRedeemCategoryNameDict
  );

  const linkToFunc = DataStore.useState((s) => s.linkToFunc);

  /////////////////////////////////////////////////////////////////////////

  const selectedTableOrders = CommonStore.useState(
    (s) => s.selectedTableOrders
  );

  const isPlacingReservation = CommonStore.useState(
    (s) => s.isPlacingReservation
  );

  const isDepositOnly = CommonStore.useState((s) => s.isDepositOnly);

  const [trimmedOutletName, setTrimmedOutletName] = useState("");

  /////////////////////////////////////////////////////////////////////////

  // 2022-06-22 Sortable outlet categories

  const [filteredOutletCategories, setFilteredOutletCategories] = useState([]);

  /////////////////////////////////////////////////////////////////////////

  // 2022-08-04 - Add login support

  const [showLoginModal, setShowLoginModal] = useState(false);
  const [inputEmail, setInputEmail] = useState("");
  const [inputPassword, setInputPassword] = useState("");

  // const [showLoginPhoneModal, setShowLoginPhoneModal] = useState(false);
  const [inputName, setInputName] = useState("");
  const [inputPhone, setInputPhone] = useState("");

  const isLoading = CommonStore.useState((s) => s.isLoading);

  /////////////////////////////////////////////////////////////////////////

  const showLoginPhoneModal = TempStore.useState((s) => s.showLoginPhoneModal);
  const fromApplyVoucherButton = TempStore.useState((s) => s.fromApplyVoucherButton);

  const selectedUserOrderAnonymousTakeaway = CommonStore.useState((s) => s.selectedUserOrderAnonymousTakeaway);

  /////////////////////////////////////////////////////////////////////////

  const userOrders = CommonStore.useState((s) => s.userOrders);
  const [activeDineInOrders, setActiveDineInOrders] = useState([]);

  /////////////////////////////////////////////////////////////////////////

  const [userOrdersTakeaway, setUserOrdersTakeaway] = useState([]);
  const [userOrdersDineIn, setUserOrdersDineIn] = useState([]);

  const [userOrdersTakeawayActions, setUserOrdersTakeawayActions] = useState([]);
  const [userOrdersDineInActions, setUserOrdersDineInActions] = useState([]);

  const userIdAnonymous = UserStore.useState((s) => s.userIdAnonymous);

  const availablePromotions = CommonStore.useState(s => s.availablePromotions);
  const selectedOutletCover = selectedOutlet && selectedOutlet.cover ? selectedOutlet.cover : "";
  const [images, setImages] = useState([selectedOutletCover]);
  const [promoName, setPromoName] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [imageOpacity, setImageOpacity] = useState(new Animated.Value(1));

  /////////////////////////////////////////////////////////////////////////////////////////////////////

  // 2024-10-15 - topup credit with free voucher features changes

  useEffect(() => {

  }, []);

  /////////////////////////////////////////////////////////////////////////////////////////////////////

  useEffect(() => {
    if (availablePromotions && availablePromotions.length > 0) {
      const promotionImages = availablePromotions.map(promotion => promotion.image);
      const allImages = [selectedOutletCover, ...promotionImages.filter(image => image !== selectedOutletCover)];
      setImages(allImages);

      const promotionName = availablePromotions.map(promo => promo.campaignName);
      setPromoName(promotionName);
    }
  }, [availablePromotions]);

  useEffect(() => {
    const interval = setInterval(() => {
      Animated.timing(imageOpacity, {
        toValue: 0,
        duration: 1000,
        useNativeDriver: true,
      }).start(() => {
        setCurrentIndex(prevIndex => (prevIndex + 1) % images.length);
        Animated.timing(imageOpacity, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }).start();
      });
    }, 5000);

    return () => clearInterval(interval);
  }, [currentIndex, images, imageOpacity]);

  // const slide = (shift) => {
  //   scrl.current.scrollLeft += shift;
  // };


  /////////////////////////////////////////////////////////////////////////

  // const showGreetingPopup = TempStore.useState(
  //   (s) => s.showGreetingPopup
  // );

  /////////////////////////////////////////////////////////////////////////

  // useEffect(() => {
  //   if (global.subdomain === 'hominsan-ss15' ||
  //     global.subdomain === 'hominsanttdi' ||
  //     global.subdomain === 'kenneth-cafe') {
  //     TempStore.update(s => {
  //       s.showGreetingPopup = true;
  //     });
  //   }
  // }, []);

  /////////////////////////////////////////////////////////////////////////

  // const upsellingCampaignsAfterCart = DataStore.useState(s => s.upsellingCampaignsAfterCart);

  const availableUpsellingCampaigns = CommonStore.useState(s => s.availableUpsellingCampaigns);

  const currCrmUser = CommonStore.useState(s => s.currCrmUser);
  const selectedOutletCRMTagsDict = CommonStore.useState(s => s.selectedOutletCRMTagsDict);

  /////////////////////////////////////////////////////////////////////////

  // upselling after cart

  useEffect(() => {
    var upsellingCampaignsAfterCheckoutTemp = [];
    var upsellingCampaignsAfterCheckoutRecommendationTemp = [];
    var toRecommendedItemsTemp = [];
    const availableUpsellingCampaignsFiltered = availableUpsellingCampaigns.filter(campaign =>
      campaign.upsellingSection === UPSELLING_SECTION.AFTER_CHECKOUT || campaign.upsellingSection === UPSELLING_SECTION.AFTER_CHECKOUT_RECOMMENDATION);

    var isSameOutlet = true;
    if (selectedOutlet && selectedOutlet.uniqueId) {
      availableUpsellingCampaignsFiltered.map(campaign => {
        if (campaign.outletId !== selectedOutlet.uniqueId) {
          isSameOutlet = false;
        }
      })
    }
    else {
      isSameOutlet = false;
    }

    if (isSameOutlet && cartItems && cartItems.length > 0 && availableUpsellingCampaignsFiltered.length > 0) {
      for (var campaignIndex = 0; campaignIndex < availableUpsellingCampaignsFiltered.length; campaignIndex++) {
        var isValidCampaign = false;

        const upsellingCampaign = availableUpsellingCampaignsFiltered[campaignIndex];

        // setCurrUpsellingCampaign(currUpsellingCampaign);

        const upsellingProductList = upsellingCampaign.productList;
        const upsellingProductIdList = upsellingCampaign.productList.map(product => product.productId);

        for (var upsellingIndex = 0; upsellingIndex < upsellingProductIdList.length; upsellingIndex++) {
          for (var i = 0; i < selectedOutletItemsRaw.length; i++) {
            if (upsellingProductIdList[upsellingIndex] === selectedOutletItemsRaw[i].uniqueId) {
              if (checkIfVisibleItem(selectedOutletItemsRaw[i])) {
                const upsellingItem = upsellingProductList.find(product => product.productId === selectedOutletItemsRaw[i].uniqueId);
                const priceUpselling = upsellingItem.upsellPrice;

                var isValidOrderType = selectedOutletItemsRaw[i].hideInOrderTypes && selectedOutletItemsRaw[i].hideInOrderTypes.length > 0
                  ? (selectedOutletItemsRaw[i].hideInOrderTypes.includes(orderType)
                    ? false
                    : true)
                  : true;

                var isValidActive = (
                  selectedOutletItemsRaw[i].isActive &&
                  (selectedOutletItemsRaw[i].isAvailableDayActive
                    ? (selectedOutletItemsRaw[i].effectiveTypeOptions.includes(effectiveDays) &&
                      selectedOutletItemsRaw[i].effectiveStartTime && selectedOutletItemsRaw[i].effectiveEndTime &&
                      moment().isSameOrAfter(
                        moment(selectedOutletItemsRaw[i].effectiveStartTime)
                          .year(moment().year())
                          .month(moment().month())
                          .date(moment().date())
                      )
                      &&
                      moment().isBefore
                        (moment(selectedOutletItemsRaw[i].effectiveEndTime)
                          .year(moment().year())
                          .month(moment().month())
                          .date(moment().date())
                        ))
                    : true) &&
                  (selectedOutletItemsRaw[i].isOnlineMenu !== undefined ? selectedOutletItemsRaw[i].isOnlineMenu : true) &&
                  (selectedOutletItemsRaw[i].isStockCountActive !== undefined &&
                    selectedOutletItemsRaw[i].isStockCountActive !== false &&
                    selectedOutletItemsRaw[i].stockCount !== undefined &&
                    selectedOutletItemsRaw[i].toSellIgnoreStock !== undefined
                    ? selectedOutletItemsRaw[i].isStockCountActive &&
                    selectedOutletItemsRaw[i].stockCount > 0 &&
                    (selectedOutletItemsRaw[i].toSellIgnoreStock !== undefined
                      ? selectedOutletItemsRaw[i].toSellIgnoreStock
                      : true)
                    : true)
                );

                if (isValidOrderType && isValidActive) {
                  var existingItem = cartItems.find(item => item.itemId === selectedOutletItemsRaw[i].uniqueId);
                  // var existingItem = null;
                  // if (selectedOutletItemsRaw[i].uniqueId === selectedOutletItem.uniqueId) {
                  //   existingItem = selectedOutletItem;
                  // }

                  if (!existingItem) {
                    // didn't existed in cart, continue

                    ////////////////////////////////////////////////////////////////

                    // by customer tags

                    if (upsellingItem.upsellByType === UPSELL_BY_TYPE.CUSTOMER) {
                      if (upsellingItem.tagIdList && upsellingItem.tagIdList.length > 0) {
                        // try compare with tags

                        if (currCrmUser && currCrmUser.uniqueId) {
                          if (upsellingItem.tagIdList && upsellingItem.tagIdList.length > 0) {
                            for (var j = 0; j < upsellingItem.tagIdList.length; j++) {
                              var userTagId = upsellingItem.tagIdList[j];

                              if (selectedOutletCRMTagsDict[userTagId] && selectedOutletCRMTagsDict[userTagId].uniqueId) {
                                var userTag = selectedOutletCRMTagsDict[userTagId];

                                if (
                                  (userTag.emailList && userTag.emailList.includes(currCrmUser.userEmail))
                                  ||
                                  (userTag.phoneList && userTag.phoneList.includes(currCrmUser.userNumber))
                                ) {
                                  // toRecommendedItemsTemp.push(selectedOutletItemsRaw[i]);

                                  // toRecommendedItemsTemp.push({
                                  //     ...selectedOutletItemsRaw[i],
                                  //     price: priceUpselling,

                                  //     priceUpselling: priceUpselling,
                                  //     upsellingCampaignId: upsellingCampaign.uniqueId,
                                  // });

                                  isValidCampaign = true;

                                  break;
                                }
                              }
                            }
                          }
                        }
                      }
                      else {
                        if (
                          // toRecommendedItemsTemp.find(findItem => findItem.uniqueId === selectedOutletItemsRaw[i].uniqueId)
                          upsellingCampaignsAfterCheckoutTemp.find(findItem => findItem.uniqueId === upsellingCampaign.uniqueId)
                          ||
                          upsellingCampaignsAfterCheckoutRecommendationTemp.find(findItem => findItem.uniqueId === upsellingCampaign.uniqueId)
                        ) {
                          // means in the recommended list already, no need add
                        }
                        else {
                          // toRecommendedItemsTemp.push({
                          //     ...selectedOutletItemsRaw[i],
                          //     price: priceUpselling,

                          //     priceUpselling: priceUpselling,
                          //     upsellingCampaignId: upsellingCampaign.uniqueId,
                          // });

                          isValidCampaign = true;
                        }
                      }
                    }
                    else if (upsellingItem.upsellByType === UPSELL_BY_TYPE.ORDER_ITEM || upsellingItem.upsellByType === undefined) {
                      if (upsellingItem.tagIdList && upsellingItem.tagIdList.length > 0) {
                        // try compare with tags

                        if (upsellingItem.tagIdList && upsellingItem.tagIdList.length > 0) {
                          var cartOutletItemList = cartItems
                            .filter(item => {
                              var outletItem = selectedOutletItemsRaw.find(outletItem => outletItem.uniqueId === item.itemId);

                              return outletItem ? true : false;
                            })
                            .map(item => {
                              var outletItem = selectedOutletItemsRaw.find(outletItem => outletItem.uniqueId === item.itemId);

                              return outletItem;
                            });

                          //////////////////////////

                          // 2024-03-01 - crash prevention

                          if (cartOutletItemList.find(findItem => findItem === undefined)) {
                            continue;
                          }

                          //////////////////////////

                          var isValid = false;

                          for (var cartItemIndex = 0; cartItemIndex < cartOutletItemList.length; cartItemIndex++) {
                            if (cartOutletItemList[cartItemIndex] && cartOutletItemList[cartItemIndex].crmUserTagIdList && cartOutletItemList[cartItemIndex].crmUserTagIdList.length > 0) {
                              for (var j = 0; j < cartOutletItemList[cartItemIndex].crmUserTagIdList.length; j++) {
                                var productTagId = cartOutletItemList[cartItemIndex].crmUserTagIdList[j];

                                if (upsellingItem.tagIdList.includes(productTagId)) {
                                  isValid = true;
                                  break;
                                }
                              }
                            }

                            if (cartOutletItemList[cartItemIndex] && selectedOutletItemCategoriesDict && selectedOutletItemCategoriesDict[cartOutletItemList[cartItemIndex].categoryId] &&
                              selectedOutletItemCategoriesDict[cartOutletItemList[cartItemIndex].categoryId].crmUserTagIdList &&
                              selectedOutletItemCategoriesDict[cartOutletItemList[cartItemIndex].categoryId].crmUserTagIdList.length > 0) {
                              for (var j = 0; j < selectedOutletItemCategoriesDict[cartOutletItemList[cartItemIndex].categoryId].crmUserTagIdList.length; j++) {
                                var categoryIdTagId = selectedOutletItemCategoriesDict[cartOutletItemList[cartItemIndex].categoryId].crmUserTagIdList[j];

                                if (upsellingItem.tagIdList.includes(categoryIdTagId)) {
                                  isValid = true;
                                  break;
                                }
                              }
                            }

                            if (isValid) {
                              break;
                            }
                          }

                          if (!isValid) {
                            // means no matched tag, try to find by category

                            // for (var cartItemIndex = 0; cartItemIndex < cartOutletItemList.length; cartItemIndex++) {
                            //   // if (selectedOutletItemsRaw[i] === undefined ||
                            //   //   cartOutletItemList[cartItemIndex] === undefined) {
                            //   //   console.log(selectedOutletItemsRaw);
                            //   //   console.log(i);
                            //   //   console.log(cartOutletItemList);
                            //   //   console.log(cartItemIndex);
                            //   //   console.log('break');
                            //   // }

                            //   if (cartOutletItemList[cartItemIndex].categoryId === selectedOutletItemsRaw[i].categoryId) {
                            //     isValid = true;
                            //     break;
                            //   }
                            // }
                          }
                        }

                        if (isValid) {
                          if (
                            // toRecommendedItemsTemp.find(findItem => findItem.uniqueId === selectedOutletItemsRaw[i].uniqueId)
                            upsellingCampaignsAfterCheckoutTemp.find(findItem => findItem.uniqueId === upsellingCampaign.uniqueId)
                            ||
                            upsellingCampaignsAfterCheckoutRecommendationTemp.find(findItem => findItem.uniqueId === upsellingCampaign.uniqueId)
                          ) {
                            // means in the recommended list already, no need add
                          }
                          else {
                            // toRecommendedItemsTemp.push({
                            //     ...selectedOutletItemsRaw[i],
                            //     price: priceUpselling,

                            //     priceUpselling: priceUpselling,
                            //     upsellingCampaignId: upsellingCampaign.uniqueId,
                            // });

                            isValidCampaign = true;
                          }
                        }
                      }
                      else {
                        if (
                          // toRecommendedItemsTemp.find(findItem => findItem.uniqueId === selectedOutletItemsRaw[i].uniqueId)
                          upsellingCampaignsAfterCheckoutTemp.find(findItem => findItem.uniqueId === upsellingCampaign.uniqueId)
                          ||
                          upsellingCampaignsAfterCheckoutRecommendationTemp.find(findItem => findItem.uniqueId === upsellingCampaign.uniqueId)
                        ) {
                          // means in the recommended list already, no need add
                        }
                        else {
                          // toRecommendedItemsTemp.push({
                          //     ...selectedOutletItemsRaw[i],
                          //     price: priceUpselling,

                          //     priceUpselling: priceUpselling,
                          //     upsellingCampaignId: upsellingCampaign.uniqueId,
                          // });

                          isValidCampaign = true;
                        }
                      }
                    }
                  }
                }
              }
            }

            if (isValidCampaign) {
              break;
            }
          }

          if (isValidCampaign) {
            break;
          }
        }

        // if (toRecommendedItemsTemp.length >= 3) {
        //     break;
        // }

        if (isValidCampaign) {
          if (upsellingCampaign.upsellingSection === UPSELLING_SECTION.AFTER_CHECKOUT) {
            upsellingCampaignsAfterCheckoutTemp.push(upsellingCampaign);
          }
          else if (upsellingCampaign.upsellingSection === UPSELLING_SECTION.AFTER_CHECKOUT_RECOMMENDATION) {
            upsellingCampaignsAfterCheckoutRecommendationTemp.push(upsellingCampaign);
          }
        }
      }
    }

    global.upsellingCampaignsAfterCheckout = upsellingCampaignsAfterCheckoutTemp;
    global.upsellingCampaignsAfterCheckoutRecommendation = upsellingCampaignsAfterCheckoutRecommendationTemp;

    // setToRecommendedItems(toRecommendedItemsTemp.slice(0, 3));
    DataStore.update(s => {
      s.upsellingCampaignsAfterCheckout = upsellingCampaignsAfterCheckoutTemp;
      s.upsellingCampaignsAfterCheckoutRecommendation = upsellingCampaignsAfterCheckoutRecommendationTemp;
    });
  }, [
    cartItems,
    selectedOutletItemsRaw,
    currCrmUser,
    selectedOutletCRMTagsDict,
    orderType,
    availableUpsellingCampaigns,
    selectedOutletItemCategoriesDict,

    selectedOutlet,
  ]);

  /////////////////////////////////////////////////////////////////////////

  const orderPaymentStatus = CommonStore.useState(s => s.orderPaymentStatus);

  useEffect(async () => {
    if (orderPaymentStatus && orderPaymentStatus.statusCode === '00') {
      // for those users that used 'back' button in phone, when payment completed

      // to clear the paid items and redirect back      

      if (selectedOutlet) {
        global.selectedOutlet = selectedOutlet;
      }

      CommonStore.update(
        (s) => {
          // s.paymentDetails = null;

          // s.isLoading = true;

          s.cartItems = [];
          s.cartItemsProcessed = [];
          s.currPage = '';
        },
        async () => {
          console.log('orderPaymentStatus');
          console.log(orderPaymentStatus);

          await deleteDoc(
            doc(
              global.db,
              Collections.OrderPaymentStatus,
              orderPaymentStatus.uniqueId,
            )
          );

          if (orderPaymentStatus.statusCode === "00") {
            // placeUserOrder(paymentDetailsResult);

            PaymentStore.update(s => {
              s.cartItemsPayment = [];
              s.outletIdPayment = '';
              s.dateTimePayment = Date.now();
              s.orderTypePayment = '';
            });

            // afterPlaceUserOrder(orderPaymentStatus, false, orderIdCreated);
          } else {
            // error

            // PaymentStore.update(s => {
            //   s.timestampPayment = Date.now();
            // });

            // window.location.reload();

            // CommonStore.update((s) => {
            //   s.alertObj = {
            //     title: "Error",
            //     message: "Payment failed. Please try again.",
            //   };
            // });
          }
        }
      );
    }
  }, [orderPaymentStatus]);

  useEffect(() => {
    // var takeawayOrders = global.takeawayOrders;
    var takeawayOrders = selectedUserOrderAnonymousTakeaway;

    if (takeawayOrders && takeawayOrders.length > 0) {
      // CommonStore.update(s => {
      //     s.selectedUserOrderTakeaway = takeawayOrders[0];

      //     s.selectedUserOrderOthersTakeaway = takeawayOrders.slice(1);
      // });

      setUserOrdersTakeaway(takeawayOrders);

      setUserOrdersTakeawayActions(takeawayOrders.reduce((accum, order) => accum.concat(order.kdsActionHistory ? order.kdsActionHistory : []), []));
    }
    else {
      // CommonStore.update(s => {
      //     s.selectedUserOrderTakeaway = {};

      //     s.selectedUserOrderOthersTakeaway = [];
      // });

      setUserOrdersTakeaway([]);

      setUserOrdersTakeawayActions(takeawayOrders.reduce((accum, order) => accum.concat(order.kdsActionHistory ? order.kdsActionHistory : []), []));
    }
  }, [selectedUserOrderAnonymousTakeaway]);

  useEffect(() => {
    var selectedTableOrdersOwn = selectedTableOrders.filter(order => {
      let validStatus = false;

      // if (
      //     order.userIdAnonymous === userIdAnonymous ||
      //     order.userIdAnonymous === '' ||
      //     order.userIdAnonymous === undefined
      // ) {
      //     validStatus = true;
      // }

      validStatus = true;

      // if (order.userPhone) {
      //   if (order.userPhone !== userNumber) {
      //     // means should be other people orders

      //     if (
      //       order.taggableVoucherId ||
      //       (order.promotionIdList &&
      //         order.promotionIdList.length > 0)
      //       ||
      //       (order.promoCodePromotionIdList &&
      //         order.promoCodePromotionIdList.length > 0)
      //       ||
      //       (order.cartPromotionIdList &&
      //         order.cartPromotionIdList.length > 0)
      //     ) {
      //       // means is promotional order, needs separated

      //       validStatus = false;

      //       // selectedTableOtherPeoplePromoVoucherOrdersTemp.push(order);
      //     }
      //   }
      // }
      // else if (selectedOutlet.dineInRequiredAuthorization &&
      //   order.orderType === ORDER_TYPE.DINEIN &&
      //   order.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED) {
      //   // means need wait merchant approved first, then only can show

      //   validStatus = false;

      //   // selectedTablePendingApprovalOrdersTemp.push(order);
      // }

      return validStatus;
    });

    if (selectedTableOrdersOwn && selectedTableOrdersOwn.length > 0) {
      // CommonStore.update(s => {
      //     s.selectedUserOrder = selectedTableOrdersOwn[0];

      //     s.selectedUserOrderOthers = selectedTableOrdersOwn.slice(1);
      // });

      setUserOrdersDineIn(selectedTableOrdersOwn);

      setUserOrdersDineInActions(selectedTableOrdersOwn.reduce((accum, order) => accum.concat(order.kdsActionHistory ? order.kdsActionHistory : []), []));
    }
    else {
      // CommonStore.update(s => {
      //     s.selectedUserOrder = {};

      //     s.selectedUserOrderOthers = [];
      // });

      setUserOrdersDineIn(selectedTableOrdersOwn);

      setUserOrdersDineInActions(selectedTableOrdersOwn.reduce((accum, order) => accum.concat(order.kdsActionHistory ? order.kdsActionHistory : []), []));
    }
  }, [selectedTableOrders, userIdAnonymous]);

  /////////////////////////////////////////////////////////////////////////

  useEffect(() => {
    if (linkTo) {
      DataStore.update((s) => {
        s.linkToFunc = linkTo;
      });

      global.linkToFunc = linkTo;
    }

    // setTimeout(() => {
    //   if (global.selectedOutlet === null) {
    //     console.log('global.selectedoutlet = null (2) (==test==)');

    //     linkTo && linkTo(`${prefix}/scan`);
    //   }
    // }, 10000);

    // if (route.params === undefined) {
    //   linkTo && linkTo(`${prefix}/error`);
    // } else {
    //   const subdomain = route.params.subdomain;
    // }
  }, [linkTo, route]);

  /////////////////////////////////////////////////////////////////////////

  // 11/10/2022 For Available On (Time) - Greg
  const timeCheckItem = CommonStore.useState(s => s.timeCheckItem);

  useEffect(() => {
    if (global.outletName) {
      logEvent(global.analytics, ANALYTICS.WO_OUTLET_MENU, {
        eventNameParsed: ANALYTICS_PARSED.WO_OUTLET_MENU,

        outletName: global.outletName ? `${global.outletName} (Web)` : '',

        webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
      });
    }

    setInterval(() => {
      CommonStore.update(s => {
        s.timeCheckItem = Date.now();
      });
    }, 30000);

    CommonStore.update(s => {
      s.currPage = '';
    });
  }, []);

  /////////////////////////////////////////////////////////////////////////

  // 5/1/2023 For Search Bar - Greg
  const [search, setSearch] = useState('');
  const [loading, setLoading] = useState(false);
  const [isOnSearch, setIsOnSearch] = useState(false);

  const [openSearchBar, setOpenSearchBar] = useState(false);

  // const selectedOutletItemsRaw2 = CommonStore.useState(
  //   (s) => s.selectedOutletItems
  // );

  /////////////////////////////////////////////////////////////////////////

  // 2024-03-25 - for user login popup related

  const startAsMember = TempStore.useState(s => s.startAsMember);
  const checkedSignInWithPhone = TempStore.useState(s => s.checkedSignInWithPhone);

  /////////////////////////////////////////////////////////////////////////

  // 30/3/2023 Redeem Vooucher Popup - Greg

  const claimVoucherList = TempStore.useState((s) => s.claimVoucherList);
  const showClaimVoucher = TempStore.useState(s => s.showClaimVoucher);
  const claimingVoucher = TempStore.useState(s => s.claimingVoucher);
  const selectedClaimVoucher = TempStore.useState((s) => s.selectedClaimVoucher);

  const showGreetingPopup = TempStore.useState(
    (s) => s.showGreetingPopup
  );

  const userPointsBalance = UserStore.useState((s) => s.userPointsBalance);

  useEffect(() => {
    if (!showGreetingPopup && !global.isClaimedVoucherShownOnce) {
      var tempVoucherCheckList = [];

      // claimVoucherList.filter((item) => {
      //   if (item.voucherPointsRequired <= userPointsBalance) {
      //     tempVoucherCheckList.push(item);
      //   }
      // });

      if (tempVoucherCheckList.length == 0) {
        TempStore.update(s => {
          s.showClaimVoucher = false;
        });
      }
      else {
        TempStore.update(s => {
          s.claimVoucherList = tempVoucherCheckList;
          //s.showClaimVoucher = true;
        });

        // if (claimingVoucher) {
        //   TempStore.update(s => {
        //     //s.claimVoucherList = tempVoucherCheckList;
        //     s.showClaimVoucher = true;
        //   });
        // }

        TempStore.update(s => {
          //s.claimVoucherList = tempVoucherCheckList;
          s.showClaimVoucher = false;
        });

        global.isClaimedVoucherShownOnce = true;
      }
    }

  }, [claimVoucherList, showGreetingPopup]);

  /////////////////////////////////////////////////////////////////////////

  // 2024-03-25 - to determine whether to show user login popup or not

  useEffect(() => {
    if (selectedOutlet && selectedOutlet.uniqueId) {
      if (userName && userNumber) {
        // no need show login popup

        if (checkedSignInWithPhone === 'yes') {
          TempStore.update(s => {
            s.showStartAsGuestButton = false;
          });
        }
      }
      else {
        if (startAsMember && checkedSignInWithPhone === 'no') {
          // can show login popup now

          // setShowLoginPhoneModal(true);

          TempStore.update(s => {
            s.showLoginPhoneModal = true;
          });
        } else {
          {/* 20240618 e-invoice */ }
          // TempStore.update((s) => {
          //   s.showSignUpMember = true;
          // })
        }

        if (checkedSignInWithPhone === 'yes') {
          TempStore.update(s => {
            s.showStartAsGuestButton = false;
          });
        }
      }
    }
  }, [
    userName,
    userNumber,

    startAsMember,
    checkedSignInWithPhone,

    selectedOutlet,
  ]);

  /////////////////////////////////////////////////////////////////////////

  // const dummyVoucherList = [
  //   {
  //     image: '',
  //     campaignName: 'test 1',
  //     expirationDate: 1657244902242,
  //     campaignDescription: 'testing 123',
  //     priceToBuy: 12,

  //   },
  //   {
  //     image: '',
  //     campaignName: 'test 2',
  //     expirationDate: 1657244902242,
  //     campaignDescription: 'testing 123',
  //     priceToBuy: 12,

  //   },
  //   {
  //     image: '',
  //     campaignName: 'test 3',
  //     expirationDate: 1657244902242,
  //     campaignDescription: 'testing 123',
  //     priceToBuy: 12,

  //   },
  //   {
  //     image: '',
  //     campaignName: 'test 4',
  //     expirationDate: 1657244902242,
  //     campaignDescription: 'testing 123',
  //     priceToBuy: 12,

  //   },
  //   {
  //     image: '',
  //     campaignName: 'test 5',
  //     expirationDate: 1657244902242,
  //     campaignDescription: 'testing 123',
  //     priceToBuy: 12,

  //   },
  //   {
  //     image: '',
  //     campaignName: 'test 6',
  //     expirationDate: 1657244902242,
  //     campaignDescription: 'testing 123',
  //     priceToBuy: 12,

  //   },
  // ]

  console.log('selectedOutletItemCategory');
  console.log(selectedOutletItemCategory);

  const selectedTaggableVoucher = CommonStore.useState(s => s.selectedTaggableVoucher);

  useEffect(() => {
    if (selectedOutletItems && selectedOutletItems.length > 0 && selectedOutletItemCategory && selectedTaggableVoucher && selectedTaggableVoucher.uniqueId && selectedTaggableVoucher.criteriaList &&
      selectedTaggableVoucher.criteriaList.length > 0 && selectedTaggableVoucher.criteriaList[0] && selectedTaggableVoucher.criteriaList[0].variationItemsSku &&
      selectedTaggableVoucher.criteriaList[0].variationItemsSku.length > 0) {
      if (selectedTaggableVoucher.criteriaList[0].variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
        const foundItem = selectedOutletItems.find(findItem => findItem.sku === selectedTaggableVoucher.criteriaList[0].variationItemsSku[0]);

        if (foundItem) {
          const foundCategory = selectedOutletItemCategories.find(findCategory => findCategory.uniqueId === foundItem.categoryId);

          if (foundCategory && foundCategory.uniqueId) {
            CommonStore.update(s => {
              s.selectedOutletItemCategory = foundCategory;
            });
          }
        }
      }
      else {

        CommonStore.update(s => {
          const targetCategoryName = selectedTaggableVoucher.criteriaList[0].variationItemsSku[0];

          const foundCategory = selectedOutletItemCategories.find(findCategory => findCategory && findCategory.name === targetCategoryName);

          if (foundCategory && foundCategory.uniqueId) {
            CommonStore.update(s => {
              s.selectedOutletItemCategory = foundCategory;
            });
          }
        })
      }
    }
  }, [selectedOutletItems, selectedOutletItemCategories, selectedTaggableVoucher]);

  const renderVoucherList = ({ item }) => {
    return (
      <View style={{
        flex: 1,
        width: isMobile() ? 300 : Dimensions.get('window').width * 0.225,
        // minWidth: (Styles.width / 2) - 64,
        height: Dimensions.get('window').height * 0.05,
        // backgroundColor: Colors.primaryColor,
        backgroundColor: 'white',
        flexDirection: 'row',
        marginTop: 10,
        alignContent: 'center',
        alignItems: 'center',
        borderRadius: 20,

        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 3,
        },
        shadowOpacity: 0.29,
        shadowRadius: 4.65,

        elevation: 7,
      }}>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          width: windowWidth * 0.9,
          margin: 10,
          justifyContent: 'space-between'
        }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              width: windowWidth * 0.9,
              margin: 10,
              justifyContent: 'space-between'
            }}>
            <View style={{ flexDirection: 'row' }}>
              <View style={{ justifyContent: 'center' }}>
                {item.image ? (
                  <>
                    <AsyncImage
                      source={{ uri: item.image }}
                      item={item}
                      style={{
                        width: 65,
                        height: 65,
                        borderRadius: 10,
                      }}
                    />
                  </>
                ) :
                  <MaterialCommunityIcons
                    name={"ticket-confirmation-outline"}
                    size={65}
                  />
                  // <View style={{
                  //   width: 65,
                  //   height: 65,
                  //   backgroundColor: 'blue'
                  // }} />
                }
              </View>

              <View>
                <Text
                  style={{
                    width: 100,
                    marginTop: 10,
                    marginLeft: 10,
                    fontSize: 20,
                    fontFamily: 'NunitoSans-Bold',
                    //color: Colors.whiteColor,
                  }}
                  numberOfLines={1}>
                  {item.campaignName}
                </Text>

                <Text
                  style={{
                    width: 100,
                    marginTop: 5,
                    marginLeft: 10,
                    fontSize: 16,
                    fontFamily: 'NunitoSans-Bold',
                    //color: Colors.whiteColor,
                  }}
                  numberOfLines={1}>
                  {item.voucherPointsRequired} Points
                </Text>

                <Text
                  style={{
                    //marginTop: 10,
                    marginLeft: 10,
                    fontSize: 12,
                    fontFamily: 'NunitoSans-SemiBold',
                    //color: Colors.whiteColor,
                  }}>
                  Expiry Date:
                </Text>

                <Text
                  style={{
                    width: isMobile() ? 100 : '100%',
                    //marginTop: 10,
                    marginLeft: 10,
                    fontSize: 12,
                    fontFamily: 'NunitoSans-Regular',
                    //color: Colors.whiteColor,
                  }}>
                  {moment(item.promoDateEnd).format('dddd, Do MMM YYYY')}
                </Text>
              </View>
              <View style={isMobile() ? {
                marginLeft: -10
              } : {}}>
                <TouchableOpacity
                  style={{
                    marginLeft: isMobile() ? '30%' : 80,
                  }}
                  onPress={async () => {
                    if (email) {
                      TempStore.update(s => {
                        s.showClaimVoucher = false;
                        // s.claimingVoucher = true;
                        s.showGeneralAskUserInfo = true;
                        s.selectedClaimVoucher = item;
                      });
                    }
                    else {
                      TempStore.update(s => {
                        s.showClaimVoucher = false;
                        // s.claimingVoucher = true;
                        s.showGeneralAskUserInfo = true;
                        s.selectedClaimVoucher = item;
                      });
                    }
                  }}>
                  <View
                    style={{
                      backgroundColor: Colors.primaryColor,
                      padding: 28,
                      paddingVertical: 12,
                      borderRadius: 10,
                      alignItems: "center",

                      //marginHorizontal: 48,
                      marginTop: 30,
                      marginBottom: 24,

                      shadowColor: "#000",
                      shadowOffset: {
                        width: 0,
                        height: 1,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 2.22,
                      elevation: 3,


                      ...(!isMobile() && {
                        //width: "100%",
                        alignSelf: "center",
                      }),
                    }}
                  >
                    <Text
                      style={{
                        color: "#ffffff",
                        fontSize: 16,
                        fontFamily: "NunitoSans-Bold",
                      }}
                    >
                      CLAIM
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </View>
    );
  };



  /////////////////////////////////////////////////////////////////////////

  useEffect(() => {
    var activeDineInOrdersTemp = [];

    for (var i = 0; i < userOrders.length; i++) {
      if (
        userOrders[i].orderStatus !== USER_ORDER_STATUS.ORDER_COMPLETED &&
        userOrders[i].orderStatus !==
        USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT &&
        userOrders[i].orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER
      ) {
        if (userOrders[i].orderType === ORDER_TYPE.DINEIN) {
          if (userOrders[i].outletId === selectedOutlet.uniqueId) {
            activeDineInOrdersTemp.push(userOrders[i]);
          }
        }
      }
    }

    setActiveDineInOrders(activeDineInOrdersTemp);
  }, [userOrders]);

  /////////////////////////////////////////////////////////////////////////

  useEffect(async () => {
    if (selectedOutlet && selectedOutlet.uniqueId) {
      // var merchantSnapshot = await firebase
      //   .firestore()
      //   .collection(Collections.Merchant)
      //   .where("uniqueId", "==", selectedOutlet.merchantId)
      //   .limit(1)
      //   .get();

      const merchantSnapshot = await getDocs(
        query(
          collection(global.db, Collections.Merchant),
          where("uniqueId", "==", selectedOutlet.merchantId),
          limit(1),
        )
      );
      if (!merchantSnapshot.empty) {
        // setMerchantName(merchantSnapshot.docs[0].name);

        CommonStore.update(s => {
          s.selectedMerchant = merchantSnapshot.docs[0].data();
        });
      }

      await AsyncStorage.multiSet([
        ['latestOutletId', selectedOutlet.uniqueId],
        ['latestSubdomain', (selectedOutlet.subdomain || '')],
        // ['latestTableId', route.params.tableId],
        // ['latestUserId', result.userId],
      ]);
    }
  }, [selectedOutlet]);

  useEffect(() => {
    var filteredOutletCategoriesTemp = selectedOutletItemCategories.filter((category) => {
      return category.hideInOrderTypes && category.hideInOrderTypes.length > 0
        ? category.hideInOrderTypes.includes(orderType)
          ? false
          : true
        : true;
    }).filter(category => {
      let isValidZone = true;
      if (orderType === ORDER_TYPE.PICKUP) {
        // do nothing
      }
      else if (orderType === ORDER_TYPE.DINEIN) {
        if (category.hideOutletSectionIdList &&
          category.hideOutletSectionIdList.length > 0) {
          if (category.hideOutletSectionIdList.includes(
            selectedOutletSectionId
          )) {
            isValidZone = false;
          }
        }
        else {
          // do nothing 
        }
      }

      if (
        isValidZone &&
        (category.isActive || category.isActive === undefined) &&
        (category.isAvailableDayActive
          ? (category.effectiveTypeOptions.includes(effectiveDays) &&
            category.effectiveStartTime && category.effectiveEndTime &&
            moment().isSameOrAfter(
              moment(category.effectiveStartTime)
                .year(moment().year())
                .month(moment().month())
                .date(moment().date())
            )
            &&
            moment().isBefore
              (moment(category.effectiveEndTime)
                .year(moment().year())
                .month(moment().month())
                .date(moment().date())
              )
          )
          : true)) {
        return true;
      }
    });

    filteredOutletCategoriesTemp = filteredOutletCategoriesTemp.sort((a, b) => {
      return (
        (a.orderIndex
          ? a.orderIndex
          : filteredOutletCategoriesTemp.length) -
        (b.orderIndex
          ? b.orderIndex
          : filteredOutletCategoriesTemp.length)
      );
    });

    // filteredOutletCategoriesTemp = filteredOutletCategoriesTemp.map((category, cIndex) => {
    //   return {
    //     ...category,
    //     nextCategory: (cIndex >= filteredOutletCategoriesTemp.length - 1) ? filteredOutletCategoriesTemp[0] : filteredOutletCategoriesTemp[cIndex + 1],
    //     nextCategoryIndex: (cIndex >= filteredOutletCategoriesTemp.length - 1) ? 0 : cIndex + 1,
    //   };
    // });

    console.log('sorted filteredOutletCategoriesTemp!');
    console.log(filteredOutletCategoriesTemp);

    setFilteredOutletCategories(
      filteredOutletCategoriesTemp
    );

    // CommonStore.update(s => {
    //   s.visibleCategories = filteredOutletCategoriesTemp;
    // });

    global.visibleCategories = filteredOutletCategoriesTemp;
  }, [
    selectedOutletItemCategories,
    timeCheckItem,
    selectedOutletSectionId,
    orderType
  ]);

  const timestampOutletCategory = CommonStore.useState(s => s.timestampOutletCategory);

  useEffect(() => {
    if (filteredOutletCategories.length > 0 &&
      selectedOutletItemCategory &&
      !filteredOutletCategories.find(category => category.uniqueId === selectedOutletItemCategory.uniqueId)) {
      InteractionManager.runAfterInteractions(() => {
        // global.selectedOutletItemCategory = filteredOutletCategories[0];

        console.log('to update selectedOutletItemCategory 1');
        console.log(filteredOutletCategories[0]);

        CommonStore.update(s => {
          s.selectedOutletItemCategory = filteredOutletCategories[0];
        });
      });
    }
  }, [filteredOutletCategories]);

  useEffect(() => {
    if (filteredOutletCategories.length > 0) {
      if (selectedOutletItemCategory &&
        !filteredOutletCategories.find(category => {
          if (category.uniqueId === selectedOutletItemCategory.uniqueId) {
            return true;
          }
        })) {
        InteractionManager.runAfterInteractions(() => {
          // global.selectedOutletItemCategory = filteredOutletCategories[0];

          console.log('to update selectedOutletItemCategory 2');
          console.log(filteredOutletCategories[0]);

          CommonStore.update(s => {
            s.selectedOutletItemCategory = filteredOutletCategories[0];
          });
        });
      }
    }
  }, [timestampOutletCategory, filteredOutletCategories]);

  console.log('selectedOutletItemCategory');
  console.log(selectedOutletItemCategory);

  useEffect(() => {
    var characterLimit = 30;
    var trimmedOutletNameTemp = "";
    var selectedOutletNameTemp = "";

    if (selectedOutlet) {
      console.log(selectedOutlet.name.split(""));
      selectedOutletNameTemp = selectedOutlet.name.split("");

      if (selectedOutletNameTemp.length > characterLimit) {
        // need trim
        for (var i = 0; i < characterLimit; i++) {
          trimmedOutletNameTemp += selectedOutletNameTemp[i];
        }
        trimmedOutletNameTemp += "...";
      } else {
        // no need trim
        for (var i = 0; i < selectedOutletNameTemp.length; i++) {
          trimmedOutletNameTemp += selectedOutletNameTemp[i];
        }
      }

      setTrimmedOutletName(trimmedOutletNameTemp);
    }
  });

  useEffect(() => {
    if (
      // isPlacingReservation
      false
    ) {
      setSelectedOutletItems(
        selectedOutletItemsRaw.filter((item) => {
          var resultSearch = false;

          if (search !== '') {
            const searchLowerCase = search.toString().toLowerCase();
            if ((item.name.toLowerCase().includes(searchLowerCase) || (item.barcode && item.barcode.serialNumber.includes(searchLowerCase)))) {
              resultSearch = true;
            } else {
              resultSearch = false;
            }
          } else {
            resultSearch = true;
          }

          return item.isReservationMenu && resultSearch;
        })
      );
    } else {
      const searchLowerCase = search.toString().toLowerCase();

      let selectedOutletItemsTemp = [];

      if (searchLowerCase !== '') {
        selectedOutletItemsTemp = selectedOutletItemsRaw.filter((item) => {
          var category = null;

          for (var i = 0; i < filteredOutletCategories.length; i++) {
            if (filteredOutletCategories[i].uniqueId === item.categoryId) {
              category = filteredOutletCategories[i];
            }
          }

          if (category) {
            let isValidZone = true;
            if (orderType === ORDER_TYPE.PICKUP) {
              // do nothing
            }
            else if (orderType === ORDER_TYPE.DINEIN) {
              if (category.hideOutletSectionIdList &&
                category.hideOutletSectionIdList.length > 0) {
                if (category.hideOutletSectionIdList.includes(
                  selectedOutletSectionId
                )) {
                  isValidZone = false;
                }
              }
              else {
                // do nothing 
              }
            }

            if (
              isValidZone
              &&
              (
                category.hideInOrderTypes && category.hideInOrderTypes.length > 0
                  ? category.hideInOrderTypes.includes(orderType)
                    ? false
                    : true
                  : true
              )
              &&
              (
                (category.isActive || category.isActive === undefined) &&
                (category.isAvailableDayActive
                  ? (category.effectiveTypeOptions.includes(effectiveDays) &&
                    category.effectiveStartTime && category.effectiveEndTime &&
                    moment().isSameOrAfter(
                      moment(category.effectiveStartTime)
                        .year(moment().year())
                        .month(moment().month())
                        .date(moment().date())
                    )
                    &&
                    moment().isBefore
                      (moment(category.effectiveEndTime)
                        .year(moment().year())
                        .month(moment().month())
                        .date(moment().date())
                      )
                  )
                  : true)
              )
            ) {
              if (searchLowerCase !== '') {
                if (item && (item.name.toLowerCase().includes(searchLowerCase) || (item.barcode && item.barcode.serialNumber.includes(searchLowerCase)))) {
                  return true;
                }
                else {
                  return false;
                }
              }
              else {
                return true;
              }
            }
            else {
              return false;
            }
          }
          else {
            return false;
          }
        });
      }
      else {
        if (openSearchBar) {
          selectedOutletItemsTemp = [];
        }
        else {
          selectedOutletItemsTemp = selectedOutletItemsRaw;
        }
      }

      setSelectedOutletItems(selectedOutletItemsTemp);
    }

    if (isPlacingReservation && isDepositOnly) {
      // redirect to cart screen

      linkTo && linkTo(`${prefix}/outlet/${selectedOutlet.subdomain}/cart`);
    }
  }, [
    selectedOutletItemsRaw,
    filteredOutletCategories,
    isPlacingReservation,
    isDepositOnly,
    timeCheckItem,
    search,

    selectedOutletSectionId,
    orderType,

    openSearchBar,
  ]);

  /////////////////////////////////////////////////////////////////////////

  // const currPageStack = TempStore.useState(s => s.currPageStack);

  // const menuItemDetailModal = CommonStore.useState(s => s.menuItemDetailModal);

  // useBackButton(() => {
  //   return true;
  // });

  // useEffect(() => {
  //   navigation && navigation.addListener('beforeRemove', (e) => {
  //     // if (!hasUnsavedChanges) {
  //     //   // If we don't have unsaved changes, then we don't need to do anything
  //     //   return;
  //     // }

  //     console.log('prevent default');

  //     // Prevent default behavior of leaving the screen
  //     e.preventDefault();

  //     // Prompt the user before leaving the screen
  //     // Alert.alert(
  //     //   'Discard changes?',
  //     //   'You have unsaved changes. Are you sure to discard them and leave the screen?',
  //     //   [
  //     //     { text: "Don't leave", style: 'cancel', onPress: () => { } },
  //     //     {
  //     //       text: 'Discard',
  //     //       style: 'destructive',
  //     //       // If the user confirmed, then we dispatch the action we blocked earlier
  //     //       // This will continue the action that had triggered the removal of the screen
  //     //       onPress: () => navigation.dispatch(e.data.action),
  //     //     },
  //     //   ]
  //     // );
  //   })
  // }, [navigation]);

  useEffect(() => {
    // const popStateListener = async e => {
    //   // e.preventDefault();
    //   // console.log('unload!');

    //   // linkTo(`${prefix}/outlet/menu`);

    //   // window.history.pushState(null, '', window.location.href);

    //   if (
    //     global.menuItemDetailModal
    //     // false
    //   ) {
    //     CommonStore.update(s => {
    //       s.menuItemDetailModal = false;
    //     });

    //     global.menuItemDetailModal = false;

    //     // if (!subdomain) {
    //     //   global.linkToFunc && global.linkToFunc(`${prefix}/outlet/menu`);
    //     // } else {
    //     //   global.linkToFunc && global.linkToFunc(`${prefix}/outlet/${subdomain}/menu`);
    //     // }        

    //     // window.history.pushState({
    //     //   page: 'menuItemDetailModal',
    //     // }, '');

    //     // window.history.pushState({
    //     //   page: 'menuItemDetailModal',
    //     // }, '');

    //     // window.history.go(1);

    //     // const subdomain = await AsyncStorage.getItem("latestSubdomain");

    //     // if (!subdomain) {
    //     //   global.linkToFunc && global.linkToFunc(`${prefix}/outlet/menu`);
    //     // } else {
    //     //   global.linkToFunc && global.linkToFunc(`${prefix}/outlet/${subdomain}/menu`);
    //     // }

    //     // e.preventDefault();
    //   }
    //   else {
    //     var isNeededToPopState = true;

    //     if (global.currPageStack[global.currPageStack.length - 1] === 'RewardDetailsScreen') {
    //       isNeededToPopState = false;
    //     }

    //     if (global.currPageStack[global.currPageStack.length - 1] === 'OR_StampDetails') {
    //       isNeededToPopState = false;
    //     }

    //     if (global.currPageStack[global.currPageStack.length - 1] === 'TaggableVoucherListScreen') {
    //       isNeededToPopState = false;
    //     }

    //     // if (global.currPageStack[global.currPageStack.length - 2] === 'OrderHistoryDetail') {
    //     //   isNeededToPopState = false;
    //     // }

    //     if (
    //       global.currPageStack[global.currPageStack.length - 1] === 'CartScreen'
    //       &&
    //       global.currPageStack[global.currPageStack.length - 2] === 'init'
    //     ) {
    //       if (global.clickedCartIcon) {
    //         isNeededToPopState = false;

    //         const subdomain = await AsyncStorage.getItem("latestSubdomain");

    //         if (!subdomain) {
    //           global.linkToFunc && global.linkToFunc(`${prefix}/outlet/cart`);
    //         }
    //         else {
    //           global.linkToFunc && global.linkToFunc(`${prefix}/outlet/${subdomain}/cart`);
    //         }
    //       }
    //     }

    //     if (
    //       global.currPageStack[global.currPageStack.length - 1] === 'CartScreen'
    //       &&
    //       (
    //         global.currPageStack[global.currPageStack.length - 2] === 'OrderHistoryDetail'
    //         ||
    //         global.currPageStack[global.currPageStack.length - 2] === 'OrderHistoryDetailTakeaway'
    //       )
    //     ) {
    //       isNeededToPopState = false;
    //     }

    //     if (isNeededToPopState) {
    //       const subdomain = await AsyncStorage.getItem("latestSubdomain");

    //       const onUpdatingCartItem = await AsyncStorage.getItem('onUpdatingCartItem');
    //       if (onUpdatingCartItem !== '1') {
    //         ///////////////////////////////////

    //         // 2024-06-19 - for OutletScreen support

    //         if (
    //           global.currPageStack[global.currPageStack.length - 1] === 'OutletScreen'
    //         ) {
    //           // global.currPageStack.pop();

    //           // TempStore.update(s => {
    //           //   s.showGreetingPopup = false;
    //           // });

    //           const subdomain = await AsyncStorage.getItem("latestSubdomain");

    //           if (subdomain) {
    //             global.currPageStack = [];

    //             TempStore.update(s => {
    //               s.showGreetingPopup = false;
    //             });

    //             // linkTo && linkTo(`${prefix}/outlet/${selectedOutlet.subdomain}`);

    //             window.location.replace(`${prefix}/outlet/${subdomain}`);
    //           }
    //         }
    //         else {
    //           if (!subdomain) {
    //             global.linkToFunc && global.linkToFunc(`${prefix}/outlet/menu`);

    //             // if (!global.isInitBack) {
    //             //   global.isInitBack = true;

    //             //   window.history.back();
    //             // }
    //           } else {
    //             global.linkToFunc && global.linkToFunc(`${prefix}/outlet/${subdomain}/menu`);

    //             // if (!global.isInitBack) {
    //             //   global.isInitBack = true;

    //             //   window.history.back();
    //             // }
    //           }
    //         }
    //       }
    //     }
    //   }
    // };

    // // window.addEventListener(
    // //   "popstate",
    // //   popStateListener,
    // //   false
    // // );

    // window.onpopstate = popStateListener;

    const loadListener = () => {
      setTimeout(() => {
        readStates();
      }, 1000);

      setTimeout(() => {
        readCommonStates();
      }, 1000);
    };

    window.addEventListener(
      "load",
      loadListener,
      false
    );
  }, []);

  useEffect(() => {
    if (linkTo) {
      DataStore.update((s) => {
        s.linkToFunc = linkTo;
      });

      global.linkToFunc = linkTo;
    }
  }, [linkTo]);

  useEffect(() => {
    if (selectedOutlet === null) {
      readStates();
    }

    readCommonStates();
  }, [selectedOutlet]);

  const readStates = async () => {
    console.log('global.selectedoutlet = readStates (1) (==test==)');

    if (selectedOutlet === null) {
      console.log('global.selectedoutlet = readStates (2) (==test==)');

      // const commonStoreDataRaw = await AsyncStorage.getItem("@commonStore");
      const commonStoreDataRaw = await idbGet('@commonStore');
      if (commonStoreDataRaw !== undefined) {
        console.log('global.selectedoutlet = readStates (3) (==test==)');

        const commonStoreData = JSON.parse(commonStoreDataRaw);

        const latestOutletId = await AsyncStorage.getItem("latestOutletId");

        console.log('latestOutletId');
        console.log(latestOutletId);
        console.log('commonStoreData.selectedOutlet');
        console.log(commonStoreData.selectedOutlet);

        if (
          commonStoreData.selectedOutlet &&
          latestOutletId === commonStoreData.selectedOutlet.uniqueId
        ) {
          // check if it's the same outlet user scanned

          console.log('global.selectedoutlet = readStates (4) (==test==)');

          if (isPlacingReservation) {
          } else {
            // if (
            //   commonStoreData.orderType === ORDER_TYPE.DINEIN 
            //   // 2022-10-08 - Try to disable this
            //   // &&
            //   // commonStoreData.userCart.uniqueId === undefined
            // ) {
            //   // logout the user

            //   linkTo && linkTo(`${prefix}/scan`);
            // }
          }

          console.log('global.selectedoutlet = commonStoreData.selectedOutlet (3) (==test==)');

          global.selectedOutlet = commonStoreData.selectedOutlet;

          CommonStore.replace(commonStoreData);

          // const userStoreDataRaw = await AsyncStorage.getItem("@userStore");
          const userStoreDataRaw = await idbGet('@userStore');
          if (userStoreDataRaw !== undefined) {
            const userStoreData = JSON.parse(userStoreDataRaw);

            UserStore.replace(userStoreData);
          }

          // const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
          const dataStoreDataRaw = await idbGet('@dataStore');
          if (dataStoreDataRaw !== undefined) {
            const dataStoreData = JSON.parse(dataStoreDataRaw);

            DataStore.replace(dataStoreData);
            // DataStore.replace({
            //   ...dataStoreData,
            //   ...dataStoreData.linkToFunc !== undefined && {
            //     linkToFunc: dataStoreData.linkToFunc,
            //   },
            // });
          }

          // const tableStoreDataRaw = await AsyncStorage.getItem("@tableStore");
          const tableStoreDataRaw = await idbGet('@tableStore');
          if (tableStoreDataRaw !== undefined) {
            const tableStoreData = JSON.parse(tableStoreDataRaw);

            TableStore.replace(tableStoreData);
          }

          // const paymentStoreDataRaw = await AsyncStorage.getItem("@paymentStore");
          const paymentStoreDataRaw = await idbGet('@paymentStore');
          if (paymentStoreDataRaw !== undefined) {
            const paymentStoreData = JSON.parse(paymentStoreDataRaw);

            PaymentStore.replace(paymentStoreData);
          }

          // const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
          // if (dataStoreDataRaw !== undefined) {
          //   const dataStoreData = JSON.parse(dataStoreDataRaw);

          //   DataStore.replace(dataStoreData);
          // }
        }
      }
    }
  };

  const readCommonStates = async () => {
    // if (!linkToFunc) {
    //   const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
    //   if (dataStoreDataRaw !== undefined) {
    //     const dataStoreData = JSON.parse(dataStoreDataRaw);
    //     DataStore.replace(dataStoreData);
    //   }
    // }
  };

  // const userCart = CommonStore.useState(s => s.userCart);
  // const selectedOutletTableId = CommonStore.useState(s => s.selectedOutletTableId);

  // useEffect(() => {
  //   syncWithDineInCart();
  // }, [selectedOutletTableId, userCart]);

  useEffect(() => {
    if (cartItems.length > 0) {
      setCartIcon(true);
    } else {
      setCartIcon(false);
    }
  }, [cartItems.length]);

  const setState = () => { };

  useEffect(() => {
    categoryFunc();
    refresh();
    refreshMenu();

    // ApiClient.GET(API.outlet2 + outletData.id).then((result) => {
    //   // console.log(result)
    //   setState({ outletData: result })

    //   refreshMenu();
    // });
    // menu item
    // ApiClient.GET(API.merchantMenu + outletData.id).then((result) => {
    //   // console.log(result)
    //   if (result.length > 0) {
    //     setState({ category: result[0].category, menu: result[0].items })
    //   }
    //   setState({ outletMenu: result })
    // });

    // setInterval(() => {
    //   cartCount();
    //   getCartItem();
    // }, 1000);
    // setInterval(() => {
    //   cartCount();
    //   getCartItem();
    // }, 2500);

    window.history.replaceState('', '', '');
  }, []);

  ////////////////////////////////////////////

  // 2023-03-09 - No need first

  // useEffect(() => {
  //   return () => {
  //     CommonStore.update((s) => {
  //       // s.selectedOutletItems = [];
  //       // s.selectedOutletItemCategories = [];

  //       // s.scannedQrData = null;

  //       // s.selectedOutlet = null;

  //       ////////////////////////////////////////

  //       // 2023-02-24 = no need this first

  //       // s.orderType = isPlacingReservation
  //       //   ? ORDER_TYPE.DINEIN
  //       //   : ORDER_TYPE.DELIVERY;

  //       ////////////////////////////////////////

  //       // s.orderType = ORDER_TYPE.DELIVERY;

  //       s.selectedOutletTableId = "";
  //     });
  //   };
  // }, [
  //   // scannedQrData,

  //   isPlacingReservation,
  // ]);

  // useEffect(() => {
  //   console.log("scannedQrData");
  //   console.log(scannedQrData);

  //   if (scannedQrData !== null) {
  //     return () => {
  //       proceedDeleteUserCart();

  //       CommonStore.update((s) => {
  //         // s.selectedOutletItems = [];
  //         // s.selectedOutletItemCategories = [];

  //         s.scannedQrData = null;

  //         s.selectedOutlet = null;

  //         ////////////////////////////////////////

  //         // 2023-02-24 = no need this first

  //         // s.orderType = isPlacingReservation
  //         //   ? ORDER_TYPE.DINEIN
  //         //   : ORDER_TYPE.DELIVERY;

  //         ////////////////////////////////////////ERY;

  //         s.selectedOutletTableId = "";
  //       });
  //     };
  //   }
  // }, [scannedQrData, isPlacingReservation]);

  ////////////////////////////////////////////

  const proceedDeleteUserCart = async () => {
    // await deleteUserCart();
  };

  const deleteUserCart = async () => {
    if (userCart.uniqueId) {
      const body = {
        userCartId: userCart.uniqueId,
      };

      ApiClient.POST(API.deleteUserCart, body).then((result) => {
        if (result && result.status === "success") {
          console.log("ok");
        }
      });
    }
  };

  // function here

  // const syncWithDineInCart = async () => {
  //   if (selectedOutletTableId.length > 0) {
  //     await clearCartItems();

  //     if (userCart.tableId === selectedOutletTableId) {
  //       CommonStore.update(s => {
  //         s.cartItems = userCart.cartItems;
  //       });
  //     }
  //   }
  // };

  const getCartItem = () => {
    setState({ cartItem: Cart.getCartItem() });
    // console.log(cartItem)
  };

  const cartCount = () => {
    if (Cart.getCartItem() !== null) {
      if (Cart.getCartItem().length > 0) {
        setState({ cartIcon: true });
      } else {
        setState({ cartIcon: false });
      }
    } else {
      setState({ cartIcon: false });
    }
  };

  const goToCart = () => {
    // if (Cart.getCartItem().length > 0) {
    //   if (navFrom == "TAKEAWAY") {
    //     props.navigation.navigate('Cart', {
    //       screen: "Cart", params: {
    //         // test: test,
    //         outletData: selectedOutlet, navFrom: navFrom
    //       }
    //     })
    //   }
    //   else {
    //     props.navigation.navigate("Cart", {
    //       // test: test,
    //       outletData: selectedOutlet });
    //   }
    // } else {
    //   window.confirm("Info", "No item in your cart at the moment", [
    //     { text: "OK", onPress: () => { } }
    //   ],
    //     { cancelable: false })
    // }
  };

  const onCartClicked = () => {
    // if (cartItems.length > 0) {
    //   if (navFrom == "TAKEAWAY") {
    //     // props.navigation.navigate('Cart', { screen: "Cart", params: { test: test, outletData: outletData, navFrom: navFrom } })
    //     props.navigation.navigate('Cart', {
    //       // test: test,
    //       outletData: selectedOutlet, navFrom: navFrom });
    //   }
    //   else {
    //     props.navigation.navigate("Cart", {
    //       // test: test,
    //       outletData: selectedOutlet });
    //   }
    // } else {
    //   window.confirm("Info", "No item in your cart at the moment", [
    //     { text: "OK", onPress: () => { } }
    //   ],
    //     { cancelable: false })
    // }
  };

  const categoryFunc = () => {
    // ApiClient.GET(API.activeMenu + outletData.id).then((result) => {
    //   const tmpCategories = {};
    //   for (const category of result) {
    //     const categoryName = category.name
    //     const categoryId = category.id
    //     if (!tmpCategories[categoryName]) {
    //       tmpCategories[categoryName] = {
    //         label: categoryName,
    //         value: categoryId,
    //       };
    //     }
    //   }
    //   const categories = Object.values(tmpCategories);
    //   setState({ categoryOutlet: categories, category: categories[0].label });
    // }).catch(err => {
    //   console.log("Error")
    //   console.log(err)
    // });
  };

  const refresh = () => {
    // ApiClient.GET(API.merchantMenu + outletData.id).then((result) => {
    //   if (result != undefined && result.length > 0) {
    //     var productListRaw = [];
    //     result.forEach((element) => {
    //       console.log(element.items);
    //       productListRaw = productListRaw.concat(element.category);
    //       const activeItem = productListRaw.filter(item => item.active == 1)
    //       setState({ productList: productListRaw, productList2: activeItem, }, () => { });
    //     });
    //   }
    // });
  };

  const refreshMenu = () => {
    // ApiClient.GET(API.activeMenu + outletData.id).then((result) => {
    //   const category = result.filter(i => i.name == category)
    //   category.map(i => {
    //     // const newList = []
    //     // for (const item of i.items){
    //     //   if(item.name !== ""){
    //     //     newList.push(item)
    //     //   }
    //     // }
    //     // setState({ currentMenu: newList })
    //     setState({ currentMenu: i.items })
    //   })
    //   // }
    //   // }else{
    //   //   setState({ currentMenu: result });
    //   // }
    // });
  };

  // const refresh = () => {
  //   setState({ refresh: true });
  // }

  const renderMenu = ({ item, index }) => {
    var quantity = 0;
    //const cartItem = cartItem.find(obj => obj.itemId === item.id);

    const itemsInCart = cartItems.filter((obj) => obj.itemId === item.uniqueId);
    if (itemsInCart) {
      for (const obj of itemsInCart) {
        quantity += parseInt(obj.quantity);
      }
    }

    var itemNameFontSize = 15;

    if (windowWidth <= 360) {
      itemNameFontSize = 13;
      //console.log(windowWidth)
    }

    const itemNameTextScale = {
      fontSize: itemNameFontSize,
    };

    let excludePromoVoucher = false;
    let outletCategory = selectedOutletItemCategoriesDict[item.categoryId];
    if (
      (item && item.excludePromoVoucher)
      ||
      (outletCategory && outletCategory.excludePromoVoucher)
    ) {
      excludePromoVoucher = true;
    }

    var overrideCategoryPrice = undefined;
    if (
      !excludePromoVoucher &&
      selectedOutletItemCategoriesDict[item.categoryId] &&
      overrideCategoryPriceNameDict[
      selectedOutletItemCategoriesDict[item.categoryId].name
      ] !== undefined
    ) {
      overrideCategoryPrice =
        overrideCategoryPriceNameDict[
          selectedOutletItemCategoriesDict[item.categoryId].name
        ].overridePrice;
    }

    // if (item.name === 'The Teddy Brunch (Chicken)' || item.name == 'All Day Big Breakfast') {
    //   console.log('stop');
    // }

    if (
      item.categoryId === selectedOutletItemCategory.uniqueId &&
      // item.categoryId === global.selectedOutletItemCategory.uniqueId &&
      (item.hideInOrderTypes && item.hideInOrderTypes.length > 0
        ? item.hideInOrderTypes.includes(orderType)
          ? false
          : true
        : true)
    ) {
      var amountOffCategory = undefined;
      if (
        !excludePromoVoucher &&
        selectedOutletItemCategoriesDict[item.categoryId] &&
        amountOffCategoryNameDict[
        selectedOutletItemCategoriesDict[item.categoryId].name
        ] !== undefined
      ) {
        amountOffCategory =
          amountOffCategoryNameDict[
          selectedOutletItemCategoriesDict[item.categoryId].name
          ];
      }

      var percentageOffCategory = undefined;
      if (
        !excludePromoVoucher &&
        selectedOutletItemCategoriesDict[item.categoryId] &&
        percentageOffCategoryNameDict[
        selectedOutletItemCategoriesDict[item.categoryId].name
        ] !== undefined
      ) {
        percentageOffCategory =
          percentageOffCategoryNameDict[
          selectedOutletItemCategoriesDict[item.categoryId].name
          ];
      }

      var pointsRedeemCategory = undefined;

      if (
        !excludePromoVoucher &&
        selectedOutletItemCategoriesDict[item.categoryId] &&
        pointsRedeemCategoryNameDict[
        selectedOutletItemCategoriesDict[item.categoryId].name
        ] !== undefined
      ) {
        pointsRedeemCategory =
          pointsRedeemCategoryNameDict[
          selectedOutletItemCategoriesDict[item.categoryId].name
          ];
      }

      var buy1Free1Category = undefined;

      if (
        !excludePromoVoucher &&
        selectedOutletItemCategoriesDict[item.categoryId] &&
        buy1Free1CategoryNameDict[
        selectedOutletItemCategoriesDict[item.categoryId].name
        ] !== undefined
      ) {
        buy1Free1Category =
          buy1Free1CategoryNameDict[
          selectedOutletItemCategoriesDict[item.categoryId].name
          ];
      }

      var extraPrice = 0;
      if (
        orderType === ORDER_TYPE.DELIVERY &&
        selectedOutlet &&
        selectedOutlet.deliveryPrice
      ) {
        extraPrice = selectedOutlet.deliveryPrice;
      } else if (
        orderType === ORDER_TYPE.PICKUP &&
        selectedOutlet &&
        selectedOutlet.pickUpPrice
      ) {
        extraPrice = selectedOutlet.pickUpPrice;
      }

      if (orderType === ORDER_TYPE.DELIVERY) {
        extraPrice = item.deliveryCharges || 0;

        if (
          extraPrice &&
          item.deliveryChargesType === CHARGES_TYPE.PERCENTAGE_BASED
        ) {
          extraPrice = (item.price * extraPrice) / 100;
        }

        if (!item.deliveryChargesActive) {
          extraPrice = 0;
        }
      }

      if (orderType === ORDER_TYPE.PICKUP) {
        extraPrice = item.pickUpCharges || 0;

        if (
          extraPrice &&
          item.pickUpChargesType === CHARGES_TYPE.PERCENTAGE_BASED
        ) {
          extraPrice = (item.price * extraPrice) / 100;
        }

        if (!item.pickUpChargesActive) {
          extraPrice = 0;
        }
      }

      if (item.isLoadingIcon) {
        console.log('loadingIcon');
        console.log(`scrollBottomSpinner-${item.categoryId}`);
      }

      const isNotAvailable = !item.isActive ||
        !(item.isAvailableDayActive
          ? (item.effectiveTypeOptions.includes(effectiveDays) &&
            item.effectiveStartTime && item.effectiveEndTime &&
            moment().isSameOrAfter(
              moment(item.effectiveStartTime)
                .year(moment().year())
                .month(moment().month())
                .date(moment().date())
            )
            &&
            moment().isBefore
              (moment(item.effectiveEndTime)
                .year(moment().year())
                .month(moment().month())
                .date(moment().date())
              ))
          : true) ||
        !(item.isOnlineMenu !== undefined
          ? item.isOnlineMenu
          : true) ||
        !(item.isStockCountActive !== undefined &&
          item.isStockCountActive !== false &&
          item.stockCount !== undefined &&
          item.toSellIgnoreStock !== undefined
          ? item.isStockCountActive &&
          item.stockCount > 0 &&
          (item.toSellIgnoreStock !== undefined
            ? item.toSellIgnoreStock
            : true)
          : true);

      let specialTag = '';
      if (item.specialTags && item.specialTags.length > 0 &&
        typeof item.specialTags[0] === 'string') {
        specialTag = item.specialTags[0];
      }

      return (
        <>
          {
            item.isLoadingIcon
              ?
              <></>
              // <TouchableOpacity
              //   disabled={item.isFakeItem}
              //   onPress={async () => {
              //   }}
              //   style={{
              //     // opacity: item.isFakeItem ? 0 : 100,
              //   }}
              // >
              //   <View
              //     id={`scrollBottomSpinner-${item.categoryId}`}
              //     style={{
              //       flexDirection: "row",
              //       // paddingHorizontal: 20,
              //       // paddingBottom: 15,
              //       // paddingTop: 10,
              //       display: "flex",
              //       justifyContent: "center",
              //       alignItems: 'center',
              //       alignSelf: 'center',
              //       // flexDirection: "row",
              //       backgroundColor: "white",
              //       borderRadius: 40,
              //       shadowOffset: {
              //         width: 0,
              //         height: 0,
              //       },
              //       shadowOpacity: 0.45,
              //       shadowRadius: 3.22,
              //       elevation: 1,
              //       width: 40,
              //       height: 40,

              //       transform: rotateScrollSpinnerStyle,
              //       transition: 'transform 0.3s',

              //       // WebkitTransform: 'translate3d(0,0,0)',
              //       // WebkitBackfaceVisibility: 'hidden',
              //       // WebkitTransformStyle: 'preserve-3d',
              //     }}
              //   >
              //     <MaterialCommunityIcons
              //       color={Colors.primaryColor}
              //       name={"refresh"}
              //       size={30}
              //     />
              //   </View>
              // </TouchableOpacity>
              :
              <TouchableOpacity
                disabled={item.isFakeItem}
                onPress={async () => {
                  if (!item.isFakeItem) {
                    if (global.outletName) {
                      logEvent(global.analytics, ANALYTICS.WO_OUTLET_MENU_ITEM_CLICK, {
                        eventNameParsed: ANALYTICS_PARSED.WO_OUTLET_MENU_ITEM_CLICK,

                        outletName: global.outletName ? `${global.outletName} (Web)` : '',

                        webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
                      });
                    }

                    if (checkCartOutlet()) {
                      // setState({ cartWarning: true, })

                      setTempItem(item);

                      setCartWarning(true);
                    } else {
                      if (
                        item.isActive &&
                        (item.isAvailableDayActive
                          ? (item.effectiveTypeOptions.includes(effectiveDays) &&
                            item.effectiveStartTime && item.effectiveEndTime &&
                            moment().isSameOrAfter(
                              moment(item.effectiveStartTime)
                                .year(moment().year())
                                .month(moment().month())
                                .date(moment().date())
                            )
                            &&
                            moment().isBefore
                              (moment(item.effectiveEndTime)
                                .year(moment().year())
                                .month(moment().month())
                                .date(moment().date())
                              ))
                          : true) &&
                        (item.isOnlineMenu !== undefined ? item.isOnlineMenu : true) &&
                        (item.isStockCountActive !== undefined &&
                          item.isStockCountActive !== false &&
                          item.stockCount !== undefined &&
                          item.toSellIgnoreStock !== undefined
                          ? item.isStockCountActive &&
                          item.stockCount > 0 &&
                          (item.toSellIgnoreStock !== undefined
                            ? item.toSellIgnoreStock
                            : true)
                          : true)
                      ) {
                        CommonStore.update((s) => {
                          s.selectedOutletItem = item;
                          s.currPage = 'Product Details - KooDoo Web Order';

                          s.selectedOutletItemAddOn = {};
                          s.selectedOutletItemAddOnChoice = {};
                          s.selectedOutletItemAddOnOi = {};

                          // s.selectedAddOnIdForChoiceQtyDict = {};
                        });

                        // const subdomain = await AsyncStorage.getItem("latestSubdomain");

                        // if (!subdomain) {
                        //   linkTo && linkTo(`${prefix}/outlet/menu/item`);
                        // } else {
                        //   linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu/item`);
                        // }

                        // linkTo && linkTo(`${prefix}/outlet/menu/item`);
                        if (isMobile() && selectedOutlet && true) {
                          CommonStore.update((s) => {
                            s.menuItemDetailModal = true;
                          });

                          global.menuItemDetailModal = true;

                          window.history.pushState({
                            page: 'menuItemDetailModal',
                          }, '');

                          CommonStore.update(s => {
                            s.currPageIframe = 'MenuItemDetails';
                          });

                          // window.history.pushState(null, '', window.location.href);
                          // window.history.forward();

                          // const subdomain = await AsyncStorage.getItem("latestSubdomain");

                          // if (!subdomain) {
                          //   linkTo && linkTo(`${prefix}/outlet/menu/item`);
                          // } else {
                          //   linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu/item`);
                          // }

                          // props.navigation.navigate(
                          //   "Product Details - KooDoo Web Order",
                          //   {
                          //     refresh: refresh.bind(this),
                          //     menuItem: item,
                          //     outletData: selectedOutlet,
                          //   }
                          // );
                        }
                        else {
                          props.navigation.navigate(
                            "Product Details - KooDoo Web Order",
                            {
                              refresh: refresh.bind(this),
                              menuItem: item,
                              outletData: selectedOutlet,
                            }
                          );
                        }
                      } else {
                        // window.confirm(
                        //   'Info',
                        //   'Sorry, this product is not available for now.',
                        // );

                        CommonStore.update((s) => {
                          s.alertObj = {
                            title: "Info",
                            message: "Sorry, this product is not available for now.",
                          };
                        });
                      }
                    }
                  }
                }}
                style={{
                  opacity: item.isFakeItem ? 0 : 100,
                }}
              >
                <View
                  style={{
                    flexDirection: "row",
                    paddingHorizontal: 20,
                    paddingBottom: 15,
                    paddingTop: 10,
                    display: "flex",
                    justifyContent: "space-between",
                    flexDirection: "row",
                  }}
                >
                  <View
                    style={{
                      flexDirection: "row",
                      alignContent: "center",
                      alignItems: "center",
                      width: isMobile() ? "75%" : windowWidth * 0.55,
                      display: "flex",
                      justifyContent: "flex-start",
                      // backgroundColor: 'blue',
                    }}
                  >
                    <View>
                      <View
                        style={[
                          {
                            backgroundColor: Colors.secondaryColor,
                            // width: 60,
                            // height: 60,
                            width: isMobile()
                              ? windowWidth * 0.22
                              : windowWidth * 0.05,
                            height: isMobile()
                              ? windowWidth * 0.22
                              : windowWidth * 0.05,
                            borderRadius: 10,
                          },
                          item.image
                            ? {}
                            : {
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                            },
                        ]}
                      >
                        {item.image ? (
                          // <Image source={{ uri: item.image }} style={{
                          //   width: windowWidth * 0.22,
                          //   height: windowWidth * 0.22,
                          //   borderRadius: 10
                          // }} />
                          <AsyncImage
                            source={{ uri: item.image }}
                            item={item}
                            style={{
                              width: isMobile()
                                ? windowWidth * 0.22
                                : windowWidth * 0.05,
                              height: isMobile()
                                ? windowWidth * 0.22
                                : windowWidth * 0.05,
                              borderRadius: 10,
                            }}
                          />
                        ) : (
                          // <Ionicons name="fast-food-outline" size={50} />
                          <View
                            style={{
                              width: isMobile()
                                ? windowWidth * 0.22
                                : windowWidth * 0.05,
                              height: isMobile()
                                ? windowWidth * 0.22
                                : windowWidth * 0.05,
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                            }}
                          >
                            <Ionicons
                              name="fast-food-outline"
                              // size={45}
                              size={
                                isMobile() ? windowWidth * 0.1 : windowWidth * 0.02
                              }
                            />
                          </View>
                        )}

                        {(isNotAvailable || specialTag) ? (
                          <View
                            style={{
                              position: "absolute",
                              zIndex: 3,
                            }}
                          >
                            <View
                              style={{
                                // width: 120,
                                width: isMobile()
                                  ? windowWidth * 0.25
                                  : windowWidth * 0.06,
                                left: isMobile()
                                  ? -windowWidth * 0.03
                                  : -windowWidth * 0.01,
                                padding: 0,
                                paddingLeft: isMobile()
                                  ? windowWidth * 0.02
                                  : windowWidth * 0.005,
                                justifyContent: "center",
                                alignItems: "center",
                                backgroundColor: isNotAvailable ? Colors.tabRed : Colors.primaryColor,
                                height: 20,
                                borderTopRightRadius: 10,
                                borderBottomRightRadius: 3,

                                ...(!item.image && {
                                  left: isMobile()
                                    ? -windowWidth * 0.015
                                    : -windowWidth * 0.005,
                                  bottom: isMobile()
                                    ? windowWidth * 0.074
                                    : windowWidth * 0.014,
                                }),
                              }}
                            >
                              <Text
                                testID="notAvilableWording"
                                style={{
                                  color: "#FFF",
                                  fontFamily: "NunitoSans-Bold",
                                  fontSize: 10,
                                  bottom: 1,
                                }}
                              >
                                {isNotAvailable ? 'Not available' : specialTag}
                              </Text>
                            </View>
                            <View
                              style={{
                                left: isMobile()
                                  ? -windowWidth * 0.03
                                  : -windowWidth * 0.01,
                                bottom: "1%",
                                width: 0,
                                height: 0,
                                backgroundColor: "transparent",
                                borderStyle: "solid",
                                borderRightWidth: isMobile()
                                  ? windowWidth * 0.03
                                  : windowWidth * 0.01,
                                borderTopWidth: isMobile()
                                  ? windowWidth * 0.03
                                  : windowWidth * 0.01,
                                borderRightColor: "transparent",
                                borderTopColor: isNotAvailable ? Colors.tabRed : Colors.primaryColor,
                                transform: [{ rotate: "90deg" }],

                                ...(!item.image && {
                                  left: isMobile()
                                    ? -windowWidth * 0.015
                                    : -windowWidth * 0.005,
                                  bottom: isMobile()
                                    ? windowWidth * 0.074
                                    : windowWidth * 0.014,
                                }),
                              }}
                            />
                          </View>
                        ) : (
                          <></>
                        )}
                      </View>

                      {pointsRedeemItemSkuDict[item.sku] !== undefined ||
                        pointsRedeemCategory !== undefined ? (
                        <View
                          style={{
                            marginTop: 5,
                          }}
                        >
                          <Text
                            style={{
                              color: Colors.primaryColor,
                              fontFamily: "NunitoSans-SemiBold",
                              fontSize: 12,
                              textAlign: "center",
                            }}
                          >
                            {pointsRedeemItemSkuDict[item.sku] !== undefined
                              ? `${pointsRedeemItemSkuDict[item.sku]
                                .conversionPointsFrom
                              } Points to RM${pointsRedeemItemSkuDict[item.sku]
                                .conversionCurrencyTo
                              }`
                              : `${pointsRedeemCategory.conversionPointsFrom} Points to RM${pointsRedeemCategory.conversionCurrencyTo}`}
                          </Text>
                        </View>
                      ) : (
                        <></>
                      )}

                      {!excludePromoVoucher && (buy1Free1ItemSkuDict[item.sku] !== undefined ||
                        buy1Free1Category !== undefined) ? (
                        <View
                          style={{
                            marginTop: 5,
                          }}
                        >
                          <Text
                            style={{
                              color: Colors.descriptionColor,
                              fontFamily: "NunitoSans-SemiBold",
                              fontSize: 12,
                              textAlign: "center",
                            }}
                          >
                            {`Bundle Deal`}
                            {/* {buy1Free1ItemSkuDict[item.sku] !== undefined
                        ? `Buy ${
                            buy1Free1ItemSkuDict[item.sku].buyAmount
                          } Free ${buy1Free1ItemSkuDict[item.sku].getAmount}`
                        : `Buy ${buy1Free1Category.buyAmount} Free ${buy1Free1Category.getAmount}`} */}
                          </Text>
                        </View>
                      ) : (
                        <></>
                      )}
                    </View>

                    <View
                      style={{
                        marginLeft: 15,
                        // flexDirection: 'row',
                        // flexShrink: 1,
                        width: isMobile() ? "55%" : windowWidth * 0.45,
                        // backgroundColor: 'red',
                      }}
                    >
                      <Text
                        testID={`productName-${index}`}
                        // numberOfLines={1}
                        style={[
                          itemNameTextScale,
                          {
                            //fontSize: 16,
                            textTransform: "uppercase",
                            fontFamily: "NunitoSans-Bold",
                            // flexWrap: 'wrap',
                            // flex: 1,
                            // flexShrink: 1,
                            // width: '100%',
                          },
                        ]}
                        numberOfLines={3}
                      >
                        {item.dpName ? item.dpName : (item.name ? item.name : '')}
                      </Text>
                      <Text
                        testID={`productName-${index}`}
                        // numberOfLines={1}
                        style={[
                          itemNameTextScale,
                          {
                            fontSize: 14,
                            textTransform: "uppercase",
                            fontFamily: "NunitoSans-Regular",
                            // flexWrap: 'wrap',
                            // flex: 1,
                            // flexShrink: 1,
                            // width: '100%',
                          },
                        ]}
                        numberOfLines={3}
                      >
                        {item.dpName ? item.dpName : ''}
                      </Text>

                      <View
                        style={{
                          flexDirection: "row",
                          alignItems: "center",
                        }}
                      >
                        <Text
                          style={{
                            color: Colors.primaryColor,
                            fontFamily: "NunitoSans-Bold",
                            paddingTop: 5,
                            fontSize: 16,
                            textDecorationLine:
                              !excludePromoVoucher &&
                                (
                                  overrideItemPriceSkuDict[item.sku] !== undefined ||
                                  overrideCategoryPrice !== undefined
                                )
                                ? "line-through"
                                : "none",
                          }}
                        >
                          RM{parseFloat(extraPrice + item.price).toFixed(2)}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[item.unitType]}` : ''}
                        </Text>

                        {!excludePromoVoucher && (overrideItemPriceSkuDict[item.sku] !== undefined ||
                          overrideCategoryPrice !== undefined) ? (
                          <Text
                            style={{
                              color: Colors.secondaryColor,
                              fontFamily: "NunitoSans-Bold",
                              paddingTop: 5,
                              fontSize: 16,
                              marginLeft: 5,
                            }}
                          >
                            RM
                            {(overrideItemPriceSkuDict[item.sku] &&
                              overrideItemPriceSkuDict[item.sku].overridePrice !==
                              undefined)
                              ? parseFloat(
                                overrideItemPriceSkuDict[item.sku].overridePrice
                              ).toFixed(2)
                              : parseFloat(overrideCategoryPrice).toFixed(2)}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[item.unitType]}` : ''}
                          </Text>
                        ) : (
                          <></>
                        )}
                      </View>

                      <View
                        style={{
                          flexDirection: "row",
                          alignItems: "center",
                        }}
                      >
                        {!excludePromoVoucher && (amountOffItemSkuDict[item.sku] !== undefined ||
                          amountOffCategory !== undefined) ? (
                          <Text
                            style={{
                              color: Colors.descriptionColor,
                              fontFamily: "NunitoSans-SemiBold",
                              paddingTop: 5,
                              fontSize: 14,
                              // marginLeft: 5,
                            }}
                          >
                            {amountOffItemSkuDict[item.sku] !== undefined
                              ? `Buy ${amountOffItemSkuDict[item.sku].quantityMin
                              } pcs to enjoy RM${amountOffItemSkuDict[
                                item.sku
                              ].amountOff.toFixed(2)} off\n(Min purchases: RM${amountOffItemSkuDict[item.sku].priceMin
                              })`
                              : `Buy ${amountOffCategory.quantityMin} pcs to enjoy RM${amountOffCategory.amountOff.toFixed(
                                0
                              )} off\n(Min purchases: RM${amountOffCategory.priceMin
                              })`}
                          </Text>
                        ) : (
                          <></>
                        )}
                      </View>

                      <View
                        style={{
                          flexDirection: "row",
                          alignItems: "center",
                        }}
                      >
                        {!excludePromoVoucher && (percentageOffItemSkuDict[item.sku] !== undefined ||
                          percentageOffCategory !== undefined) ? (
                          <Text
                            style={{
                              color: Colors.descriptionColor,
                              fontFamily: "NunitoSans-SemiBold",
                              paddingTop: 5,
                              fontSize: 14,
                              // marginLeft: 5,
                            }}
                          >
                            {percentageOffItemSkuDict[item.sku] !== undefined
                              ? `Buy ${percentageOffItemSkuDict[item.sku].quantityMin
                              } pcs to enjoy ${percentageOffItemSkuDict[
                                item.sku
                              ].percentageOff.toFixed(0)}% off\n(Min purchases: RM${percentageOffItemSkuDict[item.sku].priceMin
                              })`
                              : `Buy ${percentageOffCategory.quantityMin} pcs to enjoy ${percentageOffCategory.percentageOff.toFixed(
                                0
                              )}% off\n(Min purchases: RM${percentageOffCategory.priceMin
                              })`}
                          </Text>
                        ) : (
                          <></>
                        )}
                      </View>
                    </View>
                  </View>

                  <View
                    style={{
                      flexDirection: "row",
                      // width: "20%",
                      // marginLeft: 60
                      // backgroundColor: 'red',
                      ...(!isMobile() && {
                        // width: windowWidth,
                        height: 26,
                        // right: 0,
                        alignSelf: "center",
                      }),
                    }}
                  >
                    <View
                      style={{
                        // backgroundColor: "#e3e1e1",
                        backgroundColor: Colors.primaryColor,

                        // width: 67,
                        // height: 24,

                        width: 68,
                        height: 26,

                        // paddingVertical: 4,
                        // paddingHorizontal: 20,

                        borderRadius: 10,
                        justifyContent: "center",
                        alignSelf: "center",
                      }}
                    >
                      <TouchableOpacity
                        disabled={item.isFakeItem}
                        onPress={async () => {
                          /* if (checkCartOutlet()) {
                            // setState({
                            //   cartWarning: true,
                            //   cartProceed: item
                            // })
       
                            setTempItem(item);
       
                            setCartWarning(true);
                          } else {
                            if (item.isActive) {
                              CommonStore.update((s) => {
                                s.selectedOutletItem = item;
                              });
       
                              // const subdomain = await AsyncStorage.getItem(
                              //   "latestSubdomain"
                              // );
       
                              // if (!subdomain) {
                              //   linkTo && linkTo(`${prefix}/outlet/menu/item`);
                              // } else {
                              //   linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu/item`);
                              // }
       
                              // linkTo && linkTo(`${prefix}/outlet/menu/item`);
       
                              props.navigation.navigate("MenuItemDetails", {
                                refresh: refresh.bind(this),
                                menuItem: item,
                                outletData: selectedOutlet,
                              });
                            } else {
                              console.log(orderType);
                              window.confirm(
                                "Sorry, this product is not available at the moment."
                              );
                            }
                          }
                        }} */
                          if (checkCartOutlet()) {
                            // setState({ cartWarning: true, })

                            setTempItem(item);

                            setCartWarning(true);
                          } else {
                            if (
                              item.isActive &&
                              (item.isAvailableDayActive
                                ? (item.effectiveTypeOptions.includes(effectiveDays) &&
                                  item.effectiveStartTime && item.effectiveEndTime &&
                                  moment().isSameOrAfter(
                                    moment(item.effectiveStartTime)
                                      .year(moment().year())
                                      .month(moment().month())
                                      .date(moment().date())
                                  )
                                  &&
                                  moment().isBefore
                                    (moment(item.effectiveEndTime)
                                      .year(moment().year())
                                      .month(moment().month())
                                      .date(moment().date())
                                    ))
                                : true) &&
                              (item.isOnlineMenu !== undefined
                                ? item.isOnlineMenu
                                : true) &&
                              (item.isStockCountActive !== undefined &&
                                item.isStockCountActive !== false &&
                                item.stockCount !== undefined &&
                                item.toSellIgnoreStock !== undefined
                                ? item.isStockCountActive &&
                                item.stockCount > 0 &&
                                (item.toSellIgnoreStock !== undefined
                                  ? item.toSellIgnoreStock
                                  : true)
                                : true)
                            ) {
                              CommonStore.update((s) => {
                                s.selectedOutletItem = item;

                                s.selectedOutletItemAddOn = {};
                                s.selectedOutletItemAddOnChoice = {};
                                s.selectedOutletItemAddOnOi = {};

                                // s.selectedAddOnIdForChoiceQtyDict = {};
                              });

                              // const subdomain = await AsyncStorage.getItem("latestSubdomain");

                              // if (!subdomain) {
                              //   linkTo && linkTo(`${prefix}/outlet/menu/item`);
                              // } else {
                              //   linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu/item`);
                              // }

                              // linkTo && linkTo(`${prefix}/outlet/menu/item`);
                              if (isMobile() && selectedOutlet && true) {
                                CommonStore.update((s) => {
                                  s.menuItemDetailModal = true;
                                });

                                global.menuItemDetailModal = true;

                                window.history.pushState({
                                  page: 'menuItemDetailModal',
                                }, '');

                                // window.history.pushState(null, '', window.location.href);
                                // window.history.forward();

                                // const subdomain = await AsyncStorage.getItem("latestSubdomain");

                                // if (!subdomain) {
                                //   linkTo && linkTo(`${prefix}/outlet/menu/item`);
                                // } else {
                                //   linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu/item`);
                                // }

                                // props.navigation.navigate(
                                //   "Product Details - KooDoo Web Order",
                                //   {
                                //     refresh: refresh.bind(this),
                                //     menuItem: item,
                                //     outletData: selectedOutlet,
                                //   }
                                // );
                              }
                              else {
                                props.navigation.navigate(
                                  "Product Details - KooDoo Web Order",
                                  {
                                    refresh: refresh.bind(this),
                                    menuItem: item,
                                    outletData: selectedOutlet,
                                  }
                                );
                              }
                            } else {
                              // window.confirm(
                              //   'Info',
                              //   'Sorry, this product is not available for now.',
                              // );

                              CommonStore.update((s) => {
                                s.alertObj = {
                                  title: "Info",
                                  message:
                                    "Sorry, this product is not available for now.",
                                };
                              });
                            }
                          }
                        }}
                      >
                        <Text
                          style={{
                            alignSelf: "center",
                            // color: "#8f8f8f",
                            color: Colors.whiteColor,
                            fontSize: 13,
                            fontFamily: "NunitoSans-Bold",
                          }}
                        >
                          {quantity > 0 ? quantity : "Add"}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              </TouchableOpacity>
          }
        </>
      );
    }
  };

  const renderAllMenu = ({ item }) => {
    var quantity = 0;
    //const cartItem = cartItem.find(obj => obj.itemId === item.id);

    const itemsInCart = cartItems.filter((obj) => obj.itemId === item.uniqueId);
    if (itemsInCart) {
      for (const obj of itemsInCart) {
        quantity += parseInt(obj.quantity);
      }
    }

    var itemNameFontSize = 15;

    if (windowWidth <= 360) {
      itemNameFontSize = 13;
      //console.log(windowWidth)
    }

    const itemNameTextScale = {
      fontSize: itemNameFontSize,
    };

    let excludePromoVoucher = false;
    let outletCategory = selectedOutletItemCategoriesDict[item.categoryId];
    if (
      (item && item.excludePromoVoucher)
      ||
      (outletCategory && outletCategory.excludePromoVoucher)
    ) {
      excludePromoVoucher = true;
    }

    var overrideCategoryPrice = undefined;
    if (
      !excludePromoVoucher &&
      selectedOutletItemCategoriesDict[item.categoryId] &&
      overrideCategoryPriceNameDict[
      selectedOutletItemCategoriesDict[item.categoryId].name
      ] !== undefined
    ) {
      overrideCategoryPrice =
        overrideCategoryPriceNameDict[
          selectedOutletItemCategoriesDict[item.categoryId].name
        ].overridePrice;
    }

    if (
      //item.categoryId === selectedOutletItemCategory.uniqueId &&
      (item.hideInOrderTypes && item.hideInOrderTypes.length > 0
        ? item.hideInOrderTypes.includes(orderType)
          ? false
          : true
        : true)
    ) {
      var amountOffCategory = undefined;
      if (
        !excludePromoVoucher &&
        selectedOutletItemCategoriesDict[item.categoryId] &&
        amountOffCategoryNameDict[
        selectedOutletItemCategoriesDict[item.categoryId].name
        ] !== undefined
      ) {
        amountOffCategory =
          amountOffCategoryNameDict[
          selectedOutletItemCategoriesDict[item.categoryId].name
          ];
      }

      var percentageOffCategory = undefined;
      if (
        !excludePromoVoucher &&
        selectedOutletItemCategoriesDict[item.categoryId] &&
        percentageOffCategoryNameDict[
        selectedOutletItemCategoriesDict[item.categoryId].name
        ] !== undefined
      ) {
        percentageOffCategory =
          percentageOffCategoryNameDict[
          selectedOutletItemCategoriesDict[item.categoryId].name
          ];
      }

      var pointsRedeemCategory = undefined;
      if (
        !excludePromoVoucher &&
        selectedOutletItemCategoriesDict[item.categoryId] &&
        pointsRedeemCategoryNameDict[
        selectedOutletItemCategoriesDict[item.categoryId].name
        ] !== undefined
      ) {
        pointsRedeemCategory =
          pointsRedeemCategoryNameDict[
          selectedOutletItemCategoriesDict[item.categoryId].name
          ];
      }

      var buy1Free1Category = undefined;
      if (
        !excludePromoVoucher &&
        selectedOutletItemCategoriesDict[item.categoryId] &&
        buy1Free1CategoryNameDict[
        selectedOutletItemCategoriesDict[item.categoryId].name
        ] !== undefined
      ) {
        buy1Free1Category =
          buy1Free1CategoryNameDict[
          selectedOutletItemCategoriesDict[item.categoryId].name
          ];
      }

      var extraPrice = 0;
      if (
        orderType === ORDER_TYPE.DELIVERY &&
        selectedOutlet &&
        selectedOutlet.deliveryPrice
      ) {
        extraPrice = selectedOutlet.deliveryPrice;
      } else if (
        orderType === ORDER_TYPE.PICKUP &&
        selectedOutlet &&
        selectedOutlet.pickUpPrice
      ) {
        extraPrice = selectedOutlet.pickUpPrice;
      }

      if (orderType === ORDER_TYPE.DELIVERY) {
        extraPrice = item.deliveryCharges || 0;

        if (
          extraPrice &&
          item.deliveryChargesType === CHARGES_TYPE.PERCENTAGE_BASED
        ) {
          extraPrice = (item.price * extraPrice) / 100;
        }

        if (!item.deliveryChargesActive) {
          extraPrice = 0;
        }
      }

      if (orderType === ORDER_TYPE.PICKUP) {
        extraPrice = item.pickUpCharges || 0;

        if (
          extraPrice &&
          item.pickUpChargesType === CHARGES_TYPE.PERCENTAGE_BASED
        ) {
          extraPrice = (item.price * extraPrice) / 100;
        }

        if (!item.pickUpChargesActive) {
          extraPrice = 0;
        }
      }

      const isNotAvailable = !item.isActive ||
        !(item.isAvailableDayActive
          ? (item.effectiveTypeOptions.includes(effectiveDays) &&
            item.effectiveStartTime && item.effectiveEndTime &&
            moment().isSameOrAfter(
              moment(item.effectiveStartTime)
                .year(moment().year())
                .month(moment().month())
                .date(moment().date())
            )
            &&
            moment().isBefore
              (moment(item.effectiveEndTime)
                .year(moment().year())
                .month(moment().month())
                .date(moment().date())
              ))
          : true) ||
        !(item.isOnlineMenu !== undefined
          ? item.isOnlineMenu
          : true) ||
        !(item.isStockCountActive !== undefined &&
          item.isStockCountActive !== false &&
          item.stockCount !== undefined &&
          item.toSellIgnoreStock !== undefined
          ? item.isStockCountActive &&
          item.stockCount > 0 &&
          (item.toSellIgnoreStock !== undefined
            ? item.toSellIgnoreStock
            : true)
          : true);

      let specialTag = '';
      if (item.specialTags && item.specialTags.length > 0 &&
        typeof item.specialTags[0] === 'string') {
        specialTag = item.specialTags[0];
      }

      return (
        <TouchableOpacity
          onPress={async () => {
            if (checkCartOutlet()) {
              // setState({ cartWarning: true, })

              setTempItem(item);

              setCartWarning(true);
            } else {
              if (
                item.isActive &&
                (item.isAvailableDayActive
                  ? (item.effectiveTypeOptions.includes(effectiveDays) &&
                    item.effectiveStartTime && item.effectiveEndTime &&
                    moment().isSameOrAfter(
                      moment(item.effectiveStartTime)
                        .year(moment().year())
                        .month(moment().month())
                        .date(moment().date())
                    )
                    &&
                    moment().isBefore
                      (moment(item.effectiveEndTime)
                        .year(moment().year())
                        .month(moment().month())
                        .date(moment().date())
                      ))
                  : true) &&
                (item.isOnlineMenu !== undefined ? item.isOnlineMenu : true) &&
                (item.isStockCountActive !== undefined &&
                  item.isStockCountActive !== false &&
                  item.stockCount !== undefined &&
                  item.toSellIgnoreStock !== undefined
                  ? item.isStockCountActive &&
                  item.stockCount > 0 &&
                  (item.toSellIgnoreStock !== undefined
                    ? item.toSellIgnoreStock
                    : true)
                  : true)
              ) {
                CommonStore.update((s) => {
                  s.selectedOutletItem = item;

                  s.selectedOutletItemAddOn = {};
                  s.selectedOutletItemAddOnChoice = {};
                  s.selectedOutletItemAddOnOi = {};

                  // s.selectedAddOnIdForChoiceQtyDict = {};
                });

                // const subdomain = await AsyncStorage.getItem("latestSubdomain");

                // if (!subdomain) {
                //   linkTo && linkTo(`${prefix}/outlet/menu/item`);
                // } else {
                //   linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu/item`);
                // }

                // linkTo && linkTo(`${prefix}/outlet/menu/item`);                

                if (isMobile() && selectedOutlet && true) {
                  CommonStore.update((s) => {
                    s.menuItemDetailModal = true;
                  });

                  global.menuItemDetailModal = true;

                  window.history.pushState({
                    page: 'menuItemDetailModal',
                  }, '');

                  CommonStore.update(s => {
                    s.currPageIframe = 'MenuItemDetails';
                  });

                  // window.history.pushState(null, '', window.location.href);
                  // window.history.forward();

                  // const subdomain = await AsyncStorage.getItem("latestSubdomain");

                  // if (!subdomain) {
                  //   linkTo && linkTo(`${prefix}/outlet/menu/item`);
                  // } else {
                  //   linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu/item`);
                  // }

                  // props.navigation.navigate(
                  //   "Product Details - KooDoo Web Order",
                  //   {
                  //     refresh: refresh.bind(this),
                  //     menuItem: item,
                  //     outletData: selectedOutlet,
                  //   }
                  // );
                }
                else {
                  props.navigation.navigate(
                    "Product Details - KooDoo Web Order",
                    {
                      refresh: refresh.bind(this),
                      menuItem: item,
                      outletData: selectedOutlet,
                    }
                  );
                }
              } else {
                // window.confirm(
                //   'Info',
                //   'Sorry, this product is not available for now.',
                // );

                CommonStore.update((s) => {
                  s.alertObj = {
                    title: "Info",
                    message: "Sorry, this product is not available for now.",
                  };
                });
              }
            }
          }}
        >
          <View
            style={{
              flexDirection: "row",
              paddingHorizontal: 20,
              paddingBottom: 15,
              paddingTop: 10,
              display: "flex",
              justifyContent: "space-between",
              flexDirection: "row",
            }}
          >
            <View
              style={{
                flexDirection: "row",
                alignContent: "center",
                alignItems: "center",
                width: isMobile() ? "75%" : windowWidth * 0.55,
                display: "flex",
                justifyContent: "flex-start",
                // backgroundColor: 'blue',
              }}
            >
              <View>
                <View
                  style={[
                    {
                      backgroundColor: Colors.secondaryColor,
                      // width: 60,
                      // height: 60,
                      width: isMobile()
                        ? windowWidth * 0.22
                        : windowWidth * 0.05,
                      height: isMobile()
                        ? windowWidth * 0.22
                        : windowWidth * 0.05,
                      borderRadius: 10,
                    },
                    item.image
                      ? {}
                      : {
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      },
                  ]}
                >
                  {item.image ? (
                    // <Image source={{ uri: item.image }} style={{
                    //   width: windowWidth * 0.22,
                    //   height: windowWidth * 0.22,
                    //   borderRadius: 10
                    // }} />
                    <AsyncImage
                      source={{ uri: item.image }}
                      item={item}
                      style={{
                        width: isMobile()
                          ? windowWidth * 0.22
                          : windowWidth * 0.05,
                        height: isMobile()
                          ? windowWidth * 0.22
                          : windowWidth * 0.05,
                        borderRadius: 10,
                      }}
                    />
                  ) : (
                    // <Ionicons name="fast-food-outline" size={50} />
                    <View
                      style={{
                        width: isMobile()
                          ? windowWidth * 0.22
                          : windowWidth * 0.05,
                        height: isMobile()
                          ? windowWidth * 0.22
                          : windowWidth * 0.05,
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      <Ionicons
                        name="fast-food-outline"
                        // size={45}
                        size={
                          isMobile() ? windowWidth * 0.1 : windowWidth * 0.02
                        }
                      />
                    </View>
                  )}

                  {(isNotAvailable || specialTag) ? (
                    <View
                      style={{
                        position: "absolute",
                        zIndex: 3,
                      }}
                    >
                      <View
                        style={{
                          // width: 120,
                          width: isMobile()
                            ? windowWidth * 0.25
                            : windowWidth * 0.06,
                          left: isMobile()
                            ? -windowWidth * 0.03
                            : -windowWidth * 0.01,
                          padding: 0,
                          paddingLeft: isMobile()
                            ? windowWidth * 0.02
                            : windowWidth * 0.005,
                          justifyContent: "center",
                          alignItems: "center",
                          backgroundColor: isNotAvailable ? Colors.tabRed : Colors.primaryColor,
                          height: 20,
                          borderTopRightRadius: 10,
                          borderBottomRightRadius: 3,

                          ...(!item.image && {
                            left: isMobile()
                              ? -windowWidth * 0.015
                              : -windowWidth * 0.005,
                            bottom: isMobile()
                              ? windowWidth * 0.074
                              : windowWidth * 0.014,
                          }),
                        }}
                      >
                        <Text
                          style={{
                            color: "#FFF",
                            fontFamily: "NunitoSans-Bold",
                            fontSize: 10,
                            bottom: 1,
                          }}
                        >
                          {isNotAvailable ? 'Not available' : specialTag}
                        </Text>
                      </View>
                      <View
                        style={{
                          left: isMobile()
                            ? -windowWidth * 0.03
                            : -windowWidth * 0.01,
                          bottom: "1%",
                          width: 0,
                          height: 0,
                          backgroundColor: "transparent",
                          borderStyle: "solid",
                          borderRightWidth: isMobile()
                            ? windowWidth * 0.03
                            : windowWidth * 0.01,
                          borderTopWidth: isMobile()
                            ? windowWidth * 0.03
                            : windowWidth * 0.01,
                          borderRightColor: "transparent",
                          borderTopColor: isNotAvailable ? Colors.tabRed : Colors.primaryColor,
                          transform: [{ rotate: "90deg" }],

                          ...(!item.image && {
                            left: isMobile()
                              ? -windowWidth * 0.015
                              : -windowWidth * 0.005,
                            bottom: isMobile()
                              ? windowWidth * 0.074
                              : windowWidth * 0.014,
                          }),
                        }}
                      />
                    </View>
                  ) : (
                    <></>
                  )}
                </View>

                {pointsRedeemItemSkuDict[item.sku] !== undefined ||
                  pointsRedeemCategory !== undefined ? (
                  <View
                    style={{
                      marginTop: 5,
                    }}
                  >
                    <Text
                      style={{
                        color: Colors.primaryColor,
                        fontFamily: "NunitoSans-SemiBold",
                        fontSize: 12,
                        textAlign: "center",
                      }}
                    >
                      {pointsRedeemItemSkuDict[item.sku] !== undefined
                        ? `${pointsRedeemItemSkuDict[item.sku]
                          .conversionPointsFrom
                        } Points to RM${pointsRedeemItemSkuDict[item.sku]
                          .conversionCurrencyTo
                        }`
                        : `${pointsRedeemCategory.conversionPointsFrom} Points to RM${pointsRedeemCategory.conversionCurrencyTo}`}
                    </Text>
                  </View>
                ) : (
                  <></>
                )}

                {!excludePromoVoucher && (buy1Free1ItemSkuDict[item.sku] !== undefined ||
                  buy1Free1Category !== undefined) ? (
                  <View
                    style={{
                      marginTop: 5,
                    }}
                  >
                    <Text
                      style={{
                        color: Colors.descriptionColor,
                        fontFamily: "NunitoSans-SemiBold",
                        fontSize: 12,
                        textAlign: "center",
                      }}
                    >
                      {`Bundle Deal`}
                      {/* {buy1Free1ItemSkuDict[item.sku] !== undefined
                        ? `Buy ${
                            buy1Free1ItemSkuDict[item.sku].buyAmount
                          } Free ${buy1Free1ItemSkuDict[item.sku].getAmount}`
                        : `Buy ${buy1Free1Category.buyAmount} Free ${buy1Free1Category.getAmount}`} */}
                    </Text>
                  </View>
                ) : (
                  <></>
                )}
              </View>

              <View
                style={{
                  marginLeft: 15,
                  // flexDirection: 'row',
                  // flexShrink: 1,
                  width: isMobile() ? "55%" : windowWidth * 0.45,
                  // backgroundColor: 'red',
                }}
              >
                <Text
                  // numberOfLines={1}
                  style={[
                    itemNameTextScale,
                    {
                      //fontSize: 16,
                      textTransform: "uppercase",
                      fontFamily: "NunitoSans-Bold",
                      // flexWrap: 'wrap',
                      // flex: 1,
                      // flexShrink: 1,
                      // width: '100%',
                    },
                  ]}
                  numberOfLines={3}
                >
                  {item.name}
                </Text>

                <Text
                  // numberOfLines={1}
                  style={[
                    itemNameTextScale,
                    {
                      fontSize: 12,
                      textTransform: "uppercase",
                      fontFamily: "NunitoSans-Regular",
                      // flexWrap: 'wrap',
                      // flex: 1,
                      // flexShrink: 1,
                      // width: '100%',
                    },
                  ]}
                  numberOfLines={3}
                >
                  {item.dpName ? item.dpName : ''}
                </Text>

                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                  }}
                >
                  <Text
                    style={{
                      color: Colors.primaryColor,
                      fontFamily: "NunitoSans-Bold",
                      paddingTop: 5,
                      fontSize: 16,
                      textDecorationLine:
                        !excludePromoVoucher &&
                          (
                            overrideItemPriceSkuDict[item.sku] !== undefined ||
                            overrideCategoryPrice !== undefined
                          )
                          ? "line-through"
                          : "none",
                    }}
                  >
                    RM{parseFloat(extraPrice + item.price).toFixed(2)}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[item.unitType]}` : ''}
                  </Text>

                  {!excludePromoVoucher && (overrideItemPriceSkuDict[item.sku] !== undefined ||
                    overrideCategoryPrice !== undefined) ? (
                    <Text
                      style={{
                        color: Colors.secondaryColor,
                        fontFamily: "NunitoSans-Bold",
                        paddingTop: 5,
                        fontSize: 16,
                        marginLeft: 5,
                      }}
                    >
                      RM
                      {overrideItemPriceSkuDict[item.sku] &&
                        overrideItemPriceSkuDict[item.sku].overridePrice !==
                        undefined
                        ? parseFloat(
                          overrideItemPriceSkuDict[item.sku].overridePrice
                        ).toFixed(2)
                        : parseFloat(overrideCategoryPrice).toFixed(2)}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[item.unitType]}` : ''}
                    </Text>
                  ) : (
                    <></>
                  )}
                </View>

                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                  }}
                >
                  {!excludePromoVoucher && (amountOffItemSkuDict[item.sku] !== undefined ||
                    amountOffCategory !== undefined) ? (
                    <Text
                      style={{
                        color: Colors.descriptionColor,
                        fontFamily: "NunitoSans-SemiBold",
                        paddingTop: 5,
                        fontSize: 14,
                        // marginLeft: 5,
                      }}
                    >
                      {amountOffItemSkuDict[item.sku] !== undefined
                        ? `Buy ${amountOffItemSkuDict[item.sku].quantityMin
                        } pcs to enjoy RM${amountOffItemSkuDict[
                          item.sku
                        ].amountOff.toFixed(2)} off\n(Min purchases: RM${amountOffItemSkuDict[item.sku].priceMin
                        })`
                        : `Buy ${amountOffCategory.quantityMin} pcs to enjoy ${amountOffCategory.amountOff.toFixed(
                          0
                        )}% off\n(Min purchases: RM${amountOffCategory.priceMin
                        })`}
                    </Text>
                  ) : (
                    <></>
                  )}
                </View>

                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                  }}
                >
                  {!excludePromoVoucher && (percentageOffItemSkuDict[item.sku] !== undefined ||
                    percentageOffCategory !== undefined) ? (
                    <Text
                      style={{
                        color: Colors.descriptionColor,
                        fontFamily: "NunitoSans-SemiBold",
                        paddingTop: 5,
                        fontSize: 14,
                        // marginLeft: 5,
                      }}
                    >
                      {percentageOffItemSkuDict[item.sku] !== undefined
                        ? `Buy ${percentageOffItemSkuDict[item.sku].quantityMin
                        } pcs to enjoy ${percentageOffItemSkuDict[
                          item.sku
                        ].percentageOff.toFixed(0)}% off\n(Min purchases: RM${percentageOffItemSkuDict[item.sku].priceMin
                        })`
                        : `Buy ${percentageOffCategory.quantityMin} pcs to enjoy ${percentageOffCategory.percentageOff.toFixed(
                          0
                        )}% off\n(Min purchases: RM${percentageOffCategory.priceMin
                        })`}
                    </Text>
                  ) : (
                    <></>
                  )}
                </View>
              </View>
            </View>

            <View
              style={{
                flexDirection: "row",
                // width: "20%",
                // marginLeft: 60
                // backgroundColor: 'red',
                ...(!isMobile() && {
                  // width: windowWidth,
                  height: 26,
                  // right: 0,
                  alignSelf: "center",
                }),
              }}
            >
              <View
                style={{
                  // backgroundColor: "#e3e1e1",
                  backgroundColor: Colors.primaryColor,

                  // width: 67,
                  // height: 24,

                  width: 68,
                  height: 26,

                  // paddingVertical: 4,
                  // paddingHorizontal: 20,

                  borderRadius: 10,
                  justifyContent: "center",
                  alignSelf: "center",
                }}
              >
                <TouchableOpacity
                  onPress={async () => {
                    /* if (checkCartOutlet()) {
                      // setState({
                      //   cartWarning: true,
                      //   cartProceed: item
                      // })

                      setTempItem(item);

                      setCartWarning(true);
                    } else {
                      if (item.isActive) {
                        CommonStore.update((s) => {
                          s.selectedOutletItem = item;
                        });

                        // const subdomain = await AsyncStorage.getItem(
                        //   "latestSubdomain"
                        // );

                        // if (!subdomain) {
                        //   linkTo && linkTo(`${prefix}/outlet/menu/item`);
                        // } else {
                        //   linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu/item`);
                        // }

                        // linkTo && linkTo(`${prefix}/outlet/menu/item`);

                        props.navigation.navigate("MenuItemDetails", {
                          refresh: refresh.bind(this),
                          menuItem: item,
                          outletData: selectedOutlet,
                        });
                      } else {
                        console.log(orderType);
                        window.confirm(
                          "Sorry, this product is not available at the moment."
                        );
                      }
                    }
                  }} */
                    if (checkCartOutlet()) {
                      // setState({ cartWarning: true, })

                      setTempItem(item);

                      setCartWarning(true);
                    } else {
                      if (
                        item.isActive &&
                        (item.isAvailableDayActive
                          ? (item.effectiveTypeOptions.includes(effectiveDays) &&
                            item.effectiveStartTime && item.effectiveEndTime &&
                            moment().isSameOrAfter(
                              moment(item.effectiveStartTime)
                                .year(moment().year())
                                .month(moment().month())
                                .date(moment().date())
                            )
                            &&
                            moment().isBefore
                              (moment(item.effectiveEndTime)
                                .year(moment().year())
                                .month(moment().month())
                                .date(moment().date())
                              ))
                          : true) &&
                        (item.isOnlineMenu !== undefined
                          ? item.isOnlineMenu
                          : true) &&
                        (item.isStockCountActive !== undefined &&
                          item.isStockCountActive !== false &&
                          item.stockCount !== undefined &&
                          item.toSellIgnoreStock !== undefined
                          ? item.isStockCountActive &&
                          item.stockCount > 0 &&
                          (item.toSellIgnoreStock !== undefined
                            ? item.toSellIgnoreStock
                            : true)
                          : true)
                      ) {
                        CommonStore.update((s) => {
                          s.selectedOutletItem = item;

                          s.selectedOutletItemAddOn = {};
                          s.selectedOutletItemAddOnChoice = {};
                          s.selectedOutletItemAddOnOi = {};

                          // s.selectedAddOnIdForChoiceQtyDict = {};
                        });

                        // const subdomain = await AsyncStorage.getItem("latestSubdomain");

                        // if (!subdomain) {
                        //   linkTo && linkTo(`${prefix}/outlet/menu/item`);
                        // } else {
                        //   linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu/item`);
                        // }

                        // linkTo && linkTo(`${prefix}/outlet/menu/item`);
                        if (isMobile() && selectedOutlet && true) {
                          CommonStore.update((s) => {
                            s.menuItemDetailModal = true;
                          });

                          global.menuItemDetailModal = true;

                          window.history.pushState({
                            page: 'menuItemDetailModal',
                          }, '');

                          CommonStore.update(s => {
                            s.currPageIframe = 'MenuItemDetails';
                          });

                          // window.history.pushState(null, '', window.location.href);
                          // window.history.forward();

                          // const subdomain = await AsyncStorage.getItem("latestSubdomain");

                          // if (!subdomain) {
                          //   linkTo && linkTo(`${prefix}/outlet/menu/item`);
                          // } else {
                          //   linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu/item`);
                          // }

                          // props.navigation.navigate(
                          //   "Product Details - KooDoo Web Order",
                          //   {
                          //     refresh: refresh.bind(this),
                          //     menuItem: item,
                          //     outletData: selectedOutlet,
                          //   }
                          // );
                        }
                        else {
                          props.navigation.navigate(
                            "Product Details - KooDoo Web Order",
                            {
                              refresh: refresh.bind(this),
                              menuItem: item,
                              outletData: selectedOutlet,
                            }
                          );
                        }
                      } else {
                        // window.confirm(
                        //   'Info',
                        //   'Sorry, this product is not available for now.',
                        // );

                        CommonStore.update((s) => {
                          s.alertObj = {
                            title: "Info",
                            message:
                              "Sorry, this product is not available for now.",
                          };
                        });
                      }
                    }
                  }}
                >
                  <Text
                    style={{
                      alignSelf: "center",
                      // color: "#8f8f8f",
                      color: Colors.whiteColor,
                      fontSize: 13,
                      fontFamily: "NunitoSans-Bold",
                    }}
                  >
                    {quantity > 0 ? quantity : "Add"}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </TouchableOpacity>
      );
    }
  };

  const renderAllMenuV2 = ({ item, index }) => {
    var quantity = 0;
    //const cartItem = cartItem.find(obj => obj.itemId === item.id);

    const itemsInCart = cartItems.filter((obj) => obj.itemId === item.uniqueId);
    if (itemsInCart) {
      for (const obj of itemsInCart) {
        quantity += parseInt(obj.quantity);
      }
    }

    var itemNameFontSize = 15;

    if (windowWidth <= 360) {
      itemNameFontSize = 13;
      //console.log(windowWidth)
    }

    const itemNameTextScale = {
      fontSize: itemNameFontSize,
    };

    let excludePromoVoucher = false;
    let outletCategory = selectedOutletItemCategoriesDict[item.categoryId];
    if (
      (item && item.excludePromoVoucher)
      ||
      (outletCategory && outletCategory.excludePromoVoucher)
    ) {
      excludePromoVoucher = true;
    }

    var overrideCategoryPrice = undefined;

    if (
      !excludePromoVoucher &&
      selectedOutletItemCategoriesDict[item.categoryId] &&
      overrideCategoryPriceNameDict[
      selectedOutletItemCategoriesDict[item.categoryId].name
      ] !== undefined
    ) {
      overrideCategoryPrice =
        overrideCategoryPriceNameDict[
          selectedOutletItemCategoriesDict[item.categoryId].name
        ].overridePrice;
    }

    if (
      //item.categoryId === selectedOutletItemCategory.uniqueId &&
      (item.hideInOrderTypes && item.hideInOrderTypes.length > 0
        ? item.hideInOrderTypes.includes(orderType)
          ? false
          : true
        : true)
    ) {
      var amountOffCategory = undefined;
      if (
        !excludePromoVoucher &&
        selectedOutletItemCategoriesDict[item.categoryId] &&
        amountOffCategoryNameDict[
        selectedOutletItemCategoriesDict[item.categoryId].name
        ] !== undefined
      ) {
        amountOffCategory =
          amountOffCategoryNameDict[
          selectedOutletItemCategoriesDict[item.categoryId].name
          ];
      }

      var percentageOffCategory = undefined;
      if (
        !excludePromoVoucher &&
        selectedOutletItemCategoriesDict[item.categoryId] &&
        percentageOffCategoryNameDict[
        selectedOutletItemCategoriesDict[item.categoryId].name
        ] !== undefined
      ) {
        percentageOffCategory =
          percentageOffCategoryNameDict[
          selectedOutletItemCategoriesDict[item.categoryId].name
          ];
      }

      var pointsRedeemCategory = undefined;
      if (
        !excludePromoVoucher &&
        selectedOutletItemCategoriesDict[item.categoryId] &&
        pointsRedeemCategoryNameDict[
        selectedOutletItemCategoriesDict[item.categoryId].name
        ] !== undefined
      ) {
        pointsRedeemCategory =
          pointsRedeemCategoryNameDict[
          selectedOutletItemCategoriesDict[item.categoryId].name
          ];
      }

      var buy1Free1Category = undefined;
      if (
        !excludePromoVoucher &&
        selectedOutletItemCategoriesDict[item.categoryId] &&
        buy1Free1CategoryNameDict[
        selectedOutletItemCategoriesDict[item.categoryId].name
        ] !== undefined
      ) {
        buy1Free1Category =
          buy1Free1CategoryNameDict[
          selectedOutletItemCategoriesDict[item.categoryId].name
          ];
      }

      var extraPrice = 0;
      if (
        orderType === ORDER_TYPE.DELIVERY &&
        selectedOutlet &&
        selectedOutlet.deliveryPrice
      ) {
        extraPrice = selectedOutlet.deliveryPrice;
      } else if (
        orderType === ORDER_TYPE.PICKUP &&
        selectedOutlet &&
        selectedOutlet.pickUpPrice
      ) {
        extraPrice = selectedOutlet.pickUpPrice;
      }

      if (orderType === ORDER_TYPE.DELIVERY) {
        extraPrice = item.deliveryCharges || 0;

        if (
          extraPrice &&
          item.deliveryChargesType === CHARGES_TYPE.PERCENTAGE_BASED
        ) {
          extraPrice = (item.price * extraPrice) / 100;
        }

        if (!item.deliveryChargesActive) {
          extraPrice = 0;
        }
      }

      if (orderType === ORDER_TYPE.PICKUP) {
        extraPrice = item.pickUpCharges || 0;

        if (
          extraPrice &&
          item.pickUpChargesType === CHARGES_TYPE.PERCENTAGE_BASED
        ) {
          extraPrice = (item.price * extraPrice) / 100;
        }

        if (!item.pickUpChargesActive) {
          extraPrice = 0;
        }
      }

      const isNotAvailable = !item.isActive ||
        !(item.isAvailableDayActive
          ? (item.effectiveTypeOptions.includes(effectiveDays) &&
            item.effectiveStartTime && item.effectiveEndTime &&
            moment().isSameOrAfter(
              moment(item.effectiveStartTime)
                .year(moment().year())
                .month(moment().month())
                .date(moment().date())
            )
            &&
            moment().isBefore
              (moment(item.effectiveEndTime)
                .year(moment().year())
                .month(moment().month())
                .date(moment().date())
              ))
          : true) ||
        !(item.isOnlineMenu !== undefined
          ? item.isOnlineMenu
          : true) ||
        !(item.isStockCountActive !== undefined &&
          item.isStockCountActive !== false &&
          item.stockCount !== undefined &&
          item.toSellIgnoreStock !== undefined
          ? item.isStockCountActive &&
          item.stockCount > 0 &&
          (item.toSellIgnoreStock !== undefined
            ? item.toSellIgnoreStock
            : true)
          : true);

      let specialTag = '';
      if (item.specialTags && item.specialTags.length > 0 &&
        typeof item.specialTags[0] === 'string') {
        specialTag = item.specialTags[0];
      }

      return (
        <>
          {
            item.firstItemInCategory
              ?
              <View
                // onLayout={(event) => onItemLayout(index, event)}
                onLayout={(event) => {
                  const { width, height, x, y, } = event.nativeEvent.layout;

                  // console.log(item.categoryId);
                  // console.log(item.name);
                  // console.log(`y pos: ${y}`);
                  // console.log('=======================');

                  // global.categoryWidthByNameDict[item.name] = width;

                  if (item && item.categoryId) {
                    global.categoryPosYByIdDict[item.categoryId] = y;
                  }

                  // console.log("test: ", global.categoryPosXByIdDict);
                }}
                style={{ alignItems: 'center', width: '100%' }}
              >
                <View
                  style={{
                    backgroundColor: Colors.primaryColor,
                    width: '95%',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginTop: 10,
                    marginBottoM: 5,
                  }}>
                  <Text
                    style={{
                      textTransform: "capitalize",
                      paddingVertical: 12,
                      fontFamily: "NunitoSans-Bold",
                      color: Colors.whiteColor,
                      fontSize: 16,
                      textAlign: 'center',

                    }}
                  >
                    {item.categoryName ? item.categoryName.toUpperCase() : 'N/A'}
                  </Text>
                </View>

              </View>
              :
              <></>
          }

          <TouchableOpacity
            onPress={async () => {
              if (checkCartOutlet()) {
                // setState({ cartWarning: true, })

                setTempItem(item);

                setCartWarning(true);
              } else {
                if (
                  item.isActive &&
                  (item.isAvailableDayActive
                    ? (item.effectiveTypeOptions.includes(effectiveDays) &&
                      item.effectiveStartTime && item.effectiveEndTime &&
                      moment().isSameOrAfter(
                        moment(item.effectiveStartTime)
                          .year(moment().year())
                          .month(moment().month())
                          .date(moment().date())
                      )
                      &&
                      moment().isBefore
                        (moment(item.effectiveEndTime)
                          .year(moment().year())
                          .month(moment().month())
                          .date(moment().date())
                        ))
                    : true) &&
                  (item.isOnlineMenu !== undefined ? item.isOnlineMenu : true) &&
                  (item.isStockCountActive !== undefined &&
                    item.isStockCountActive !== false &&
                    item.stockCount !== undefined &&
                    item.toSellIgnoreStock !== undefined
                    ? item.isStockCountActive &&
                    item.stockCount > 0 &&
                    (item.toSellIgnoreStock !== undefined
                      ? item.toSellIgnoreStock
                      : true)
                    : true)
                ) {
                  CommonStore.update((s) => {
                    s.selectedOutletItem = item;

                    s.selectedOutletItemAddOn = {};
                    s.selectedOutletItemAddOnChoice = {};
                    s.selectedOutletItemAddOnOi = {};
                    // s.selectedAddOnIdForChoiceQtyDict = {};
                  });

                  // const subdomain = await AsyncStorage.getItem("latestSubdomain");

                  // if (!subdomain) {
                  //   linkTo && linkTo(`${prefix}/outlet/menu/item`);
                  // } else {
                  //   linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu/item`);
                  // }

                  // linkTo && linkTo(`${prefix}/outlet/menu/item`);                

                  if (isMobile() && selectedOutlet && true) {
                    CommonStore.update((s) => {
                      s.menuItemDetailModal = true;
                    });

                    global.menuItemDetailModal = true;

                    window.history.pushState({
                      page: 'menuItemDetailModal',
                    }, '');

                    CommonStore.update(s => {
                      s.currPageIframe = 'MenuItemDetails';
                    });


                    // window.history.pushState(null, '', window.location.href);
                    // window.history.forward();

                    // const subdomain = await AsyncStorage.getItem("latestSubdomain");

                    // if (!subdomain) {
                    //   linkTo && linkTo(`${prefix}/outlet/menu/item`);
                    // } else {
                    //   linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu/item`);
                    // }

                    // props.navigation.navigate(
                    //   "Product Details - KooDoo Web Order",
                    //   {
                    //     refresh: refresh.bind(this),
                    //     menuItem: item,
                    //     outletData: selectedOutlet,
                    //   }
                    // );
                  }
                  else {
                    props.navigation.navigate(
                      "Product Details - KooDoo Web Order",
                      {
                        refresh: refresh.bind(this),
                        menuItem: item,
                        outletData: selectedOutlet,
                      }
                    );
                  }
                } else {
                  // window.confirm(
                  //   'Info',
                  //   'Sorry, this product is not available for now.',
                  // );

                  CommonStore.update((s) => {
                    s.alertObj = {
                      title: "Info",
                      message: "Sorry, this product is not available for now.",
                    };
                  });
                }
              }
            }}
          >
            <View
              style={{
                flexDirection: "row",
                paddingHorizontal: 20,
                paddingBottom: 15,
                paddingTop: 10,
                display: "flex",
                justifyContent: "space-between",
                flexDirection: "row",
              }}
            >
              <View
                style={{
                  flexDirection: "row",
                  alignContent: "center",
                  alignItems: "center",
                  width: isMobile() ? "75%" : windowWidth * 0.55,
                  display: "flex",
                  justifyContent: "flex-start",
                  // backgroundColor: 'blue',
                }}
              >
                <View>
                  <View
                    style={[
                      {
                        backgroundColor: Colors.secondaryColor,
                        // width: 60,
                        // height: 60,
                        width: isMobile()
                          ? windowWidth * 0.22
                          : windowWidth * 0.05,
                        height: isMobile()
                          ? windowWidth * 0.22
                          : windowWidth * 0.05,
                        borderRadius: 10,
                      },
                      item.image
                        ? {}
                        : {
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                        },
                    ]}
                  >
                    {item.image ? (
                      // <Image source={{ uri: item.image }} style={{
                      //   width: windowWidth * 0.22,
                      //   height: windowWidth * 0.22,
                      //   borderRadius: 10
                      // }} />
                      <AsyncImage
                        source={{ uri: item.image }}
                        item={item}
                        style={{
                          width: isMobile()
                            ? windowWidth * 0.22
                            : windowWidth * 0.05,
                          height: isMobile()
                            ? windowWidth * 0.22
                            : windowWidth * 0.05,
                          borderRadius: 10,
                        }}
                      />
                    ) : (
                      // <Ionicons name="fast-food-outline" size={50} />
                      <View
                        style={{
                          width: isMobile()
                            ? windowWidth * 0.22
                            : windowWidth * 0.05,
                          height: isMobile()
                            ? windowWidth * 0.22
                            : windowWidth * 0.05,
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        <Ionicons
                          name="fast-food-outline"
                          // size={45}
                          size={
                            isMobile() ? windowWidth * 0.1 : windowWidth * 0.02
                          }
                        />
                      </View>
                    )}

                    {isNotAvailable || specialTag ? (
                      <View
                        style={{
                          position: "absolute",
                          zIndex: 3,
                        }}
                      >
                        <View
                          style={{
                            // width: 120,
                            width: isMobile()
                              ? windowWidth * 0.25
                              : windowWidth * 0.06,
                            left: isMobile()
                              ? -windowWidth * 0.03
                              : -windowWidth * 0.01,
                            padding: 0,
                            paddingLeft: isMobile()
                              ? windowWidth * 0.02
                              : windowWidth * 0.005,
                            justifyContent: "center",
                            alignItems: "center",
                            backgroundColor: isNotAvailable ? Colors.tabRed : Colors.primaryColor,
                            height: 20,
                            borderTopRightRadius: 10,
                            borderBottomRightRadius: 3,

                            ...(!item.image && {
                              left: isMobile()
                                ? -windowWidth * 0.015
                                : -windowWidth * 0.005,
                              bottom: isMobile()
                                ? windowWidth * 0.074
                                : windowWidth * 0.014,
                            }),
                          }}
                        >
                          <Text
                            style={{
                              color: "#FFF",
                              fontFamily: "NunitoSans-Bold",
                              fontSize: 10,
                              bottom: 1,
                            }}
                          >
                            {isNotAvailable ? 'Not available' : specialTag}
                          </Text>
                        </View>
                        <View
                          style={{
                            left: isMobile()
                              ? -windowWidth * 0.03
                              : -windowWidth * 0.01,
                            bottom: "1%",
                            width: 0,
                            height: 0,
                            backgroundColor: "transparent",
                            borderStyle: "solid",
                            borderRightWidth: isMobile()
                              ? windowWidth * 0.03
                              : windowWidth * 0.01,
                            borderTopWidth: isMobile()
                              ? windowWidth * 0.03
                              : windowWidth * 0.01,
                            borderRightColor: "transparent",
                            borderTopColor: isNotAvailable ? Colors.tabRed : Colors.primaryColor,
                            transform: [{ rotate: "90deg" }],

                            ...(!item.image && {
                              left: isMobile()
                                ? -windowWidth * 0.015
                                : -windowWidth * 0.005,
                              bottom: isMobile()
                                ? windowWidth * 0.074
                                : windowWidth * 0.014,
                            }),
                          }}
                        />
                      </View>
                    ) : (
                      <></>
                    )}
                  </View>

                  {pointsRedeemItemSkuDict[item.sku] !== undefined ||
                    pointsRedeemCategory !== undefined ? (
                    <View
                      style={{
                        marginTop: 5,
                      }}
                    >
                      <Text
                        style={{
                          color: Colors.primaryColor,
                          fontFamily: "NunitoSans-SemiBold",
                          fontSize: 12,
                          textAlign: "center",
                        }}
                      >
                        {pointsRedeemItemSkuDict[item.sku] !== undefined
                          ? `${pointsRedeemItemSkuDict[item.sku]
                            .conversionPointsFrom
                          } Points to RM${pointsRedeemItemSkuDict[item.sku]
                            .conversionCurrencyTo
                          }`
                          : `${pointsRedeemCategory.conversionPointsFrom} Points to RM${pointsRedeemCategory.conversionCurrencyTo}`}
                      </Text>
                    </View>
                  ) : (
                    <></>
                  )}

                  {!excludePromoVoucher && (buy1Free1ItemSkuDict[item.sku] !== undefined ||
                    buy1Free1Category !== undefined) ? (
                    <View
                      style={{
                        marginTop: 5,
                      }}
                    >
                      <Text
                        style={{
                          color: Colors.descriptionColor,
                          fontFamily: "NunitoSans-SemiBold",
                          fontSize: 12,
                          textAlign: "center",
                        }}
                      >
                        {`Bundle Deal`}
                        {/* {buy1Free1ItemSkuDict[item.sku] !== undefined
                        ? `Buy ${
                            buy1Free1ItemSkuDict[item.sku].buyAmount
                          } Free ${buy1Free1ItemSkuDict[item.sku].getAmount}`
                        : `Buy ${buy1Free1Category.buyAmount} Free ${buy1Free1Category.getAmount}`} */}
                      </Text>
                    </View>
                  ) : (
                    <></>
                  )}
                </View>

                <View
                  style={{
                    marginLeft: 15,
                    // flexDirection: 'row',
                    // flexShrink: 1,
                    width: isMobile() ? "55%" : windowWidth * 0.45,
                    // backgroundColor: 'red',
                  }}
                >
                  <Text
                    // numberOfLines={1}
                    style={[
                      itemNameTextScale,
                      {
                        //fontSize: 16,
                        textTransform: "uppercase",
                        fontFamily: "NunitoSans-Bold",
                        // flexWrap: 'wrap',
                        // flex: 1,
                        // flexShrink: 1,
                        // width: '100%',
                      },
                    ]}
                    numberOfLines={3}
                  >
                    {item.name}
                  </Text>

                  <Text
                    // numberOfLines={1}
                    style={[
                      itemNameTextScale,
                      {
                        fontSize: 12,
                        textTransform: "uppercase",
                        fontFamily: "NunitoSans-Regular",
                        // flexWrap: 'wrap',
                        // flex: 1,
                        // flexShrink: 1,
                        // width: '100%',
                      },
                    ]}
                    numberOfLines={3}
                  >
                    {item.dpName ? item.dpName : ''}
                  </Text>

                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                    }}
                  >
                    <Text
                      style={{
                        color: Colors.primaryColor,
                        fontFamily: "NunitoSans-Bold",
                        paddingTop: 5,
                        fontSize: 16,
                        textDecorationLine:
                        !excludePromoVoucher &&
                        (
                          overrideItemPriceSkuDict[item.sku] !== undefined ||
                            overrideCategoryPrice !== undefined)
                            ? "line-through"
                            : "none",
                      }}
                    >
                      RM{parseFloat(extraPrice + item.price).toFixed(2)}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[item.unitType]}` : ''}
                    </Text>

                    {!excludePromoVoucher && (overrideItemPriceSkuDict[item.sku] !== undefined ||
                      overrideCategoryPrice !== undefined) ? (
                      <Text
                        style={{
                          color: Colors.secondaryColor,
                          fontFamily: "NunitoSans-Bold",
                          paddingTop: 5,
                          fontSize: 16,
                          marginLeft: 5,
                        }}
                      >
                        RM
                        {overrideItemPriceSkuDict[item.sku] &&
                          overrideItemPriceSkuDict[item.sku].overridePrice !==
                          undefined
                          ? parseFloat(
                            overrideItemPriceSkuDict[item.sku].overridePrice
                          ).toFixed(2)
                          : parseFloat(overrideCategoryPrice).toFixed(2)}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[item.unitType]}` : ''}
                      </Text>
                    ) : (
                      <></>
                    )}
                  </View>

                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                    }}
                  >
                    {!excludePromoVoucher && (amountOffItemSkuDict[item.sku] !== undefined ||
                      amountOffCategory !== undefined) ? (
                      <Text
                        style={{
                          color: Colors.descriptionColor,
                          fontFamily: "NunitoSans-SemiBold",
                          paddingTop: 5,
                          fontSize: 14,
                          // marginLeft: 5,
                        }}
                      >
                        {amountOffItemSkuDict[item.sku] !== undefined
                          ? `Buy ${amountOffItemSkuDict[item.sku].quantityMin
                          } pcs to enjoy RM${amountOffItemSkuDict[
                            item.sku
                          ].amountOff.toFixed(2)} off\n(Min purchases: RM${amountOffItemSkuDict[item.sku].priceMin
                          })`
                          : `Buy ${amountOffCategory.quantityMin} pcs to enjoy ${amountOffCategory.amountOff.toFixed(
                            0
                          )}% off\n(Min purchases: RM${amountOffCategory.priceMin
                          })`}
                      </Text>
                    ) : (
                      <></>
                    )}
                  </View>

                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                    }}
                  >
                    {!excludePromoVoucher && (percentageOffItemSkuDict[item.sku] !== undefined ||
                      percentageOffCategory !== undefined) ? (
                      <Text
                        style={{
                          color: Colors.descriptionColor,
                          fontFamily: "NunitoSans-SemiBold",
                          paddingTop: 5,
                          fontSize: 14,
                          // marginLeft: 5,
                        }}
                      >
                        {percentageOffItemSkuDict[item.sku] !== undefined
                          ? `Buy ${percentageOffItemSkuDict[item.sku].quantityMin
                          } pcs to enjoy ${percentageOffItemSkuDict[
                            item.sku
                          ].percentageOff.toFixed(0)}% off\n(Min purchases: RM${percentageOffItemSkuDict[item.sku].priceMin
                          })`
                          : `Buy ${percentageOffCategory.quantityMin} pcs to enjoy ${percentageOffCategory.percentageOff.toFixed(
                            0
                          )}% off\n(Min purchases: RM${percentageOffCategory.priceMin
                          })`}
                      </Text>
                    ) : (
                      <></>
                    )}
                  </View>
                </View>
              </View>

              <View
                style={{
                  flexDirection: "row",
                  // width: "20%",
                  // marginLeft: 60
                  // backgroundColor: 'red',
                  ...(!isMobile() && {
                    // width: windowWidth,
                    height: 26,
                    // right: 0,
                    alignSelf: "center",
                  }),
                }}
              >
                <View
                  style={{
                    // backgroundColor: "#e3e1e1",
                    backgroundColor: Colors.primaryColor,

                    // width: 67,
                    // height: 24,

                    width: 68,
                    height: 26,

                    // paddingVertical: 4,
                    // paddingHorizontal: 20,

                    borderRadius: 10,
                    justifyContent: "center",
                    alignSelf: "center",
                  }}
                >
                  <TouchableOpacity
                    onPress={async () => {
                      /* if (checkCartOutlet()) {
                        // setState({
                        //   cartWarning: true,
                        //   cartProceed: item
                        // })
  
                        setTempItem(item);
  
                        setCartWarning(true);
                      } else {
                        if (item.isActive) {
                          CommonStore.update((s) => {
                            s.selectedOutletItem = item;
                          });
  
                          // const subdomain = await AsyncStorage.getItem(
                          //   "latestSubdomain"
                          // );
  
                          // if (!subdomain) {
                          //   linkTo && linkTo(`${prefix}/outlet/menu/item`);
                          // } else {
                          //   linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu/item`);
                          // }
  
                          // linkTo && linkTo(`${prefix}/outlet/menu/item`);
  
                          props.navigation.navigate("MenuItemDetails", {
                            refresh: refresh.bind(this),
                            menuItem: item,
                            outletData: selectedOutlet,
                          });
                        } else {
                          console.log(orderType);
                          window.confirm(
                            "Sorry, this product is not available at the moment."
                          );
                        }
                      }
                    }} */
                      if (checkCartOutlet()) {
                        // setState({ cartWarning: true, })

                        setTempItem(item);

                        setCartWarning(true);
                      } else {
                        if (
                          item.isActive &&
                          (item.isAvailableDayActive
                            ? (item.effectiveTypeOptions.includes(effectiveDays) &&
                              item.effectiveStartTime && item.effectiveEndTime &&
                              moment().isSameOrAfter(
                                moment(item.effectiveStartTime)
                                  .year(moment().year())
                                  .month(moment().month())
                                  .date(moment().date())
                              )
                              &&
                              moment().isBefore
                                (moment(item.effectiveEndTime)
                                  .year(moment().year())
                                  .month(moment().month())
                                  .date(moment().date())
                                ))
                            : true) &&
                          (item.isOnlineMenu !== undefined
                            ? item.isOnlineMenu
                            : true) &&
                          (item.isStockCountActive !== undefined &&
                            item.isStockCountActive !== false &&
                            item.stockCount !== undefined &&
                            item.toSellIgnoreStock !== undefined
                            ? item.isStockCountActive &&
                            item.stockCount > 0 &&
                            (item.toSellIgnoreStock !== undefined
                              ? item.toSellIgnoreStock
                              : true)
                            : true)
                        ) {
                          CommonStore.update((s) => {
                            s.selectedOutletItem = item;

                            s.selectedOutletItemAddOn = {};
                            s.selectedOutletItemAddOnChoice = {};
                            s.selectedOutletItemAddOnOi = {};

                            // s.selectedAddOnIdForChoiceQtyDict = {};
                          });

                          // const subdomain = await AsyncStorage.getItem("latestSubdomain");

                          // if (!subdomain) {
                          //   linkTo && linkTo(`${prefix}/outlet/menu/item`);
                          // } else {
                          //   linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu/item`);
                          // }

                          // linkTo && linkTo(`${prefix}/outlet/menu/item`);
                          if (isMobile() && selectedOutlet && true) {
                            CommonStore.update((s) => {
                              s.menuItemDetailModal = true;
                            });

                            global.menuItemDetailModal = true;

                            window.history.pushState({
                              page: 'menuItemDetailModal',
                            }, '');

                            // window.history.pushState(null, '', window.location.href);
                            // window.history.forward();

                            // const subdomain = await AsyncStorage.getItem("latestSubdomain");

                            // if (!subdomain) {
                            //   linkTo && linkTo(`${prefix}/outlet/menu/item`);
                            // } else {
                            //   linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu/item`);
                            // }

                            // props.navigation.navigate(
                            //   "Product Details - KooDoo Web Order",
                            //   {
                            //     refresh: refresh.bind(this),
                            //     menuItem: item,
                            //     outletData: selectedOutlet,
                            //   }
                            // );
                          }
                          else {
                            props.navigation.navigate(
                              "Product Details - KooDoo Web Order",
                              {
                                refresh: refresh.bind(this),
                                menuItem: item,
                                outletData: selectedOutlet,
                              }
                            );
                          }
                        } else {
                          // window.confirm(
                          //   'Info',
                          //   'Sorry, this product is not available for now.',
                          // );

                          CommonStore.update((s) => {
                            s.alertObj = {
                              title: "Info",
                              message:
                                "Sorry, this product is not available for now.",
                            };
                          });
                        }
                      }
                    }}
                  >
                    <Text
                      style={{
                        alignSelf: "center",
                        // color: "#8f8f8f",
                        color: Colors.whiteColor,
                        fontSize: 13,
                        fontFamily: "NunitoSans-Bold",
                      }}
                    >
                      {quantity > 0 ? quantity : "Add"}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </TouchableOpacity>
        </>
      );
    }
    else {
      return null;
    }
  };

  // onContainerScroll = e => {
  //   console.log(windowWidth);
  //   console.log(e.nativeEvent.contentOffset.y);
  //   console.log('---------------------------')

  //   if (e.nativeEvent.contentOffset.y * 2 >= windowWidth) {
  //     console.log('hit top');

  //     // setState({
  //     //   isInfoTabHitTop: true,
  //     // });
  //   }
  //   else {
  //     console.log('not hit top');

  //     // setState({
  //     //   isInfoTabHitTop: false,
  //     // });
  //   }
  // }

  const nextCategory = () => {
    const catLength = categoryOutlet.length;
    if (categoryIndex == catLength - 1) {
      setState({
        category: categoryOutlet[0],
        categoryIndex: 0,
        // menu: choice[index].category,
      });
    } else {
      setState({
        category: categoryOutlet[categoryIndex + 1],
        categoryIndex: categoryIndex + 1,
        // menu: choice[index].category,
      });
    }
    refreshMenu();
  };

  const checkCartOutlet = () => {
    // const outletId = outletData.id
    // console.log(Cart.getOutletId() != null)
    // if (selectedOutlet && selectedOutlet.uniqueId != Cart.getOutletId() && Cart.getOutletId() != null) {
    if (
      selectedOutlet &&
      cartOutletId &&
      selectedOutlet.uniqueId !== cartOutletId
    ) {
      return true;
    }
    return false;
  };

  const clearCartItems = async () => {
    safelyExecuteIdb(() => {
      // AsyncStorage.removeItem(`${firebaseUid}.cartItems`);
      idbDel(`cartItems`);
      // AsyncStorage.removeItem(`${firebaseUid}.cartOutletId`);
      idbDel(`cartOutletId`);
    });

    CommonStore.update((s) => {
      s.cartItems = [];
      s.cartItemsProcessed = [];

      s.cartOutletId = null;
    });
  };

  const loginLogoutUserAccount = async () => {
    if (!email) {
      // login

      if (!inputEmail || !inputPassword) {
        alert("Please fill in the email and password field.");

        return;
      }

      CommonStore.update((s) => {
        s.isLoading = true;
      });

      try {
        // let res = await firebase
        //   .auth()
        //   .signInWithEmailAndPassword(inputEmail, inputPassword);
        let res = await signInWithEmailAndPassword(global.auth, inputEmail, inputPassword);
        let user = res.user;
        let firebaseToken = await user.getIdToken();

        // Token.setFirebaseToken(firebaseToken);

        // await AsyncStorage.setItem(
        //   'firebaseToken',
        //   firebaseToken
        // );

        ApiClient.GET(
          API.getToken + firebaseToken + "&app=" + APP_TYPE.WEB_ORDER
        ).then(async (result) => {
          console.log("token", result)

          if (result.token != undefined && result.token) {

            // console.log('RESU', result)
            // Token.setToken(result.token);
            // await AsyncStorage.setItem(
            //   'token',
            //   result.token
            // );

            const accessToken = result.token;

            await AsyncStorage.setItem("accessToken", result.token);

            global.accessToken = result.token;

            ApiClient.GET(API.user).then(async (result) => {
              // this.setState({ loading: false });
              // User.setUserData(result);
              // User.setName(result.name);
              // Token.setRefreshToken(result.refreshToken);
              // User.setUserId(result.firebaseUid);
              // User.setlogin(true);
              // User.getRefreshMainScreen();

              // await AsyncStorage.setItem('email', this.state.email);
              // await AsyncStorage.setItem('password', this.state.password);

              const refreshToken = result.refreshToken;

              await AsyncStorage.setItem("refreshToken", result.refreshToken);

              // await AsyncStorage.setItem(
              //   'role',
              //   result.role
              // );

              // await AsyncStorage.setItem(
              //   'firebaseUid',
              //   result.firebaseUid
              // );

              // User.getRefreshMainScreen();

              UserStore.update((s) => {
                s.firebaseUid = result.firebaseUid;
                s.userId = result.firebaseUid;
                s.role = result.role;
                s.refreshToken = refreshToken;
                s.token = accessToken;
                s.name = result.name || "N/A";
                s.email = result.email || "";
                s.number = result.number || "";
              });

              await AsyncStorage.removeItem('storedUserName');
              await AsyncStorage.removeItem('storedUserPhone');

              setShowLoginModal(false);

              CommonStore.update((s) => {
                s.isLoading = false;
              });

              TempStore.update((s) => {
                s.showClaimVoucher = false;
              });

              global.userPhoneLoaded = true;
            });
          } else {
            alert("Invalid request, please try again.");

            CommonStore.update((s) => {
              s.isLoading = false;
            });
          }
        });
      } catch (error) {
        alert("Invalid email and/or password.");

        CommonStore.update((s) => {
          s.isLoading = false;
        });
      }
    } else {
      // logout

      CommonStore.update((s) => {
        s.isLoading = true;
      });

      try {
        // firebase
        //   .auth()
        //   .signInAnonymously()
        signInAnonymously(global.auth)
          .then((result) => {
            // TempStore.update(s => {
            //   s.firebaseAuth = true;
            // });

            const firebaseUid = result.user.uid;

            ApiClient.GET(API.getTokenKWeb + firebaseUid).then(async (result) => {
              console.log("getTokenKWeb");
              console.log(result);

              if (result && result.token) {
                await AsyncStorage.setItem("accessToken", result.token);
                await AsyncStorage.setItem("refreshToken", result.refreshToken);

                global.accessToken = result.token;

                UserStore.update((s) => {
                  s.firebaseUid = result.userId;
                  s.userId = result.userId;
                  s.role = result.role;
                  s.refreshToken = result.refreshToken;
                  s.token = result.token;
                  s.name = "";
                  s.email = "";
                  s.number = "";
                });

                setInputEmail("");
                setInputPassword("");

                setShowLoginModal(false);

                CommonStore.update((s) => {
                  s.isLoading = false;
                });
              } else {
                alert("Unauthorized access.");

                CommonStore.update((s) => {
                  s.isLoading = false;
                });
              }
            });
          });
      }
      catch (ex) {
        console.error(ex);

        CommonStore.update((s) => {
          s.isLoading = false;
        });
      }
    }
  };

  const loginUserAccountByPhone = async () => {
    if (!email) {
      // login

      if (
        // !inputName
        // ||
        !inputPhone) {
        window.confirm("Info\n\nPlease fill in the phone field.");

        return;
      }

      var parsedPhone = inputPhone;;
      if (parsedPhone.length > 0 && !parsedPhone.startsWith('6')) {
        parsedPhone = `6${parsedPhone}`;
      }

      var isValidLongNumber = false;
      if (
        (parsedPhone.startsWith('011') && parsedPhone.length === 11)
        ||
        (parsedPhone.startsWith('6011') && parsedPhone.length === 12)
      ) {
        isValidLongNumber = true;
      }

      if ((parsedPhone.length === 10 || parsedPhone.length === 11 || isValidLongNumber)) {
      }
      else {
        window.confirm("Info\n\nInvalid phone number.");

        return;
      }

      CommonStore.update((s) => {
        s.isLoading = true;
      });

      try {
        const result = await signInWithPhoneForCRMUser(selectedOutlet, {
          nameVerification: false,
          name: inputName,
          phone: parsedPhone,
        });

        if (result && result.name && result.phone) {
          // means verified successfully

          await AsyncStorage.setItem('storedUserName', result.name);
          await AsyncStorage.setItem('storedUserPhone', parsedPhone);

          global.storedUserName = result.name;
          global.storedUserPhone = parsedPhone;

          window.confirm('Info\n\nSigned in successfully.');
        }
        else {
          // means failed to verify

          // window.confirm('Info\n\nInvalid name and/or phone number.');
          window.confirm('Info\n\nInvalid phone number.');
        }

        // CommonStore.update((s) => {
        //   s.isLoading = false;
        // });

        // setShowLoginPhoneModal(false);

        TempStore.update(s => {
          s.showLoginPhoneModal = false;
        });

        CommonStore.update((s) => {
          s.isLoading = false;
        }, () => {
          setTimeout(() => {
            window.location.reload();
          }, 200);
        });
      } catch (error) {
        // alert("Invalid email and/or password.");

        CommonStore.update((s) => {
          s.isLoading = false;
        });
      }
    } else {
      // logout

      CommonStore.update((s) => {
        s.isLoading = true;
      });

      // firebase
      //   .auth()
      //   .signInAnonymously()
      signInAnonymously(global.auth)
        .then((result) => {
          const firebaseUid = result.user.uid;

          ApiClient.GET(API.getTokenKWeb + firebaseUid).then(async (result) => {
            console.log("getTokenKWeb");
            console.log(result);

            if (result && result.token) {
              await AsyncStorage.setItem("accessToken", result.token);
              await AsyncStorage.setItem("refreshToken", result.refreshToken);

              global.accessToken = result.token;

              UserStore.update((s) => {
                s.firebaseUid = result.userId;
                s.userId = result.userId;
                s.role = result.role;
                s.refreshToken = result.refreshToken;
                s.token = result.token;
                s.name = "";
                s.email = "";
                s.number = "";
              });

              setInputEmail("");
              setInputPassword("");

              setShowLoginModal(false);

              CommonStore.update((s) => {
                s.isLoading = false;
              });
            } else {
              alert("Unauthorized access.");

              CommonStore.update((s) => {
                s.isLoading = false;
              });
            }
          });
        });
    }
  };

  const ringOutlet = async () => {
    if (userCart && userCart.uniqueId) {
      // means dine-in now

      const body = {
        tableId: userCart.tableId,
        tableCode: userCart.tableCode,
        userId: userCart.userId,
        outletId: userCart.outletId,
        userName: userName,
        userNumber: userNumber,
      };

      console.log('body', body)

      ApiClient.POST(API.createUserRing, body).then((result) => {
        if (result && result.status === "success") {
          alert("Success, Ring waiter successfully");
        } else {
          alert("Error, Failed to ring waiter");
        }
      });
    } else if (activeDineInOrders.length > 0) {
      const body = {
        tableId: activeDineInOrders[0].tableId,
        tableCode: activeDineInOrders[0].tableCode,
        userId: activeDineInOrders[0].userId,
        outletId: activeDineInOrders[0].outletId,
        userName: userName,
        userNumber: userNumber,
      };

      ApiClient.POST(API.createUserRing, body).then((result) => {
        if (result && result.status === "success") {
          alert("Success, Ring waiter successfully");
        } else {
          alert("Error, Failed to ring waiter");
        }
      });
    } else {
      console.log('userCart', userCart)
      console.log('userCart.uniqueId', userCart.uniqueId)
      console.log('activeDineInOrders.length', activeDineInOrders.length)
      alert("Info, Only available during dine-in");
    }
  };

  const [isScrollCat, setIsScrollCat] = useState(true);

  const switchCategoryL = () => {
    if (!openSearchBar) {
      // setUserScrolled(false);

      let categoryTemp = {};

      var temp = 0;
      var check = false;

      for (var i = 0; i < filteredOutletCategories.length; i++) {
        if (filteredOutletCategories[i].uniqueId === selectedOutletItemCategory.uniqueId) {
          if (i === 0) {
            temp = filteredOutletCategories.length - 1;
            check = true;
          }
          else {
            temp = i;
          }

          break;
        }
      }

      if (temp === filteredOutletCategories.length - 1 && check) {
        CommonStore.update((s) => {
          s.selectedOutletItemCategory = filteredOutletCategories[temp];
        });

        categoryTemp = filteredOutletCategories[temp];
      }
      else {
        CommonStore.update((s) => {
          s.selectedOutletItemCategory = filteredOutletCategories[temp - 1];
        });

        categoryTemp = filteredOutletCategories[temp - 1];
      }

      if (temp === filteredOutletCategories.length - 1) {
        if (categoryTemp && categoryTemp.uniqueId) {
          catScrollViewRef.current.scrollTo({ x: global.categoryPosXByIdDict[categoryTemp.uniqueId] - 100, animated: true });
        }
      }
      else {
        console.log('scroll calculation');
        console.log(`temp: ${temp}`);

        if (categoryTemp && categoryTemp.uniqueId) {
          catScrollViewRef.current.scrollTo({ x: global.categoryPosXByIdDict[categoryTemp.uniqueId] - 100, animated: true });
        }
      }

      if (selectedOutlet && selectedOutlet.qrUIV2 === true) {
        // 2024-09-09 - no need for v2 ui layout

        // const offset = itemHeights.slice(0, temp - 1).reduce((total, height) => total + height + 10, 0);
        // setIsProgrammaticScroll(true);
        // mainScrollViewRef.current.scrollTo({ y: offset, animated: true });
        // setTimeout(() => {
        //   setIsProgrammaticScroll(false);
        // }, 1000);

      } else {
        setTimeout(() => {
          mainScrollViewRef.current.scrollTo({ x: 0, y: 0, animated: true });
        }, 200);
      }
    }
  };

  const switchCategoryR = () => {
    if (!openSearchBar) {

      if (global.isTestingMode) {
        console.log(`log > before the logic of switchCategoryR | ${moment().format('HH:mm:ss.SSS')}`);
        global.debugText += `\nbefore the logic of switchCategoryR | ${moment().format('HH:mm:ss.SSS')}`;
        setDebugText(global.debugText);
      }

      // setUserScrolled(false);

      let categoryTemp = {};

      var temp = 0;
      var check = false;

      for (var i = 0; i < filteredOutletCategories.length; i++) {
        if (filteredOutletCategories[i].uniqueId === selectedOutletItemCategory.uniqueId) {
          if (i === (filteredOutletCategories.length - 1)) {
            temp = 0;
            check = true;
          }
          else {
            temp = i;
          }

          break;
        }
      }

      if (temp === 0 && check) {
        CommonStore.update((s) => {
          s.selectedOutletItemCategory = filteredOutletCategories[temp];
        });

        categoryTemp = filteredOutletCategories[temp];
      }
      else {
        CommonStore.update((s) => {
          s.selectedOutletItemCategory = filteredOutletCategories[temp + 1];
        });

        categoryTemp = filteredOutletCategories[temp + 1];
      }

      // CommonStore.update((s) => {
      //   s.selectedOutletItemCategory = selectedOutletItemCategory.nextCategory;
      // });

      // categoryTemp = selectedOutletItemCategory.nextCategory;
      // temp = selectedOutletItemCategory.nextCategoryIndex;

      if (global.isTestingMode) {
        console.log(`log > scroll the main ScrollView (horizontal) | ${moment().format('HH:mm:ss.SSS')}`);
        global.debugText += `\nscroll the main ScrollView (horizontal) | ${moment().format('HH:mm:ss.SSS')}`;
        setDebugText(global.debugText);
      }

      if (temp === 0) {
        catScrollViewRef.current.scrollTo({ x: 0, animated: true });
      }
      else {
        console.log('scroll calculation');
        console.log(`temp: ${temp}`);

        // let itemWidth = categoryTemp.name.length * 5;
        // if (global.categoryWidthByNameDict[categoryTemp.name]) {
        //   itemWidth = global.categoryWidthByNameDict[categoryTemp.name];
        // }

        // catScrollViewRef.current.scrollTo({ x: (temp * 100), animated: true });
        // catScrollViewRef.current.scrollTo({ x: ((temp * 50) + (itemWidth)), animated: true });

        if (categoryTemp && categoryTemp.uniqueId) {
          catScrollViewRef.current.scrollTo({ x: global.categoryPosXByIdDict[categoryTemp.uniqueId] - 100, animated: true });
        }
      }

      if (global.isTestingMode) {
        console.log(`log > scroll the main ScrollView (vertical) | ${moment().format('HH:mm:ss.SSS')}`);
        global.debugText += `\nscroll the main ScrollView (vertical) | ${moment().format('HH:mm:ss.SSS')}`;
        setDebugText(global.debugText);
      }

      if (selectedOutlet && selectedOutlet.qrUIV2 === true) {
        // 2024-09-09 - no need for v2 ui layout

        // const offset = itemHeights.slice(0, temp + 1).reduce((total, height) => total + height + 10, 0);
        // setIsProgrammaticScroll(true);
        // mainScrollViewRef.current.scrollTo({ y: offset, animated: true });
        // setTimeout(() => {
        //   setIsProgrammaticScroll(false);
        // }, 1000);

      } else {
        setTimeout(() => {
          // scrollViewRef.current.scrollTo({ x: 0, y: 0, animated: true });
          mainScrollViewRef.current.scrollTo({ x: 0, y: 0, animated: true });
        }, 200);
      }
      // setTimeout(() => {
      //   if (temp == 0 && check) {
      //     CommonStore.update((s) => {
      //       s.selectedOutletItemCategory = filteredOutletCategories[temp];
      //     });
      //   }
      //   else {
      //     CommonStore.update((s) => {
      //       s.selectedOutletItemCategory = filteredOutletCategories[temp + 1];
      //     });
      //   }
      // }, 700);
    }
  };

  const [itemHeights, setItemHeights] = useState([]);
  const [isProgrammaticScroll, setIsProgrammaticScroll] = useState(false);
  const [lastCategoryIndex, setLastCategoryIndex] = useState(null);
  const catRowScrollViewRef = useRef(null);
  const catVerticalScrollViewRef = useRef(null);
  let scrollTimeout;

  const handleScroll = (event) => {
    // clearTimeout(scrollTimeout);
    // scrollTimeout = setTimeout(() => {
    //   if (!isProgrammaticScroll) {
    //     const yOffset = event.nativeEvent.contentOffset.y;

    //     for (let i = 0; i < itemHeights.length; i++) {
    //       const categoryHeight = itemHeights.slice(0, i).reduce((total, height) => total + height, 0);

    //       if (yOffset >= categoryHeight && yOffset < categoryHeight + itemHeights[i]) {
    //         if (lastCategoryIndex !== i) {
    //           scrollCategoryRowToCategory(i);
    //           setLastCategoryIndex(i);
    //         }
    //         break;
    //       }
    //     }
    //   }
    // }, 1000);

    ////////////////////////////////////////////////////////////////

    const yOffset = event.nativeEvent.contentOffset.y;

    let filteredOutletCategoriesSorted = [...filteredOutletCategories].sort((a, b) => {
      return (global.categoryPosYByIdDict[b.uniqueId]) -
        (global.categoryPosYByIdDict[a.uniqueId]);
    })

    for (let i = 0; i < filteredOutletCategoriesSorted.length; i++) {
      const categoryY = global.categoryPosYByIdDict[filteredOutletCategoriesSorted[i].uniqueId];

      if (yOffset >= categoryY) {
        if (filteredOutletCategoriesSorted[i].uniqueId !== selectedOutletItemCategory.uniqueId) {
          scrollTimeout = setTimeout(() => {
            scrollCategoryRowToCategory(filteredOutletCategories.findIndex(cat => cat.uniqueId === filteredOutletCategoriesSorted[i].uniqueId));
          }, 200);
        }

        break;
      }
    }
  };

  // const viewabilityConfig = {
  //   waitForInteraction: true,
  //   // viewAreaCoveragePercentThreshold: ITEM_HEIGHT,
  // }

  // const handleViewableItemsChanged = useCallback((info) => {
  //   console.log('viewable items info', info)
  // }, []);

  // const onItemLayout = (index, event) => {
  //   const { height } = event.nativeEvent.layout;
  //   setItemHeights(prev => {
  //     const updatedHeights = [...prev];
  //     updatedHeights[index] = height;
  //     return updatedHeights;
  //   });
  // };

  const scrollToItem = (item, index) => {
    if (mainScrollViewRef.current) {

      if (item && item.uniqueId) {
        mainScrollViewRef.current.scrollTo({ y: global.categoryPosYByIdDict[item.uniqueId] + 150, animated: true });
        catScrollViewRef.current.scrollTo({ x: global.categoryPosXByIdDict[item.uniqueId] - 100, animated: true });
      }

      setTimeout(() => {
        setIsProgrammaticScroll(false);
      }, 1000);
    }
  };

  const scrollCategoryRowToCategory = (index) => {
    if (!isProgrammaticScroll) {
      CommonStore.update((s) => {
        s.selectedOutletItemCategory = filteredOutletCategories[index];
      });

      if (filteredOutletCategories[index] && filteredOutletCategories[index].uniqueId) {
        const categoryTemp = filteredOutletCategories[index].uniqueId;

        if (catScrollViewRef.current) {
          catScrollViewRef.current.scrollTo({ x: global.categoryPosXByIdDict[categoryTemp] - 150, animated: true });
        }
      }
    }
  };


  /////////////////////////////////////////////////////////////////////////
  // const handleEndReached = () => {
  //   // Perform actions when reaching the bottom of the list
  //   console.log('Reached the end of the list');
  // };

  const mainScrollViewRef = useRef(null);
  const scrollViewRef = useRef(null);
  const catScrollViewRef = useRef(null);
  const flatListRef = useRef(null);

  const [userSwiped, setUserSwiped] = useState('');

  useEffect(() => {
    const container = scrollViewRef.current;
    const handleTouchStart = (e) => {
      startX = e.touches[0].clientX;
      startY = e.touches[0].clientY;
      setUserSwiped('');
      // setDetector(0);
    };

    const handleTouchEnd = (e) => {
      const endX = e.changedTouches[0].clientX;
      const endY = e.changedTouches[0].clientY;

      const deltaX = endX - startX;
      const deltaY = endY - startY;

      const thresholdX = 40;
      const thresholdY = 30;

      if (
        Math.abs(deltaX) > thresholdX &&
        Math.abs(deltaY) < thresholdY
      ) {
        if (deltaX > 0) {
          console.log('Swipe RIGHT detected');
          setUserSwiped('right');
        } else {
          console.log('Swipe LEFT detected');
          setUserSwiped('left');
        }
      }
    };

    container.addEventListener('touchstart', handleTouchStart);
    container.addEventListener('touchend', handleTouchEnd);

    return () => {
      container.removeEventListener('touchstart', handleTouchStart);
      container.removeEventListener('touchend', handleTouchEnd);
    };
  }, []);

  useEffect(() => {
    console.log('selectedOutletItemCategory');
    console.log(selectedOutletItemCategory);

    if (selectedOutletItemCategory &&
      selectedOutletItemCategory.uniqueId === undefined &&
      filteredOutletCategories.length > 0) {
      if (global.selectedOutletItemCategoryTimeoutId) {
        clearTimeout(global.selectedOutletItemCategoryTimeoutId);
      }

      global.selectedOutletItemCategoryTimeoutId = setTimeout(() => {
        CommonStore.update(s => {
          s.selectedOutletItemCategory = filteredOutletCategories[0];
        });
      }, 500);

    }
  }, [selectedOutletItemCategory, filteredOutletCategories]);

  const translateYAnim = useRef(new Animated.Value(100)).current;
  const fadeAnim = new Animated.Value(1);
  const [isNearBottom, setIsNearBottom] = useState(false);

  const [currentScrollY, setCurrentScrollY] = useState(0);
  const [canSwitchCategory, setCanSwitchCategory] = useState(true);

  const isCloseToBottom = ({
    layoutMeasurement,
    contentOffset,
    contentSize,
  }) => {
    const paddingToBottom = 20;
    return (
      layoutMeasurement.height + contentOffset.y >= contentSize.height - paddingToBottom
    );
  };

  const rotateScrollSpinner = ({
    layoutMeasurement,
    contentOffset,
    contentSize,
  }, categoryId) => {
    // var elemScrollBottomSpinner = document.getElementById(`scrollBottomSpinner-${categoryId}`);
    // if (elemScrollBottomSpinner) {
    //   // elemScrollBottomSpinner.style.transform = 'rotate(' + (contentOffset.y / 5) + 'deg)';
    //   setRotateScrollSpinnerStyle('rotate(' + (contentOffset.y / 5) + 'deg)');
    // }    

    if (global.rotateScrollSpinnerTimeoutId) {
      clearTimeout(global.rotateScrollSpinnerTimeoutId);
    }

    global.rotateScrollSpinnerTimeoutId = setTimeout(() => {
      console.log('rotate(' + (contentOffset.y) + 'deg)');

      setRotateScrollSpinnerStyle('rotate(' + (contentOffset.y / 2) + 'deg)');
    }, 100);

    // window.requestAnimationFrame(() => {
    //   setRotateScrollSpinnerStyle('rotate(' + (contentOffset.y) + 'deg)');
    // });
  };

  const animateItemListContent = (direction) => {
    // direction 'left' or 'right'

    setOpacityItemListStyle(0);
    setOpacityTransitionItemListStyle('opacity 0.1s');

    setTimeout(() => {
      setOpacityItemListStyle(1);
      setOpacityTransitionItemListStyle('opacity 1s');
    }, 100);
  };

  ////////////////////////////////////////////////////////////////////////////////////

  // 2024-02-27 - Free to claim voucher, only open to fresh user

  useEffect(async () => {
    // if (cartItems.length > 0) {
    //   updateCartItemsDict();
    // }

    // var claimableVoucherTemp = null;

    // if (anonymousDt !== 'none') {
    //   if (moment().isSame(anonymousDt, 'day')) {
    //     // means same day visit, can proceed (to convert this customer into loyalty customer)

    //     for (let lcIndex = 0; lcIndex < availableLoyaltyCampaigns.length; lcIndex++) {
    //       const currLoyaltyCampaign = availableLoyaltyCampaigns[lcIndex];

    //       if (currLoyaltyCampaign.loyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.RETURN &&
    //         currLoyaltyCampaign.taggableVoucherId) {

    //         // 2025-06-09 - Minimum Spent To Claim
    //         if (currLoyaltyCampaign.minSpentToClaim) {
    //           const lastPlacedOrderPrice = global.lastOrderFinalPrice || 0;

    //           if (
    //             // lastPlacedOrderPrice >= currLoyaltyCampaign.minSpentToClaim
    //             true
    //           ) {
    //             let outletTaggableVoucher = selectedOutletTaggableVouchersAll.find(voucher => voucher.uniqueId === currLoyaltyCampaign.taggableVoucherId)

    //             claimableVoucherTemp = {
    //               ...outletTaggableVoucher,

    //               loyaltyCampaignId: currLoyaltyCampaign.uniqueId,
    //               batchId: 'claim',
    //               batchIndex: -1,
    //             };
    //             break;
    //           }
    //         }

    //         for (var i = 0; i < selectedOutletTaggableVouchersAll.length; i++) {
    //           if (selectedOutletTaggableVouchersAll[i].uniqueId === currLoyaltyCampaign.taggableVoucherId) {
    //             // only proceed if got this voucher tagged in loyalty campaign

    //             // var promoTimeStart = moment().set({
    //             //   hour: moment(selectedOutletTaggableVouchersAll[i].promoTimeStart).hour(),
    //             //   minute: moment(selectedOutletTaggableVouchersAll[i].promoTimeStart).minute(),
    //             // });

    //             // var promoTimeEnd = moment().set({
    //             //   hour: moment(selectedOutletTaggableVouchersAll[i].promoTimeEnd).hour(),
    //             //   minute: moment(selectedOutletTaggableVouchersAll[i].promoTimeEnd).minute(),
    //             // });

    //             if (selectedOutlet && selectedOutlet.uniqueId &&
    //               selectedOutletTaggableVouchersAll[i].voucherQuantity > 0
    //               // &&
    //               // moment().isSameOrAfter(selectedOutletTaggableVouchersAll[i].promoDateStart, 'day') &&
    //               // moment().isSameOrBefore(selectedOutletTaggableVouchersAll[i].promoDateEnd, 'day') &&
    //               // moment().isSameOrAfter(promoTimeStart) &&
    //               // moment().isBefore(promoTimeEnd) 
    //               &&
    //               selectedOutletTaggableVouchersAll[i].isFreeToClaimVoucher) {

    //               if (userEmail) {
    //                 const userTaggableVoucherSnapshot = await getDocs(
    //                   query(
    //                     collection(global.db, Collections.UserTaggableVoucher),
    //                     where('outletId', '==', selectedOutlet.uniqueId),
    //                     where('email', '==', userEmail),
    //                     // where('redeemDate', '==', null),
    //                     where('voucherId', '==', selectedOutletTaggableVouchersAll[i].uniqueId),

    //                     // where('expirationDate', '>', moment().valueOf()),
    //                     // orderBy('expirationDate'),

    //                     // limit(selectedOutletTaggableVouchersAll[i].voucherMaxClaimPerUser ? selectedOutletTaggableVouchersAll[i].voucherMaxClaimPerUser : 1),
    //                     limit(1), // always target for once (special case)
    //                   )
    //                 );

    //                 var isValidToClaim = false;
    //                 if (userTaggableVoucherSnapshot) {
    //                   if (
    //                     // userTaggableVoucherSnapshot.size < selectedOutletTaggableVouchersAll[i].voucherMaxClaimPerUser
    //                     userTaggableVoucherSnapshot.size > 0
    //                   ) {
    //                     isValidToClaim = false;

    //                     // let userTaggableVoucherFound = userTaggableVoucherSnapshot.docs[0].data();

    //                     // if (moment().isSame(userTaggableVoucherFound.createdAt, 'day')) {
    //                     //   // this one should be the voucher that created just now (today), if paid online; just that the expiration date will set to yesterday (in case of user didn't paid online)
    //                     //   // thus, can't claim first (will do a cron job to clear those unclaimed voucher due to unpaid online payments)

    //                     //   isValidToClaim = false;
    //                     // }
    //                     // else {
    //                     //   // means can still claim
    //                     //   isValidToClaim = true;
    //                     // }
    //                   }
    //                   else {
    //                     isValidToClaim = true;
    //                   }
    //                 }

    //                 if (isValidToClaim) {
    //                   claimableVoucherTemp = {
    //                     ...selectedOutletTaggableVouchersAll[i],

    //                     loyaltyCampaignId: currLoyaltyCampaign.uniqueId,
    //                     batchId: 'claim',
    //                     batchIndex: -1,
    //                   };
    //                   break;
    //                 }
    //               }
    //               else {
    //                 // since don't have email, we just assume this user havent claim yet (check again when later we get the user phone number to check)

    //                 claimableVoucherTemp = {
    //                   ...selectedOutletTaggableVouchersAll[i],

    //                   loyaltyCampaignId: currLoyaltyCampaign.uniqueId,
    //                   batchId: 'claim',
    //                   batchIndex: -1,
    //                 };
    //                 break;
    //               }
    //             }
    //           }
    //         }

    //         if (claimableVoucherTemp !== null) {
    //           break;
    //         }
    //       }
    //     }
    //   }
    //   else {
    //     // already second or more visit, no need proceed
    //   }
    // }

    let claimableVoucherTemp = await getClaimableVoucher({
      anonymousDt,
      availableLoyaltyCampaigns,
      selectedOutletTaggableVouchersAll,
      selectedOutlet,
      userEmail,
      lastOrderFinalPrice: global.createUserOrderBody?.finalPrice || 0
    });

    TempStore.update(s => {
      s.claimableVoucher = claimableVoucherTemp;
      // s.claimableVoucher = null; // quick testing
    });
  }, [
    //////////////////////////////////////////

    selectedOutletTaggableVouchersAll,
    // userTaggableVouchers,

    availableLoyaltyCampaigns,

    userIdAnonymous,
    anonymousDt,

    userEmail,
    global.createUserOrderBody

    // selectedTaggableVoucher,

    //////////////////////////////////////////
  ]);

  ////////////////////////////////////////////////////////////////////////////////////

  const [selectedOutletItemsForNewUi, setSelectedOutletItemsForNewUi] = useState([]);

  useEffect(() => {
    if (selectedOutlet && selectedOutlet.qrUIV2) {
      // Create a mapping of categories to maintain order
      const categoryOrderIndexDict = filteredOutletCategories.reduce((obj, category) => {
        obj[category.uniqueId] = {
          orderIndex: category.orderIndex ? category.orderIndex : filteredOutletCategories.length,
          name: category.name,
        };
        return obj;
      }, {});

      // Create a temporary object to group items by category
      const groupedItems = {};

      // Sort items first
      let selectedOutletItemsSorted = selectedOutletItems.slice(0);
      selectedOutletItemsSorted.sort((a, b) => {
        return (a.orderIndex ? a.orderIndex : selectedOutletItemsSorted.length) -
          (b.orderIndex ? b.orderIndex : selectedOutletItemsSorted.length)
      });

      // Group items by category
      selectedOutletItemsSorted.forEach(item => {
        if (categoryOrderIndexDict[item.categoryId]) {
          const categoryId = item.categoryId;
          if (!groupedItems[categoryId]) {
            groupedItems[categoryId] = {
              categoryName: categoryOrderIndexDict[categoryId].name,
              items: [],
            };
          }
          groupedItems[categoryId].items.push(item);
        }
      });

      // Create an ordered array of categories with their items
      const updatedItems = filteredOutletCategories.flatMap(category => {
        const categoryId = category.uniqueId;
        const items = groupedItems[categoryId]?.items || [];

        // Find the first visible item in the category
        let firstVisibleItemIndex = items.findIndex(item =>
          !(item.hideInOrderTypes && item.hideInOrderTypes.includes(orderType))
        );

        // If no visible items, return an empty array
        if (firstVisibleItemIndex === -1) {
          return []; // No visible items in this category
        }

        // Map items to include the firstItemInCategory property
        return items.map((item, index) => ({
          ...item,
          firstItemInCategory: index === firstVisibleItemIndex, // Mark the first visible item
          categoryName: groupedItems[categoryId].categoryName,
        }));
      });

      // Set the updated items for rendering
      setSelectedOutletItemsForNewUi(updatedItems);
    }
  }, [
    selectedOutletItems,
    filteredOutletCategories,
    selectedOutlet,
  ]);
  // useEffect(() => {
  //   console.log('isNearBottom', isNearBottom)
  //   if (isNearBottom) {
  //     Animated.timing(translateYAnim, {
  //       toValue: 0,
  //       duration: 500,
  //       easing: Easing.bezier(0.250, 0.460, 0.450, 0.940),
  //       useNativeDriver: true,
  //     }).start();
  //   } else {
  //     // Reset the translateYAnim value when the user scrolls away from the bottom
  //     Animated.timing(translateYAnim, {
  //       toValue: 100,
  //       duration: 0,
  //       useNativeDriver: true,
  //     }).start();
  //   }
  // }, [isNearBottom, translateYAnim]);

  // const handleScroll = (event) => {
  //   const { contentOffset, layoutMeasurement, contentSize } = event.nativeEvent;
  //   const scrollPosition = contentOffset.y;
  //   const screenHeight = layoutMeasurement.height;
  //   const contentHeight = contentSize.height;

  //   // Calculate how close the user is to the bottom of the screen
  //   const distanceToBottom = contentHeight - scrollPosition - screenHeight;

  //   // Set a threshold value for when to trigger the animation (e.g., 100)
  //   const threshold = 20;
  //   setIsNearBottom(distanceToBottom < threshold);
  // };

  // const handleScroll = ({ nativeEvent }) => {

  //   if (isCloseToBottom(nativeEvent)) {
  //     console.log('BOTTOM')
  //     Animated.timing(translateYAnim, {
  //       toValue: 0,
  //       duration: 500,
  //       easing: Easing.bezier(0.250, 0.460, 0.450, 0.940),
  //       useNativeDriver: true,
  //     }).start();

  //     setNotNearBottom(false);
  //   } 
  //   else if (!notNearBottom) {
  //     console.log('NO BOTTOM')
  //     Animated.parallel([
  //       Animated.timing(fadeAnim, {
  //         toValue: 0,
  //         duration: 400,
  //         easing: Easing.bezier(0.550, 0.085, 0.680, 0.530),
  //         useNativeDriver: true,
  //       }),
  //       // Animated.timing(fontSizeAnim, {
  //       //   toValue: 0,
  //       //   duration: 1200,
  //       //   easing: Easing.bezier(0.550, 0.085, 0.680, 0.530),
  //       //   useNativeDriver: true,
  //       // }),
  //     ]).start();

  //     setNotNearBottom(true);
  //   }
  // };

  // useEffect(() => {
  //   // This is a simple condition to check if the user scrolled to the bottom.
  //   // You can adjust this logic as per your requirements.
  //   console.log('ANIMATION BEFORE', currentScrollY)
  //   if (currentScrollY) {
  //     console.log('ANIMATION', currentScrollY)
  //     setTimeout(() => {
  //       Animated.timing(fadeAnim, {
  //         toValue: 1,
  //         duration: 700,
  //         delay: 1000,
  //         useNativeDriver: true,
  //       }).start();
  //     }, 1000)

  //   }
  //   else {
  //     Animated.timing(fadeAnim, {
  //       toValue: 0,
  //       duration: 700,
  //       easing: Easing.bezier(0.550, 0.085, 0.680, 0.530),
  //       useNativeDriver: true,
  //     }).start();
  //   }
  //   // setCurrentScrollY(false);
  //   console.log('ANIMATION AFTER', currentScrollY)
  // }, [fadeAnim]);

  // const isCloseToBottom = (y) => {
  //   // Set a threshold value for how close the ScrollView should be to the bottom.
  //   // You can adjust this value based on your requirements.
  //   const threshold = 100;
  //   return y >= scrollViewContentHeight - scrollViewContainerHeight - threshold;
  // };

  // const scrollViewContentHeight = windowHeight * 0.5; // Replace with the actual content height of your ScrollView.
  // const scrollViewContainerHeight = windowHeight * 0.6; // Replace with the actual height of your ScrollView container.


  // function end
  // console.log('image', images)
  // console.log('image.length true or false', images.length > 0)
  // console.log('image index check', ((currentIndex + 1) % images.length))
  // console.log('image index', currentIndex);
  // console.log('image index + data', images[currentIndex]);
  // console.log('image uri', images.length > 0 ? images : '')

  const clearStorage = async () => {
    try {
      await AsyncStorage.removeItem('storedUserName');
      await AsyncStorage.removeItem('storedUserPhone');

    } catch (error) {
      console.error('Error clearing storage:', error);
    }
  };

  const LOGOUT_COUNT_KEY = 'logout_count';
  const LOGOUT_DATE_KEY = 'logout_date';

  const getTodayDate = () => {
    const today = new Date();
    return today.toISOString().split('T')[0];
  };

  const checkLogoutAllowed = async () => {
    try {
      const storedDate = await AsyncStorage.getItem(LOGOUT_DATE_KEY);
      const storedCount = await AsyncStorage.getItem(LOGOUT_COUNT_KEY);

      const todayDate = getTodayDate();

      if (storedDate !== todayDate) {
        // It's a new day, reset the count
        await AsyncStorage.setItem(LOGOUT_COUNT_KEY, '0');
        await AsyncStorage.setItem(LOGOUT_DATE_KEY, todayDate);
        return true; // Allow logout
      }

      const logoutCount = parseInt(storedCount, 10) || 0;

      if (logoutCount >= 2) {
        alert('You have reached the maximum logout attempts for today.\n(only allowed to logout twice per day.)');
        return false; // Prevent logout
      }

      return true; // Allow logout

    } catch (error) {
      console.error('Error checking logout:', error);
      return false; // In case of an error, prevent logout
    }
  };

  const incrementLogoutCount = async () => {
    try {
      const storedCount = await AsyncStorage.getItem(LOGOUT_COUNT_KEY);
      const logoutCount = parseInt(storedCount, 10) || 0;
      console.log('logout count: ', logoutCount);
      await AsyncStorage.setItem(LOGOUT_COUNT_KEY, (logoutCount + 1).toString());

    } catch (error) {
      console.error('Error incrementing logout count:', error);
    }
  };

  const handleLogout = async () => {
    const canLogout = await checkLogoutAllowed();

    if (canLogout) {
      await clearStorage();
      await incrementLogoutCount();
      // await loginLogoutUserAccount();

      await loginUserAccountByPhone();
    }
  };


  /// function end
  return (
    <View
      nativeID="rootOutletMenu"
      style={[
        styles.container,
        {
          width: windowWidth,
          height: windowHeight,
        },
      ]}
    >
      {/* <SafeAreaProvider>
        <MenuItemDetailModal />
      </SafeAreaProvider> */}

      <MenuItemDetailModal />
      <VoucherBundle />

      {/* 2024-08-06 temporary */}
      {/* <VoucherPromotionItemsRedeem/> */}

      {
        (isMobile() && !showGreetingPopup)
          ?
          <RedeemVoucherModal linkToFunc={linkToFunc} />
          :
          <></>
      }

      {
        (isMobile() && !showGreetingPopup)
          ?
          <RewardsModal />
          :
          <></>
      }

      {
        (isMobile() && !showGreetingPopup)
          ?
          <RewardsModalEU />
          :
          <></>
      }

      <Modal
        style={{ flex: 1 }}
        visible={cartWarning}
        transparent={true}
        animationType="slide"
      >
        <View
          style={{
            backgroundColor: "rgba(0,0,0,0.5)",
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            minHeight: windowHeight,
          }}
        >
          <View style={styles.confirmBox}>
            <TouchableOpacity
              onPress={() => {
                // setState({ cartWarning: false });
                setCartWarning(false);
              }}
            >
              <View
                style={{
                  alignSelf: "flex-start",
                  padding: 14,
                }}
              >
                <AntDesign name="close" size={28} color={"#b0b0b0"} />
              </View>
            </TouchableOpacity>
            <View style={{ marginBottom: 10, paddingHorizontal: 10 }}>
              <Text
                style={{
                  textAlign: "center",
                  // fontWeight: '700',
                  fontSize: 18,
                  fontFamily: "NunitoSans-Bold",
                }}
              >
                You are entering a different outlet
              </Text>
              <Text
                style={{
                  textAlign: "center",
                  // fontWeight: '700',
                  fontSize: 14,
                  fontFamily: "NunitoSans-Regular",
                  marginTop: 10,
                }}
              >
                Your existing cart items will be cleared if you proceed. Are you
                sure?
              </Text>
            </View>
            <View
              style={{
                alignSelf: "center",
                marginTop: 30,
                justifyContent: "center",
                alignItems: "center",
                // width: 250,
                // height: 40,
                alignContent: "center",
                marginTop: 12,
              }}
            >
              <TouchableOpacity
                style={{
                  backgroundColor: Colors.primaryColor,
                  width: "100%",
                  justifyContent: "center",
                  alignItems: "center",
                  alignContent: "center",
                  borderRadius: 10,
                  // height: 60,
                  // marginTop: 30,
                  alignSelf: "center",

                  width: windowWidth * 0.5,
                  // paddingHorizontal: 60,
                  paddingVertical: 10,
                }}
                onPress={async () => {
                  // setState({ cartWarning: false, })
                  // Cart.clearCart();
                  setCartWarning(false);

                  await clearCartItems();

                  if (
                    tempItem.isActive &&
                    (tempItem.isAvailableDayActive
                      ? (tempItem.effectiveTypeOptions.includes(effectiveDays) &&
                        tempItem.effectiveStartTime && tempItem.effectiveEndTime &&
                        moment().isSameOrAfter(
                          moment(tempItem.effectiveStartTime)
                            .year(moment().year())
                            .month(moment().month())
                            .date(moment().date())
                        )
                        &&
                        moment().isBefore
                          (moment(tempItem.effectiveEndTime)
                            .year(moment().year())
                            .month(moment().month())
                            .date(moment().date())
                          ))
                      : true) &&
                    (tempItem.isOnlineMenu !== undefined
                      ? tempItem.isOnlineMenu
                      : true) &&
                    (tempItem.isStockCountActive !== undefined &&
                      tempItem.isStockCountActive !== false &&
                      tempItem.stockCount !== undefined &&
                      tempItem.toSellIgnoreStock !== undefined
                      ? tempItem.isStockCountActive &&
                      tempItem.stockCount > 0 &&
                      (tempItem.toSellIgnoreStock !== undefined
                        ? tempItem.toSellIgnoreStock
                        : true)
                      : true)
                  ) {
                    CommonStore.update((s) => {
                      s.selectedOutletItem = tempItem;

                      s.selectedOutletItemAddOn = {};
                      s.selectedOutletItemAddOnChoice = {};
                      s.selectedOutletItemAddOnOi = {};

                      // s.selectedAddOnIdForChoiceQtyDict = {};
                    });

                    // const subdomain = await AsyncStorage.getItem(
                    //   "latestSubdomain"
                    // );

                    // if (!subdomain) {
                    //   linkTo && linkTo(`${prefix}/outlet/menu/item`);
                    // } else {
                    //   linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu/item`);
                    // }

                    // linkTo && linkTo(`${prefix}/outlet/menu/item`);

                    props.navigation.navigate("MenuItemDetails", {
                      refresh: refresh.bind(this),
                      menuItem: tempItem,
                      outletData: selectedOutlet,
                    });
                  } else {
                    window.confirm(
                      "Info",
                      "Sorry, this product is not available at the moment."
                    );
                  }
                }}
              >
                <Text
                  style={{
                    fontSize: 20,
                    color: Colors.whiteColor,
                    fontFamily: "NunitoSans-SemiBold",
                  }}
                >
                  Proceed
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  setCartWarning(false);
                }}
                style={{
                  backgroundColor: Colors.secondaryColor,
                  width: "100%",
                  justifyContent: "center",
                  alignItems: "center",
                  alignContent: "center",
                  borderRadius: 10,
                  // height: 60,

                  width: windowWidth * 0.5,
                  marginTop: 10,
                  // paddingHorizontal: 60,
                  paddingVertical: 10,
                }}
              >
                <Text
                  style={{
                    fontSize: 20,
                    color: Colors.whiteColor,
                    fontFamily: "NunitoSans-SemiBold",
                  }}
                >
                  Take me back
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* <Modal
        style={{ flex: 1 }}
        visible={showLoginModal}
        transparent={true}
        animationType="slide"
      >
        <View
          style={{
            backgroundColor: "rgba(0,0,0,0.5)",
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            minHeight: windowHeight,
          }}
        >
          <View
            style={[
              styles.confirmBox,
              {
                ...(email && {
                  height: 240,
                }),
              },
            ]}
          >
            <TouchableOpacity
              onPress={() => {
                setShowLoginModal(false);
              }}
            >
              <View
                style={{
                  alignSelf: "flex-end",
                  padding: 14,
                }}
              >
                <AntDesign name="closecircle" size={28} color={"#b0b0b0"} />
              </View>
            </TouchableOpacity>
            <View style={{ marginBottom: 10, paddingHorizontal: 10 }}>
              <Text
                style={{
                  textAlign: "center",
                  // fontWeight: '700',
                  fontSize: 18,
                  fontFamily: "NunitoSans-Bold",
                }}
              >
                {!email
                  ? "Login to enjoy rewards now!"
                  : "Are your sure you want to logout?"}
              </Text>
            </View>
            <View
              style={{
                alignSelf: "center",
                marginTop: 30,
                justifyContent: "center",
                alignItems: "center",
                // width: 250,
                // height: 40,
                alignContent: "center",
                marginTop: 12,
              }}
            >
              {!email ? (
                <>
                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                      marginVertical: 5,
                      //borderWidth: 1,
                      //borderColor: 'red',
                    }}
                  >
                    <View style={{ justifyContent: "center" }}>
                      <Text
                        style={{
                          fontFamily: "NunitoSans-Bold",
                          color: "Black",
                          fontSize: 16,
                          textAlign: "center",
                        }}
                      >{`Email        :`}</Text>
                    </View>
                    <TextInput
                      value={inputEmail}
                      onChangeText={(text) => {
                        setInputEmail(text);
                      }}
                      style={[
                        styles.textInput,
                        {
                          // width: isMobile() ? windowWidth * 0.4 : windowWidth * 0.325,
                          width: "100%",
                          height: isMobile() ? 35 : windowHeight * 0.04,
                          borderRadius: 10,
                          borderColor: "#E5E5E5",

                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 0,
                          marginLeft: isMobile() ? 20 : 30,

                          paddingHorizontal: 10,
                        },
                      ]}
                      underlineColorAndroid={Colors.fieldtBgColor}
                      clearButtonMode="while-editing"
                      placeholder="Enter your email"
                    />
                  </View>

                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                      marginVertical: 5,
                      //borderWidth: 1,
                      //borderColor: 'red',
                    }}
                  >
                    <View style={{ justifyContent: "center" }}>
                      <Text
                        style={{
                          fontFamily: "NunitoSans-Bold",
                          color: "Black",
                          fontSize: 16,
                          textAlign: "center",
                        }}
                      >{`Password :`}</Text>
                    </View>
                    <TextInput
                      value={inputPassword}
                      onChangeText={(text) => {
                        setInputPassword(text);
                      }}
                      style={[
                        styles.textInput,
                        {
                          // width: isMobile() ? windowWidth * 0.4 : windowWidth * 0.325,
                          width: "100%",
                          height: isMobile() ? 35 : windowHeight * 0.04,
                          borderRadius: 10,
                          borderColor: "#E5E5E5",

                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 0,
                          marginLeft: isMobile() ? 20 : 30,

                          paddingHorizontal: 10,
                        },
                      ]}
                      underlineColorAndroid={Colors.fieldtBgColor}
                      clearButtonMode="while-editing"
                      placeholder="Enter your password"
                      secureTextEntry={true}
                    />
                  </View>
                </>
              ) : (
                <></>
              )}

              <TouchableOpacity
                disabled={isLoading}
                style={
                  {
                    // backgroundColor: "white",
                    // shadowColor: "#000",
                    // shadowOffset: {
                    //   width: 0,
                    //   height: 1,
                    // },
                    // shadowOpacity: 0.22,
                    // shadowRadius: 2.22,
                    // elevation: 9,
                    // bottom: -10,
                    // bottom: 45,
                  }
                }
                onPress={() => {
                  loginLogoutUserAccount();
                }}
              >
                <View
                  style={{
                    backgroundColor: Colors.primaryColor,
                    padding: 28,
                    paddingVertical: 12,
                    borderRadius: 10,
                    alignItems: "center",

                    marginHorizontal: 48,
                    marginTop: 30,
                    marginBottom: 24,

                    shadowColor: "#000",
                    shadowOffset: {
                      width: 0,
                      height: 1,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 2.22,
                    elevation: 3,

                    ...(!isMobile() && {
                      width: "100%",
                      alignSelf: "center",
                    }),
                  }}
                >
                  <Text
                    style={{
                      color: "#ffffff",
                      fontSize: 16,
                      fontFamily: "NunitoSans-Bold",
                    }}
                  >
                    {isLoading ? "LOADING..." : email ? "LOGOUT" : "LOGIN"}
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal> */}

      <Modal
        style={{ flex: 1 }}
        visible={showLoginPhoneModal}
        transparent={true}
        animationType="slide"
      >
        <View
          style={{
            backgroundColor: "rgba(0,0,0,0.5)",
            // flex: 1,
            justifyContent: "center",
            alignItems: "center",
            minHeight: windowHeight,
          }}
        >
          <View
            style={[
              styles.confirmBox,
              {
                // height: windowHeight * 0.35,
                height: windowHeight * 0.35,

                // ...(email && {
                //   // height: 240,

                //   height: windowHeight * 0.3,
                // }),

                ...isMobile() && {
                  height: windowHeight * 0.36,
                },
              },
            ]}
          >
            <TouchableOpacity
              onPress={() => {
                // setShowLoginPhoneModal(false);

                TempStore.update(s => {
                  s.showLoginPhoneModal = false;
                });
              }}
            >
              <View
                style={{
                  alignSelf: "flex-end",
                  padding: 14,
                }}
              >
                <AntDesign name="closecircle" size={28} color={"#b0b0b0"} />
              </View>
            </TouchableOpacity>
            <View style={{ marginBottom: 10, paddingHorizontal: 10 }}>
              <Text
                style={{
                  textAlign: "center",
                  // fontWeight: '700',
                  fontSize: 18,
                  fontFamily: "NunitoSans-Bold",
                }}
              >
                {!email
                  ? "Login to enjoy rewards now!"
                  : "Are your sure you want to logout?"}
              </Text>
              {/* <Text
                style={{
                  textAlign: "center",
                  // fontWeight: '700',
                  fontSize: 14,
                  fontFamily: "NunitoSans-Regular",
                  marginTop: 10,
                }}
              >
                Your existing cart items will be cleared if you proceed. Are you
                sure?
              </Text> */}
            </View>
            <View
              style={{
                alignSelf: "center",
                marginTop: 30,
                justifyContent: "center",
                alignItems: "center",
                // width: 250,
                // height: 40,
                alignContent: "center",
                marginTop: 12,
              }}
            >
              {!email ? (
                <>
                  {/* <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                      marginVertical: 5,
                      //borderWidth: 1,
                      //borderColor: 'red',
                    }}
                  >
                    <View style={{ justifyContent: "center" }}>
                      <Text
                        style={{
                          fontFamily: "NunitoSans-Bold",
                          color: "Black",
                          fontSize: 16,
                          textAlign: "center",
                        }}
                      >{`Name  :`}</Text>
                    </View>
                    <TextInput
                      value={inputName}
                      onChangeText={(text) => {
                        setInputName(text);
                      }}
                      style={[
                        styles.textInput,
                        {
                          // width: isMobile() ? windowWidth * 0.4 : windowWidth * 0.325,
                          width: "100%",
                          height: isMobile() ? 35 : windowHeight * 0.04,
                          borderRadius: 10,
                          borderColor: "#E5E5E5",

                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 0,
                          marginLeft: isMobile() ? 20 : 30,

                          paddingHorizontal: 10,
                        },
                      ]}
                      underlineColorAndroid={Colors.fieldtBgColor}
                      clearButtonMode="while-editing"
                      placeholder="Enter your name"
                    />
                  </View> */}

                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                      marginVertical: 5,
                      //borderWidth: 1,
                      //borderColor: 'red',
                    }}
                  >
                    <View style={{ justifyContent: "center" }}>
                      <Text
                        style={{
                          fontFamily: "NunitoSans-Bold",
                          color: "Black",
                          fontSize: 16,
                          textAlign: "center",
                        }}
                      >{`Phone :`}</Text>
                    </View>
                    <TextInput
                      value={inputPhone}
                      onChangeText={(text) => {
                        setInputPhone(text);
                      }}
                      style={[
                        styles.textInput,
                        {
                          // width: isMobile() ? windowWidth * 0.4 : windowWidth * 0.325,
                          width: "100%",
                          height: isMobile() ? 35 : windowHeight * 0.04,
                          borderRadius: 10,
                          borderColor: "#E5E5E5",

                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 0,
                          marginLeft: isMobile() ? 20 : 30,

                          paddingHorizontal: 10,
                        },
                      ]}
                      underlineColorAndroid={Colors.fieldtBgColor}
                      clearButtonMode="while-editing"
                      placeholder="60123456789"
                      // secureTextEntry={true}
                      keyboardType={'phone-pad'}
                    />
                  </View>

                  {(selectedOutlet && selectedOutlet.tickTerms) ? (
                    <View
                      style={{
                        marginVertical: 5,
                        marginHorizontal: 45,
                        flexDirection: "row",
                        justifyContent: 'center',
                      }}
                    >
                      <CheckBox
                        style={{
                          marginVertical: 2,
                        }}
                        value={
                          tickboxConsent
                        }
                        onValueChange={() => {
                          if (tickboxConsent == true) {
                            TempStore.update(s => { s.tickboxConsent = false; })
                          } else {
                            TempStore.update(s => { s.tickboxConsent = true; })
                          }
                        }}
                      />

                      <Text style={{ paddingHorizontal: 10 }}>
                        {'By logging in, you agree to MyKoodoo '}
                        <Text style={{ color: 'blue', textDecorationLine: 'underline' }}
                          onPress={() => { Linking.openURL('https://mykoodoo.com/terms/') }}>{'Terms of Use'}</Text>
                        {' & '}
                        <Text style={{ color: 'blue', textDecorationLine: 'underline' }}
                          onPress={() => { Linking.openURL('https://mykoodoo.com/privacy-policy/') }}>{'Privacy Policy'}</Text>
                      </Text>
                    </View>
                  ) : (
                    <></>
                  )}
                </>
              ) : (
                <></>
              )}

              <TouchableOpacity
                disabled={isLoading}
                style={
                  {
                    // backgroundColor: "white",
                    // shadowColor: "#000",
                    // shadowOffset: {
                    //   width: 0,
                    //   height: 1,
                    // },
                    // shadowOpacity: 0.22,
                    // shadowRadius: 2.22,
                    // elevation: 9,
                    // bottom: -10,
                    // bottom: 45,
                  }
                }
                onPress={() => {
                  if (email) {
                    handleLogout();
                  } else if (tickboxConsent !== true) {
                    Toastify({
                      text: 'Please agree to MyKoodoo Terms of Use and Privacy Policy before continuing.',
                      duration: 3000,
                      newWindow: true,
                      close: false,
                      gravity: "top", // top or bottom
                      position: "center", // left, center or right
                      stopOnFocus: true, // Prevents dismissing of toast on hover
                      style: {
                        background: "linear-gradient(to right, #4E9F7D, #75bd9f)",
                        color: 'white',

                        // marginLeft: '15px !important',
                        // marginRight: '15px !important',
                      },
                      onClick: function () { } // Callback after click
                    }).showToast();
                  } else {
                    loginUserAccountByPhone();
                  }
                  // clearStorage();
                  // //loginLogoutUserAccount();

                  // loginUserAccountByPhone();
                }}
              >
                <View
                  style={{
                    backgroundColor: Colors.primaryColor,
                    // padding: 28,
                    // paddingVertical: 12,
                    padding: 20,
                    paddingVertical: 12,
                    borderRadius: 10,
                    alignItems: "center",

                    marginHorizontal: 48,
                    marginTop: 30,
                    marginBottom: 24,

                    shadowColor: "#000",
                    shadowOffset: {
                      width: 0,
                      height: 1,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 2.22,
                    elevation: 3,

                    ...(!isMobile() && {
                      width: "100%",
                      alignSelf: "center",
                    }),
                  }}
                >
                  <Text
                    style={{
                      color: "#ffffff",
                      fontSize: 16,
                      fontFamily: "NunitoSans-SemiBold",
                    }}
                  >
                    {isLoading ? "LOADING..." : email ? "LOGOUT" : "LOGIN"}
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      <Modal
        style={{ flex: 1 }}
        visible={showClaimVoucher}
        transparent={true}
        animationType="fade"
      >
        <View
          style={{
            backgroundColor: Colors.modalBgColor,
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            minHeight: windowHeight,
          }}
        >
          <View
            style={{
              width: isMobile() ? 350 : windowWidth * 0.25,
              // height: windowHeight * 0.35,
              height: 600,
              borderRadius: 20,
              backgroundColor: Colors.whiteColor,
            }}
          >
            <TouchableOpacity
              onPress={() => {
                TempStore.update(s => {
                  s.showClaimVoucher = false;
                });
              }}
            >
              <View
                style={{
                  alignSelf: "flex-end",
                  padding: 14,
                }}
              >
                <AntDesign
                  name="close"
                  size={28}
                  color={Colors.fieldtTxtColor}
                />
              </View>
            </TouchableOpacity>
            <View style={{
              // /marginBottom: 10,
              //paddingHorizontal: 10,
              alignSelf: 'center'
            }}>
              <Text
                style={{
                  textAlign: "center",
                  // fontWeight: '700',
                  fontSize: 18,
                  fontFamily: "NunitoSans-Bold",
                }}
              >
                {`${(claimVoucherList ? claimVoucherList : []).length} ${claimVoucherList.length > 1 ? 'vouchers' : 'voucher'} available`}
                {/* {`${dummyVoucherList.length} Voucher(s) available`} */}
              </Text>
              <Text
                style={{
                  width: isMobile() ? 210 : '100%',
                  textAlign: "center",
                  // fontWeight: '700',
                  color: Colors.descriptionColor,
                  fontSize: 12,
                  fontFamily: "NunitoSans-Regular",
                  marginTop: 5,
                }}
              >
                You curently have enough points to claim the {claimVoucherList.length > 1 ? 'vouchers' : 'voucher'} below
              </Text>
            </View>
            {claimVoucherList && claimVoucherList.length > 0 ? (
              <>
                {claimVoucherList && claimVoucherList.length > 0 && (
                  <ScrollView style={{
                    marginBottom: 10,
                    paddingHorizontal: 10,
                    alignSelf: 'center'
                  }}
                    showsVerticalScrollIndicator={isMobile() ? true : false}
                  >
                    <FlatList
                      data={claimVoucherList}
                      renderItem={renderVoucherList}
                      keyExtractor={(item, index) => String(index)}
                      contentContainerStyle={{
                        paddingLeft: 10,
                        paddingRight: 10,
                        paddingTop: 20,

                        paddingBottom: 50,
                      }}
                    />
                  </ScrollView>
                )}
              </>
            )
              : (
                <View
                  style={{
                    justifyContent: 'center',
                    alignItems: 'center',
                    // marginTop: 30,
                    alignSelf: 'center',
                  }}>
                  <Text style={{
                    fontFamily: 'NunitoSans-Regular',
                    color: Colors.descriptionColor,
                    fontSize: 14,
                    textAlign: 'center',
                    marginTop: '2%',
                  }}>
                    This outlet currently don't have any rewards available
                  </Text>
                </View>
              )}

            {/* <View
              style={{
                alignSelf: "center",
                //marginTop: 10,
                justifyContent: "center",
                alignItems: "center",
                // width: 250,
                // height: 40,
                alignContent: "center",
                //marginTop: 12,

                flexDirection: 'row'
              }}
            >
              <TouchableOpacity
                disabled={isLoading}
                style={{}}
                onPress={() => {
                  setOnEnter(false);

                  if (email) {
                    setRedeemYes(true);
                  }
                  else {

                    setRedeemAnon(true);
                  }
                }}
              >
                <View
                  style={{
                    backgroundColor: Colors.primaryColor,
                    padding: 28,
                    paddingVertical: 12,
                    borderRadius: 10,
                    alignItems: "center",

                    marginHorizontal: 48,
                    marginTop: 30,
                    marginBottom: 24,

                    shadowColor: "#000",
                    shadowOffset: {
                      width: 0,
                      height: 1,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 2.22,
                    elevation: 3,

                    ...(!isMobile() && {
                      width: "70%",
                      alignSelf: "center",
                    }),
                  }}
                >
                  <Text
                    style={{
                      color: "#ffffff",
                      fontSize: 16,
                      fontFamily: "NunitoSans-Bold",
                    }}
                  >
                    YES
                  </Text>
                </View>
              </TouchableOpacity>
              <TouchableOpacity
                disabled={isLoading}
                style={{}}
                onPress={() => {
                  setOnEnter(false);
                }}
              >
                <View
                  style={{
                    backgroundColor: Colors.primaryColor,
                    padding: 28,
                    paddingVertical: 12,
                    borderRadius: 10,
                    alignItems: "center",

                    marginHorizontal: 48,
                    marginTop: 30,
                    marginBottom: 24,

                    shadowColor: "#000",
                    shadowOffset: {
                      width: 0,
                      height: 1,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 2.22,
                    elevation: 3,

                    ...(!isMobile() && {
                      width: "70%",
                      alignSelf: "center",
                    }),
                  }}
                >
                  <Text
                    style={{
                      color: "#ffffff",
                      fontSize: 16,
                      fontFamily: "NunitoSans-Bold",
                    }}
                  >
                    NO
                  </Text>
                </View>
              </TouchableOpacity>
            </View> */}
          </View>
        </View>
      </Modal>

      <ScrollView
        ref={mainScrollViewRef}
        stickyHeaderIndices={[6]}
        showsHorizontalScrollIndicator={false}
        // onScroll={onContainerScroll}
        // ref={scrollViewRef}
        // onTouchStart={() => {
        //   setIsScrollCat(false);
        // }}
        // onTouchEnd={() => {
        //   if (userSwiped == 'left' && !isScrollCat) {
        //     switchCategoryR()
        //   }
        //   else if (userSwiped == 'right' && !isScrollCat) {
        //     switchCategoryL()
        //   }
        //   setIsScrollCat(true);
        // }}
        onScroll={
          selectedOutlet && selectedOutlet.qrUIV2 === true ?
            handleScroll
            :
            ({ nativeEvent }) => {
              // rotateScrollSpinner(nativeEvent, selectedOutletItemCategory.uniqueId);

              // // console.log(`log > before checking isCloseToBottom | ${moment().format('HH:mm:ss.SSS')}`);

              // if (
              //   isCloseToBottom(nativeEvent) && global.canSwitchCategory
              //   && global.canSwipeItemList
              //   // && scrollDirection === 'UP'
              // ) {
              //   if (global.isTestingMode) {
              //     console.log(`log > before calling switchCategoryR | ${moment().format('HH:mm:ss.SSS')}`);
              //     global.debugText = `\n\n\nbefore calling switchCategoryR | ${moment().format('HH:mm:ss.SSS')}`;
              //     setDebugText(global.debugText);
              //   }

              //   // setCanSwitchCategory(false);
              //   global.canSwitchCategory = false;

              //   switchCategoryR();
              //   // setCurrentScrollY(0);

              //   setTimeout(() => {
              //     // console.log(`canSwitchCategory: reset to true`);

              //     // setCanSwitchCategory(true);
              //     global.canSwitchCategory = true;
              //   }, 250);
              // }
            }}
        scrollEventThrottle={1000}
        style={{
          position: "relative",
          //backgroundColor: 'blue'
        }}
      // stickyHeaderIndices={[1]}
      >

        {isMobile() ? (
          images.length > 1 ? (
            <View style={{ position: 'relative' }}>
              <View
                style={{
                  position: 'absolute',
                  bottom: 15,
                  left: 15,
                  zIndex: 1000,
                  paddingHorizontal: 7,
                  backgroundColor: 'white',
                  opacity: 0.8,
                  borderRadius: 10,
                }}
              >
                <Animated.Text
                  style={{
                    opacity: imageOpacity,
                    fontSize: 16,
                    fontFamily: 'NunitoSans-SemiBold',
                  }}
                >
                  {currentIndex === 0 ? '' : promoName[currentIndex - 1]}
                </Animated.Text>
              </View>
              <Animated.Image
                style={{
                  height: windowHeight * 0.25,
                  opacity: imageOpacity,
                  ...(selectedOutlet && selectedOutlet.portraitCoverOptions === true && {
                    height: windowHeight * 0.6,
                    resizeMode: 'stretch',
                  })
                }}
                source={{ uri: images[currentIndex] }}
              />
            </View>
          ) : (
            // 2024-07-03 - testing

            <>
              {
                !global.isTestingMode
                  ?
                  <AsyncImage
                    source={{
                      uri:
                        selectedOutlet && selectedOutlet.cover
                          ? selectedOutlet.cover
                          : '',
                    }}
                    hideLoading={true}
                    item={selectedOutlet}
                    style={[
                      styles.outletCover,
                      {
                        height: windowHeight * 0.25,
                        ...(selectedOutlet && selectedOutlet.portraitCoverOptions === true && {
                          height: windowHeight * 0.6,
                          resizeMode: 'stretch',
                        })
                      },
                    ]}
                  />
                  :
                  <Text
                    style={[
                      styles.outletCover,
                      {
                        height: windowHeight * 0.25,
                        ...(selectedOutlet && selectedOutlet.portraitCoverOptions === true && {
                          height: windowHeight * 0.6,
                          resizeMode: 'stretch',
                        })
                      },
                    ]}
                  >
                    {debugText}
                  </Text>
              }
            </>
          )
        ) : (
          <View
            style={{
              width: '100%',
              height: windowHeight * 0.5,
            }}
          >
            <AsyncImage
              source={{
                uri:
                  selectedOutlet && selectedOutlet.cover
                    ? selectedOutlet.cover
                    : '',
              }}
              hideLoading={true}
              item={selectedOutlet}
              style={[
                styles.outletCover,
                {
                  height: windowHeight * 0.5,
                },
              ]}
            />
          </View>
        )}
        {/* {isMobile() ? (
          <AsyncImage
            source={{
              uri:
                selectedOutlet && selectedOutlet.cover
                  ? selectedOutlet.cover
                  : "",
            }}
            hideLoading={true}
            item={selectedOutlet}
            style={[
              styles.outletCover,
              {
                height: isMobile() ? windowHeight * 0.25 : windowHeight * 0.5,
              },
            ]}
          />
        ) : (
          <View
            style={{
              width: "100%",
              // height: 300,
              height: windowHeight * 0.5,
            }}
          >
            <AsyncImage
              source={{
                uri:
                  selectedOutlet && selectedOutlet.cover
                    ? selectedOutlet.cover
                    : "",
              }}
              hideLoading={true}
              item={selectedOutlet}
              style={[
                styles.outletCover,
                {
                  height: isMobile() ? undefined : windowHeight * 0.5,
                },
              ]}
            />
          </View>
        )} */}

        {/* 2023-06-20 - Hide the manual login first */}
        {
          // email
          (email || (selectedOutlet
            // &&
            // (
            //   selectedOutlet.subdomain === 'hominsan-ss15' ||
            //   selectedOutlet.subdomain.startsWith('demo')
            // )
          ))
            ?
            <View
              style={{
                position: "absolute",
                right: 24,
                top: 12,
                padding: 8,
                ...isMobile() && {
                  paddingHorizontal: 11,
                  right: 20,
                },
                backgroundColor: "#ffffff",
                borderRadius: 25,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",

                flexDirection: "row",
              }}
            >
              {
                email
                  // true
                  ? (
                    <Text
                      style={{
                        fontFamily: "NunitoSans-Regular",
                        fontSize: 16,
                        color: Colors.mainTxtColor,

                        marginRight: 15,
                      }}
                    >
                      {/* {(email.startsWith('user-') && email.length === 28) ? `+${userNumber}` : email} */}
                      {(email.startsWith('user-') && email.endsWith("@mykoodoo.com")) ? `+${userNumber}` : email}
                    </Text>
                  ) : (
                    <></>
                  )}

              <TouchableOpacity
                testID="userLogin"
                style={{ alignItems: "center" }}
                onPress={() => {
                  // 2024-03-01 - disable logout function first

                  // setShowLoginModal(true);

                  // if (!email) {
                  // setShowLoginPhoneModal(true);

                  TempStore.update(s => {
                    s.showLoginPhoneModal = true;
                  });
                  // }
                }}
              >
                <FontAwesome
                  name={"user"}
                  color={Colors.primaryColor}
                  size={24}
                  style={{
                    top: 1,
                    //borderRadius: 25,
                    //padding: 8,
                  }}
                />
              </TouchableOpacity>
            </View>
            :
            <></>
        }

        {selectedOutlet && selectedOutlet.subdomain ? (
          <View
            style={{
              position: "absolute",
              right: 20,
              // top: 60, // 12
              top: (email || (
                selectedOutlet
                // selectedOutlet.subdomain === 'hominsan-ss15' ||
                // selectedOutlet.subdomain.startsWith('demo')
              )
              ) ? 60 : 12,
              padding: 8,
              backgroundColor: "#ffffff",
              borderRadius: 25,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <TouchableOpacity
              testID="buyVouchers"
              style={{ alignSelf: "center" }}
              onPress={() => {
                linkTo &&
                  linkTo(
                    `${prefix}/outlet/${selectedOutlet.subdomain}/buy-vouchers`
                  );
              }}
            >
              <MaterialCommunityIcons
                name={"ticket-confirmation-outline"}
                color={Colors.primaryColor}
                size={24}
                style={{
                  top: 1,
                }}
              />
            </TouchableOpacity>
          </View>
        ) : (
          <></>
        )}

        {selectedOutlet &&
          (
            selectedOutlet.subdomain === 'hominsanttdi' ||
            selectedOutlet.subdomain === 'hominsan-ss15' ||
            selectedOutlet.subdomain === 'dream-coffee-pastry' ||
            selectedOutlet.subdomain === 'kopi-empat-petang' ||
            selectedOutlet.subdomain === 'melvin-cafe' ||
            selectedOutlet.subdomain === 'seafarer-cr' ||
            (selectedOutlet.subdomain && selectedOutlet.subdomain.startsWith('demo-cafe')) ||
            selectedOutlet.pOr
          ) && !isMobile() && (email || userNumber) ? (
          <View
            style={{
              position: "absolute",
              right: 20,
              top: 110, // 12
              padding: 8,
              backgroundColor: "#ffffff",
              borderRadius: 25,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <TouchableOpacity
              style={{ alignSelf: "center" }}
              onPress={() => {
                linkTo &&
                  linkTo(
                    `${prefix}/outlet/${selectedOutlet.subdomain}/outlet-rewards`
                  );
              }}
            >
              <MaterialCommunityIcons
                name={"gift-outline"}
                color={Colors.primaryColor}
                size={24}
                style={{
                  top: 1,
                }}
              />
            </TouchableOpacity>
          </View>
        ) : (
          <></>
        )}

        {selectedOutlet && (
          selectedOutlet.subdomain === 'hominsanttdi' ||
          selectedOutlet.subdomain === 'hominsan-ss15' ||
          selectedOutlet.subdomain === 'dream-coffee-pastry' ||
          selectedOutlet.subdomain === 'kopi-empat-petang' ||
          selectedOutlet.subdomain === 'melvin-cafe' ||
          selectedOutlet.subdomain === 'seafarer-cr' ||
          (selectedOutlet.subdomain && selectedOutlet.subdomain.startsWith('demo-cafe')) ||
          selectedOutlet.pOr
        ) && isMobile() && (email || userNumber) ? (
          <View
            style={{
              position: "absolute",
              left: 20,
              top: 12,
              padding: 8,
              backgroundColor: "#ffffff",
              borderRadius: 25,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <TouchableOpacity
              style={{ alignSelf: "center" }}
              onPress={() => {
                linkTo &&
                  linkTo(
                    `${prefix}/outlet/${selectedOutlet.subdomain}/outlet-rewards`
                  );
              }}
            >
              <MaterialCommunityIcons
                name={"gift-outline"}
                color={Colors.primaryColor}
                size={24}
                style={{
                  top: 1,
                }}
              />
            </TouchableOpacity>
          </View>
        ) : (
          <></>
        )}

        {/* {selectedOutlet && selectedOutlet.subdomain ? (
          <View
            style={{
              position: "absolute",
              right: 20,
              top: 110, // 12
              padding: 8,
              backgroundColor: "#ffffff",
              borderRadius: 25,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              //backgroundColor: 'blue'
            }}
          >
            <TouchableOpacity
              style={{ alignSelf: "center" }}
              onPress={() => {
                ringOutlet();
              }}
            >
              <MaterialCommunityIcons
                name={"bell"}
                color={Colors.primaryColor}
                size={24}
                style={{
                  top: 1,
                }}
              />
            </TouchableOpacity>
          </View>
        ) : (
          <></>
        )} */}

        {/* {selectedOutlet && selectedOutlet.subdomain && !openSearchBar ? ( */}
        {!isMobile() ? (
          selectedOutlet && selectedOutlet.subdomain ? (
            <View
              style={{
                position: "absolute",
                right: 20,
                // top: (!isMobile() && (selectedOutlet.subdomain === 'hominsanttdi' || selectedOutlet.subdomain === 'hominsan-ss15' || selectedOutlet.subdomain === 'kenneth-cafe')) && (email || userNumber) ? 160 : 110, // 12
                top: (email || userNumber) ? 165 : 60, // 12
                // top: (!isMobile()) && (email || userNumber) ? 160 : 60, // 12
                padding: 8,
                backgroundColor: "#ffffff",
                borderRadius: 25,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                //backgroundColor: 'blue'
              }}
            >
              <TouchableOpacity
                testID="search"
                style={{ alignSelf: "center" }}
                onPress={() => {
                  if (openSearchBar) {
                    setOpenSearchBar(false)
                  }
                  else {
                    setOpenSearchBar(true);
                  }
                }}
              >
                <Feather
                  name={"search"}
                  color={Colors.primaryColor}
                  size={24}
                  style={{
                    top: 1,
                  }}
                />
              </TouchableOpacity>
            </View>
          ) :
            //   (
            //     <View
            //       style={{
            //         position: "absolute",
            //         right: 20,
            //         top: 60, // 12
            //         padding: 8,
            //         backgroundColor: "#ffffff",
            //         borderRadius: 25,
            //         display: "flex",
            //         alignItems: "center",
            //         justifyContent: "center",
            //         //backgroundColor: 'blue'
            //       }}
            //     >
            //       <TouchableOpacity
            //         style={{ alignSelf: "center" }}
            //         onPress={() => {
            //           if (openSearchBar) {
            //             setOpenSearchBar(false)
            //           }
            //           else {
            //             setOpenSearchBar(true);
            //           }
            //         }}
            //       >
            //         <Feather
            //           name={"search"}
            //           color={Colors.primaryColor}
            //           size={24}
            //           style={{
            //             top: 1,
            //           }}
            //         />
            //       </TouchableOpacity>
            //     </View>
            <></>
        ) : (
          selectedOutlet && selectedOutlet.subdomain ?
            <View
              style={{
                position: "absolute",
                right: 20,
                // top: (!isMobile() && (selectedOutlet.subdomain === 'hominsanttdi' || selectedOutlet.subdomain === 'hominsan-ss15' || selectedOutlet.subdomain === 'kenneth-cafe')) && (email || userNumber) ? 160 : 110, // 12
                top: (email || userNumber) ? 110 : 60, // 12
                // top: (!isMobile()) && (email || userNumber) ? 160 : 60, // 12
                padding: 8,
                backgroundColor: "#ffffff",
                borderRadius: 25,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                flexDirection: 'column'
                //backgroundColor: 'blue'
              }}
            >
              <TouchableOpacity
                testID="search"
                style={{ alignSelf: "center" }}
                onPress={() => {
                  if (openSearchBar) {
                    setOpenSearchBar(false)
                  }
                  else {
                    setOpenSearchBar(true);
                  }
                }}
              >
                <Feather
                  name={"search"}
                  color={Colors.primaryColor}
                  size={24}
                  style={{
                    top: 1,
                  }}
                />
              </TouchableOpacity>
            </View>

            :
            null
        )}


        {/* 7th element here */}

        <View>
          {openSearchBar ? (
            <>
              {/* <View style={{backgroundColor: 'blue'}}> */}
              <View>
                <View
                  style={
                    {
                      paddingHorizontal: 25,
                      justifyContent: "center",
                      alignItems: "center",
                      paddingVertical: 4.5,
                    }
                  }
                >
                  <View
                    style={{
                      padding: 8,
                      backgroundColor: "#ffffff",
                      borderRadius: 25,
                      alignItems: "center",
                      justifyContent: "center",
                      width: windowWidth * 0.95,
                      height: 40,
                      backgroundColor: 'white',
                      borderRadius: 5,
                      flexDirection: 'row',
                      alignContent: 'center',
                      shadowColor: '#000',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 4,
                      borderWidth: 1,
                      borderColor: '#E5E5E5',
                    }}>
                    <TouchableOpacity
                      style={{ alignSelf: "center" }}
                      onPress={() => {
                        if (openSearchBar) {
                          setOpenSearchBar(false)
                        }
                        else {
                          setOpenSearchBar(true);
                        }
                      }}
                    >
                      <Feather
                        name={"search"}
                        color={Colors.primaryColor}
                        size={24}
                        style={{
                          top: 1,
                        }}
                      />
                    </TouchableOpacity>
                    <TextInput
                      editable={!loading}
                      underlineColorAndroid={Colors.whiteColor}
                      style={{
                        width: windowWidth * 0.99,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Regular',
                        height: 40,
                        //marginLeft: 10,
                        paddingLeft: 10,
                      }}
                      clearButtonMode="while-editing"
                      placeholder="Search"
                      placeholderTextColor={'#a9a9a9'}
                      onChangeText={(text) => {
                        if (text) {
                          setSearch(text);
                          CommonStore.update((s) => {
                            s.isOnCategory = false;
                          });
                        } else {
                          CommonStore.update((s) => {
                            s.isOnCategory = true;
                          });
                          setSearch('');
                          setOpenSearchBar(false);
                        }
                        // setIsOnSearch(true)
                      }}
                      value={search}
                    />
                  </View>
                </View>
              </View>
              {/* </View> */}
            </>
          ) : (
            <View style={{ width: '100%' }}>
              <View
                style={{
                  position: "absolute",
                  alignItems: "center",
                  paddingVertical: 15,
                  paddingLeft: 6,
                  width: 20,
                  height: 47,
                  backgroundColor: Colors.highlightColor,
                  // opacity: 0.85,
                  opacity: 1,
                  // justifyContent: "center",
                  shadowColor: "#000",
                  shadowOffset: {
                    width: 1,
                    height: 0.5,
                  },
                  // marginLeft: 2,
                  shadowOpacity: 0.12,
                  shadowRadius: 3.22,
                }}

              >
                <TouchableOpacity
                  onPress={() => switchCategoryL()}
                >
                  {/* <MaterialIcons
                  name="arrow-back-ios"
                  size={20}
                  color={Colors.primaryColor}
                  style={{ height: '120%', zIndex: 3, elevation: 1 }}
                /> */}
                  <ArrowBack
                    name="arrow-back-ios"
                    size={40}
                    color={Colors.darkBgColor}
                    style={{ height: '120%', zIndex: 3, elevation: 1 }}
                  />
                </TouchableOpacity>
              </View>

              <View
                style={{
                  position: "absolute",
                  alignItems: "center",
                  alignSelf: "flex-end",
                  // marginRight: -60,
                  paddingVertical: 15,
                  paddingRight: 1,
                  width: 20,
                  height: 47,
                  backgroundColor: Colors.highlightColor,
                  // opacity: 0.85,
                  opacity: 1,
                  // justifyContent: "center",
                  shadowColor: "#000",
                  shadowOffset: {
                    width: 1,
                    height: 0.5,
                  },
                  shadowOpacity: 0.12,
                  shadowRadius: 3.22,
                }}
              >
                <TouchableOpacity
                  onPress={() => switchCategoryR()}
                >
                  {/* <MaterialIcons
                  name="arrow-forward-ios"
                  size={20}
                  color={Colors.primaryColor}
                  style={{ height: '120%', zIndex: 3, elevation: 1, }}
                /> */}
                  <ArrowForward
                    name="arrow-forward-ios"
                    size={40}
                    color={Colors.darkBgColor}
                    style={{ height: '120%', zIndex: 3, elevation: 1, }}
                  />
                </TouchableOpacity>
              </View>

              {/* category list */}
              <ScrollView
                ref={catScrollViewRef}
                // onTouchStart={() => {
                //   setIsScrollCat(true);
                // }}
                // onTouchEnd={() => {
                //   setUserSwiped(false);
                // }}
                testID="categoryList"
                showsHorizontalScrollIndicator={isMobile() ? false : true}
                alwaysBounceHorizontal={true}
                horizontal={true}
                contentContainerStyle={{
                  paddingLeft: 20,
                }}
                style={[
                  styles.infoTab,
                  {
                    // ...!isInfoTabHitTop && { position: 'absolute' },
                    // ...!isInfoTabHitTop && { top: 120 },
                    zIndex: -1,
                    // backgroundColor: 'red',
                    // width: windowWidth * 0.4,
                    ...(!isMobile() && {
                      width: windowWidth,
                    }),
                  },
                ]}
              // stickyHeaderIndices={[0]}
              >
                {/* {categoryOutlet.map((item, index) => {
              return (
                <TouchableOpacity
                  onPress={() => {
                    setState({
                      category: item.label,
                      categoryIndex: index,
                      // menu: choice[index].category,

                    })
                    refreshMenu();
                  }}>
                  <View
                    style={[
                      styles.category,
                      {
                        borderBottomColor:
                          category == item.label
                            ? Colors.primaryColor
                            : null,
                        borderBottomWidth:
                          category == item.label ? 3 : 0,
                      },
                    ]}>
                    <Text
                      style={{
                        textTransform: 'capitalize',
                        paddingVertical: 12,
                        fontFamily: category == item.label
                          ? "NunitoSans-Bold"
                          : "NunitoSans-Regular",
                        color: category == item.label
                          ? Colors.primaryColor
                          : Colors.blackColor,
                        fontSize: 15,
                      }}>
                      {item.label}
                    </Text>
                  </View>
                </TouchableOpacity>
              );
            })} */}

                {console.log(`filteredOutletCategories changed!`)}
                {/* {console.log(filteredOutletCategories.length > 0 ? filteredOutletCategories[0].name : '')} */}

                {!isSwitchingOutlets &&
                  filteredOutletCategories
                    // .filter(category => 
                    //   selectedOutletItems.some(item => item.categoryId === category.uniqueId) // hide no item category
                    // )
                    // .slice()
                    // .sort((a, b) => {
                    //   return (
                    //     (a.orderIndex
                    //       ? a.orderIndex
                    //       : filteredOutletCategories.length) -
                    //     (b.orderIndex
                    //       ? b.orderIndex
                    //       : filteredOutletCategories.length)
                    //   );
                    // })
                    .map((item, index) => {
                      return (
                        <TouchableOpacity
                          onPress={() => {
                            // setState({
                            //   category: item.label,
                            //   categoryIndex: index,
                            //   // menu: choice[index].category,

                            // })
                            // refreshMenu();

                            // global.selectedOutletItemCategory = item;

                            if (global.outletName) {
                              logEvent(global.analytics, ANALYTICS.WO_OUTLET_MENU_CATEGORY_CLICK, {
                                eventNameParsed: ANALYTICS_PARSED.WO_OUTLET_MENU_CATEGORY_CLICK,

                                outletName: global.outletName ? `${global.outletName} (Web)` : '',

                                webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
                              });
                            }

                            CommonStore.update((s) => {
                              s.selectedOutletItemCategory = item;
                            });

                            if (selectedOutlet && selectedOutlet.qrUIV2 === true) {
                              setIsProgrammaticScroll(true);
                              scrollToItem(item, index);
                            }
                          }}
                          onLayout={(event) => {
                            const { width, height, x, y, } = event.nativeEvent.layout;

                            if (item && item.uniqueId) {
                              global.categoryWidthByNameDict[item.uniqueId] = width;
                              global.categoryPosXByIdDict[item.uniqueId] = x;
                            }
                          }}
                        >
                          <View
                            style={[
                              styles.category,
                              {
                                // borderBottomColor:
                                //   selectedOutletItemCategory.name == item.name
                                //     ? Colors.primaryColor
                                //     : null,
                                // borderBottomWidth:
                                //   selectedOutletItemCategory.name == item.name ? 3 : 0,
                              },
                            ]}
                          >
                            <View
                              style={{
                                borderBottomColor:
                                  (selectedOutletItemCategory && selectedOutletItemCategory.name == item.name)
                                    ? Colors.primaryColor
                                    : null,
                                borderBottomWidth:
                                  (selectedOutletItemCategory && selectedOutletItemCategory.name == item.name)
                                    ? 3
                                    : 0,
                              }}
                            >
                              <Text
                                testID={`categoryName-${index}`}
                                style={{
                                  textTransform: "capitalize",
                                  paddingVertical: 12,
                                  fontFamily:
                                    (selectedOutletItemCategory && selectedOutletItemCategory.name == item.name)
                                      ? "NunitoSans-Bold"
                                      : "NunitoSans-Regular",
                                  color:
                                    (selectedOutletItemCategory && selectedOutletItemCategory.name == item.name)
                                      ? Colors.primaryColor
                                      : Colors.mainTxtColor,
                                  fontSize: 16,
                                }}
                              >
                                {item.name}
                              </Text>
                            </View>
                          </View>
                        </TouchableOpacity>
                      );
                    })}
              </ScrollView>

              {
                (selectedTaggableVoucher && selectedTaggableVoucher.uniqueId)
                  ?
                  <View style={{
                    // marginTop: 15,

                    backgroundColor: Colors.primaryColor,

                    alignItems: 'center',
                    justifyContent: 'center',

                    width: '100%',
                    height: 40,

                    paddingHorizontal: 15,
                  }}>
                    <Text style={{
                      color: Colors.whiteColor,
                      fontSize: 14,
                      fontFamily: "NunitoSans-SemiBold"
                    }}
                      numberOfLines={1}
                    >
                      {selectedTaggableVoucher.criteriaList && selectedTaggableVoucher.criteriaList[0].priceMin !== undefined ?
                        `Spend RM${selectedTaggableVoucher.criteriaList[0].priceMin.toFixed(2)} to get your rewards` :
                        `${selectedTaggableVoucher.campaignName ? selectedTaggableVoucher.campaignName : ' voucher applied'}`}
                    </Text>
                  </View>
                  :
                  <></>
              }
            </View>
            // </View>
          )}
        </View>

        {/* <View> */}

        <ScrollView
          stickyHeaderIndices={[6]}
          showsHorizontalScrollIndicator={false}
          // onScroll={onContainerScroll}
          ref={scrollViewRef}
          onTouchStart={() => {
            // setIsScrollCat(false);
            // global.canSwipeItemList = false;
          }}
          onTouchEnd={() => {
            if (global.canSwipeItemList) {
              if (userSwiped === 'left') {
                global.canSwipeItemList = false;

                animateItemListContent('left');
                switchCategoryR();
              }
              else if (userSwiped === 'right') {
                global.canSwipeItemList = false;

                animateItemListContent('right');
                switchCategoryL();
              }

              if (userSwiped === 'left' || userSwiped === 'right') {
                setTimeout(() => {
                  global.canSwipeItemList = true;
                }, 250);
              }
            }

            // setIsScrollCat(true);
          }}
          // onScroll={Animated.event(
          //   [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          //   { useNativeDriver: true }
          // )}
          // onScroll={({ nativeEvent }) => {
          //   if (isCloseToBottom(nativeEvent)) {
          //     Animated.timing(fadeAnim, {
          //       toValue: 1,
          //       duration: 700,
          //       // delay: 1000,
          //       useNativeDriver: true,
          //     }).start();
          //   }
          //   else {
          //     Animated.timing(fadeAnim, {
          //       toValue: 0,
          //       duration: 700,
          //       easing: Easing.bezier(0.550, 0.085, 0.680, 0.530),
          //       useNativeDriver: true,
          //     }).start();
          //   }
          // }}

          style={{
            position: "relative",
            //backgroundColor: 'blue'
          }}
        // stickyHeaderIndices={[1]}
        >
          {!isSwitchingOutlets ? !openSearchBar ? (
            selectedOutlet && selectedOutlet.qrUIV2 === true ?
              <FlatList
                data={selectedOutletItemsForNewUi}
                CellRendererComponent={renderAllMenuV2}
                // removeClippedSubviews={false}
                windowSize={2}
                initialNumToRender={8}
                maxToRenderPerBatch={8}
                // viewabilityConfig={viewabilityConfig}
                // onViewableItemsChanged={handleViewableItemsChanged}
                keyExtractor={(item, index) => index}
                contentContainerStyle={{
                  paddingBottom: 30,
                }}
                style={{
                  //paddingHorizontal: 5,
                  //backgroundColor:'red',
                  width: '100%',

                  paddingBottom: 50,
                }}
              />
              :
              <FlatList
                ref={flatListRef}
                // onViewableItemsChanged={onViewableItemsChanged.current}
                testID="itemList"
                data={
                  selectedOutletItems.filter(item => {
                    if (item.categoryId === selectedOutletItemCategory.uniqueId) {
                      return true;
                    }
                    else {
                      return false;
                    }
                  }).slice().sort((a, b) => {
                    return (a.orderIndex ? a.orderIndex : selectedOutletItems.length) -
                      (b.orderIndex ? b.orderIndex : selectedOutletItems.length)
                  })
                    .concat(
                      selectedOutletItems.filter(item => {
                        if (item.categoryId === selectedOutletItemCategory.uniqueId) {
                          return true;
                        }
                        else {
                          return false;
                        }
                      }).length <= 4
                        ?
                        Array(
                          4 - selectedOutletItems.filter(item => {
                            if (item.categoryId === selectedOutletItemCategory.uniqueId) {
                              return true;
                            }
                            else {
                              return false;
                            }
                          }).length
                        ).fill(
                          {
                            isFakeItem: true,

                            categoryId: selectedOutletItemCategory.uniqueId,
                            hideInOrderTypes: [],
                          }
                        )
                        // [
                        //   {
                        //     isFakeItem: true,

                        //     categoryId: selectedOutletItemCategory.uniqueId,
                        //     hideInOrderTypes: [],
                        //   },
                        //   {
                        //     isFakeItem: true,

                        //     categoryId: selectedOutletItemCategory.uniqueId,
                        //     hideInOrderTypes: [],
                        //   },
                        //   {
                        //     isFakeItem: true,

                        //     categoryId: selectedOutletItemCategory.uniqueId,
                        //     hideInOrderTypes: [],
                        //   },
                        //   {
                        //     isFakeItem: true,

                        //     categoryId: selectedOutletItemCategory.uniqueId,
                        //     hideInOrderTypes: [],
                        //   },
                        // ]
                        :
                        []
                      // 2024-07-02 - add additional dummy items to list that more than 7 items
                      // Array(
                      //   3
                      // ).fill(
                      //   {
                      //     isFakeItem: true,

                      //     categoryId: selectedOutletItemCategory.uniqueId,
                      //     hideInOrderTypes: [],
                      //   }
                      // )
                    )
                    .concat(
                      Array(
                        1
                      ).fill(
                        {
                          isFakeItem: true,

                          categoryId: selectedOutletItemCategory.uniqueId,
                          hideInOrderTypes: [],
                        }
                      )
                    )
                    .concat(
                      Array(
                        1
                      ).fill(
                        {
                          isFakeItem: true,
                          isLoadingIcon: true,

                          categoryId: selectedOutletItemCategory.uniqueId,
                          hideInOrderTypes: [],
                        }
                      )
                    )
                  // .concat(
                  //   Array(
                  //     1
                  //   ).fill(
                  //     {
                  //       isFakeItem: true,

                  //       categoryId: selectedOutletItemCategory.uniqueId,
                  //       hideInOrderTypes: [],
                  //     }
                  //   )
                  // )
                }
                // data={selectedOutletItems.filter((item) => {
                //   if (search !== '') {
                //     const searchLowerCase = search.toString().toLowerCase();
                //     if (item.name.toLowerCase().includes(searchLowerCase) || (item.barcode && item.barcode.serialNumber.includes(searchLowerCase))) {
                //       return true;
                //     } else {
                //       return false;
                //     }
                //   } else {
                //     return true;
                //   }
                // })}
                // data={selectedOutletItems.filter(
                //   (item) =>
                //     item.priceType === undefined ||
                //     item.priceType === PRODUCT_PRICE_TYPE.FIXED ||
                //     item.priceType === PRODUCT_PRICE_TYPE.UNIT
                // )}
                // extraData={selectedOutletItems}
                renderItem={renderMenu}
                keyExtractor={(item, index) => index}
                contentContainerStyle={{
                  paddingLeft: 10,
                  paddingRight: 10,
                  paddingTop: 20,

                  paddingBottom: 50,

                  opacity: opacityItemListStyle,
                  transition: opacityTransitionItemListStyle,
                }}
              // onEndReached={handleEndReached}
              />
          ) : (
            //By Search
            <>
              <FlatList
                data={selectedOutletItems.filter(item => {
                  if (item.hideInOrderTypes === orderType) {
                    return false;
                  }
                  else {
                    return true;
                  }
                }).slice().sort((a, b) => {
                  return (a.orderIndex ? a.orderIndex : selectedOutletItems.length) -
                    (b.orderIndex ? b.orderIndex : selectedOutletItems.length)
                })}
                // data={selectedOutletItemsRaw2.filter((item) => {
                //   if (search !== '') {
                //     const searchLowerCase = search.toString().toLowerCase();
                //     if (item.name.toLowerCase().includes(searchLowerCase) || (item.barcode && item.barcode.serialNumber.includes(searchLowerCase))) {
                //       return true;
                //     } else {
                //       return false;
                //     }
                //   } else {
                //     return true;
                //   }
                // })}
                renderItem={renderAllMenu}
                keyExtractor={(item, index) => index}
                contentContainerStyle={{
                  paddingLeft: 10,
                  paddingRight: 10,
                  paddingTop: 20,

                  paddingBottom: 50,
                }}
              />

            </>
          ) : (
            <View
              style={{
                // width: "100%",
                // height: windowHeight * 0.5,
                // justifyContent: "center",
                // alignItems: "center",
              }}
            >
              {/* <ActivityIndicator color={Colors.primaryColor} size={"large"} /> */}
            </View>
          )}
        </ScrollView>
        {/* </View> */}

        <View style={{ minHeight: 100 }} />
        {/* <Animated.View style={{ transform: [{ translateY: translateYAnim }], height: 150, marginTop: -20 }}> */}
        {/* <Animated.View
          style={{
            transform: [{ translateY: translateYAnim }],
            height: 150, 
            marginTop: -20
          }}>
          <Text style={{ fontSize: 20, color: Colors.primaryColor, fontFamily: "NunitoSans-Bold", textAlign: 'center' }}>
            {'Swipe left/right \nto switch categories'}
          </Text>
        </Animated.View> */}
        {/* {
          (selectedOutlet && selectedOutlet.subdomain &&
            (
              selectedOutlet.subdomain === 'kenneth-cafe' ||
              selectedOutlet.subdomain === 'cafe-ryan-klcc' ||
              selectedOutlet.subdomain === 'hominsanttdi' ||
              selectedOutlet.subdomain === 'hominsan-ss15'
            ))
            ?
            <RecommendedItems navigation={props.navigation} style={{ width: windowWidth, height: windowHeight * 0.2 }} />
            :
            <></>
        } */}
      </ScrollView>

      {/* {cartIcon ?
        <Draggable
          shouldReverse={reverse}
          renderSize={100}
          renderColor={Colors.secondaryColor}
          isCircle
          x={270}
          y={470}
          // onShortPressRelease={() => { goToCart(), cartCount() }}
          onShortPressRelease={onCartClicked}
        >
          <View style={{ width: 60, height: 60, justifyContent: "center" }}>
            <View style={{ alignSelf: "center" }}>
              <Ionicons name="cart-outline" size={42} color={Colors.mainTxtColor} />
            </View>
            <View style={styles.cartCount}>              
              <Text style={{
                color: Colors.whiteColor,
                fontSize: 14,
                fontFamily: "NunitoSans-Bold"
              }}>{cartItems.length}</Text>
            </View>
          </View>
        </Draggable>
        : null} */}
    </View>
  );
};

const styles = StyleSheet.create({
  confirmBox: {
    width: 350,
    // height: windowHeight * 0.35,
    height: 320,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
  },
  container: {
    // flex: 1,
    width: Dimensions.get("window").width,
    height: Dimensions.get("window").height,
    backgroundColor: "#ffffff",
    position: "relative",
  },
  outletCover: {
    // width: isMobile() ? '100%' : windowWidth,
    // alignSelf: 'center',
    // height: isMobile() ? undefined : windowHeight * 0.5,
    // aspectRatio: isMobile() ? 2 : 2,
    // resizeMode: isMobile() ? 'stretch' : 'stretch',
    width: isMobile() ? "100%" : "auto",
    alignSelf: "center",
    height: isMobile() ? undefined : Dimensions.get("window").height * 0.5,
    aspectRatio: isMobile() ? 2 : 2,
    resizeMode: isMobile() ? "stretch" : "stretch",
    ...(!isMobile() && {}),
  },
  infoTab: {
    backgroundColor: Colors.fieldtBgColor,
  },
  workingHourTab: {
    padding: 16,
    flexDirection: "row",
  },
  outletAddress: {
    textAlign: "center",
    color: Colors.mainTxtColor,
  },
  outletName: {
    fontWeight: "bold",
    fontSize: 20,
    marginBottom: 10,
  },
  logo: {
    width: 100,
    height: 100,
  },
  actionTab: {
    flexDirection: "row",
    marginTop: 20,
  },
  actionView: {
    width: Dimensions.get("window").width / 4,
    height: Dimensions.get("window").height / 4,
    justifyContent: "center",
    alignItems: "center",
    alignContent: "center",
  },
  actionBtn: {
    borderRadius: 50,
    width: 70,
    height: 70,
    borderColor: Colors.secondaryColor,
    borderWidth: StyleSheet.hairlineWidth,
    justifyContent: "center",
    alignItems: "center",
  },
  actionText: {
    fontSize: 12,
    marginTop: 10,
  },
  category: {
    // width: 150,
    paddingHorizontal: 25,
    justifyContent: "center",
    alignItems: "center",
  },
  floatCartBtn: {
    zIndex: 2,
    position: "absolute",
    bottom: 30,
    right: 30,
    width: 60,
    height: 60,
    borderRadius: 60 / 2,
    backgroundColor: Colors.secondaryColor,
    justifyContent: "center",
    alignContent: "center",
    alignItems: "center",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  cartCount: {
    position: "absolute",
    top: -8,
    right: 5,
    backgroundColor: Colors.primaryColor,
    width: 20,
    height: 20,
    borderRadius: 20 / 2,
    alignContent: "center",
    alignItems: "center",
    justifyContent: "center",
  },
  pic: {
    backgroundColor: Colors.secondaryColor,
    width: 80,
    height: 80,
    borderRadius: 10,
    alignSelf: 'center',
  },
  card: {
    flex: 1,
    width: isMobile() ? 300 : Dimensions.get('window').width * 0.2,
    // minWidth: (Styles.width / 2) - 64,
    height: Dimensions.get('window').height * 0.05,
    // backgroundColor: Colors.primaryColor,
    backgroundColor: '#416D5C',
    flexDirection: 'row',
    marginTop: 10,
    alignContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.29,
    shadowRadius: 4.65,

    elevation: 7,
  },
  card1: {
    flex: 1,
    width: isMobile() ? 300 : Dimensions.get('window').width * 0.2,
    height: Dimensions.get('window').height * 0.05,
    backgroundColor: Colors.fieldtBgColor,
    flexDirection: 'row',
    marginBottom: 20,
    alignContent: 'center',
    //alignItems: 'center',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    borderTopWidth: 1,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.29,
    shadowRadius: 4.65,

    elevation: 7,
  },
  modalTitle: {
    alignItems: "center",
  },
  modalBody: {
    flex: 0.8,
    alignItems: "center",
    justifyContent: "center",
  },
  modalTitleText: {
    fontFamily: "NunitoSans-Bold",
    // marginBottom: 10,
    fontSize: 20,
  },
  modalDescText: {
    fontFamily: "NunitoSans-SemiBold",
    fontSize: 16,
    color: Colors.fieldtTxtColor,
  },
  modalBodyText: {
    // flex: 1,
    fontFamily: "NunitoSans-SemiBold",
    fontSize: 16,
    width: "20%",
  },
  modalSaveButton: {
    width: isMobile()
      ? Dimensions.get("window").width * 0.3
      : Dimensions.get("window").width * 0.1,
    backgroundColor: Colors.fieldtBgColor,
    height: 45,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 8,

    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: "center",
    justifyContent: "center",
  },
  closeButton: {
    position: "absolute",
    right: isMobile()
      ? Dimensions.get("window").width * 0.04
      : Dimensions.get("window").width * 0.01,
    top: isMobile()
      ? Dimensions.get("window").width * 0.04
      : Dimensions.get("window").width * 0.01,
  },
  popup: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingVertical: 10,
    alignItems: 'center',
  },
  popupText: {
    color: '#fff',
    fontSize: 18,
  },
});
export default OutletMenuScreen;

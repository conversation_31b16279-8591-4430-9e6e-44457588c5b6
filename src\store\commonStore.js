import { Store } from 'pullstate';

export const CommonStore = new Store({
    debugText: 'Debug Text',

    isAuthenticating: true,
    isLoading: false,
    isPlacingOrder: false,

    isOrdering: false,

    alertObj: null,

    // lat: 3.1042,
    // lng: 101.6128,
    lat: 3.1320349, // number
    lng: 101.6136999, // number

    nearbyOutlets: [], // [ outlet, ... ]
    nearbyPromotions: [],
    nearbyLoyaltyStamps: [],

    outletsOpeningDict: {}, // { outlet_id -> outlet_opening, ... }

    searchOutlets: [], // [ outlet, ... ]

    merchantsDict: {}, // { merchant_id -> merchant, ... }

    outletsMinMaxDict: {}, // { outlet_id -> { min: outlet_item, max: outlet_item }, ... }

    tagOutlets: [],

    outletsTaxDict: {},

    outletsDict: {},

    /////////////////////////////////////////////////
    // User selected outlet

    selectedOutletWaiterName: '',
    selectedOutletWaiterId: '',

    selectedOutletSectionId: '',
    selectedOutletTableId: '',
    selectedOutletWaiterId: '',
    selectedOutletTablePax: 0,
    selectedOutletTableCode: '',
    selectedOutletTableQRUrl: '',
    userCart: {},

    // selectedOutletId: null,  // outlet
    selectedOutlet: null,  // outlet

    selectedMerchant: null,

    selectedOutletItems: [], // [ outlet_item, ... ]
    selectedOutletItemCategories: [], // [ outlet_item_category, ... ]
    selectedOutletPromotions: [],
    selectedOutletPromotionsDict: {},
    selectedOutletPreorderPackages: [],
    selectedOutletPointsRedeemPackages: [],

    selectedOutletUpsellingCampaigns: [],

    selectedOutletLoyaltyStamps: [],
    selectedOutletLoyaltyStampsDict: {},

    // stamps changes
    redeemableStackedLoyaltyStamps: [],
    toRedeemStackedLoyaltyStamp: {},

    pendingRedeemStackedLoyaltyStamp: {},

    selectedOutletLoyaltyCampaigns: [],
    selectedOutletUserLoyaltyCampaigns: [],

    selectedOutletCRMTagsDict: {},
    selectedOutletCRMSegmentsDict: {},
    selectedOutletCRMUser: {},

    selectedOutletReviews: [],
    selectedOutletReview: {},
    selectedOutletReviewSummary: {
        ratingsList: [],
        ratingsCount: 0,
        ratingsAverage: 0,
        commentsCount: 0,
    },

    selectedOutletItemsDict: {},
    selectedOutletItemsSkuDict: {},

    selectedOutletItemCategory: {}, // outlet_item_category
    selectedOutletItem: {}, // outlet_item

    selectedOutletItemCategoriesDict: {},

    outletsItemAddOnDict: {}, // { outlet_item_id -> [ outlet_item_addon, ... ], ... }
    outletsItemAddOnIdDict: {},
    outletsItemAddOnChoiceDict: {}, // { outlet_item_addon_id -> [ outlet_item_addon_choice, ... ], ... }
    outletsItemAddOnChoiceIdDict: {},

    selectedOutletItemAddOn: {}, // { outlet_item_addon_id -> { outlet_item_addon_choice_id = true/false, ... }, ... }
    selectedOutletItemAddOnChoice: {}, // { outlet_item_addon_choice_id -> true/false, ... }

    selectedOutletItemAddOnOi: {},

    /////////////////////////////////////////////////
    // Cart    

    cartOutletId: null,

    onUpdatingCartItem: null,

    cartItems: [], // [ { itemId: outlet_item_id, choices: { outlet_item_addon_choice_id: true/false, ... } }, ... ]
    cartItemChoices: {}, // { outlet_item_id -> { outlet_item_addon_choice_id = true, ... }, ... }

    cartItemsReservation: [],
    cartItemsProcessedReservation: [],

    orderType: 'INVALID',

    cartOutletItemsDict: {},
    cartOutletItemAddOnDict: {},
    cartOutletItemAddOnChoiceDict: {},

    cartItemsProcessed: [],

    selectedAddressType: 'OTHER',

    /////////////////////////////////////////////////

    tags: [],
    selectedOutletTag: {},

    tagToOutletTagDict: {}, // { tag_id -> [ outlet_tag, ... ], ... }

    searchOutletText: '',
    isLoadingSearchOutlet: false,

    searchOutletMerchantId: '',

    /////////////////////////////////////////////////

    userReservations: [],
    userQueues: [],
    userRings: [],
    userOrders: [],

    userOrdersQueue: [],
    userOrdersPreparing: [],

    selectedUserReservation: {},
    selectedUserQueue: {},
    selectedUserRing: {},
    selectedUserOrder: {},

    selectedPreorderPackage: {},
    selectedPreorderPackageCartItems: [],
    selectedPreorderPackageCartItemsProcessed: [],

    /////////////////////////////////////////////////

    merchantVouchersDict: {},
    selectedVoucher: {},

    // record scanned qr here
    scannedQrData: null,

    isSwitchingOutlets: true,

    selectedQROutletId: '',

    currQRScanningType: '',

    timestamp: 0,

    userFavoriteOutletDict: {},

    userLoyalty: {},

    beerDockets: [],
    beerDocketsRedemptions: [],
    beerDocketsRedemptionsBDDict: {},
    userBeerDockets: [],
    selectedUserBeerDocket: {},

    scannedQRBeerDocketResult: null,
    bdRedeemQuantity: 0,

    bdCartItems: [],

    currPage: '',

    availableUpsellingCampaigns: [],

    availablePromotions: [],
    selectedOutletPromotion: {},

    availableLoyaltyCampaigns: [],
    selectedLoyaltyCampaign: {},

    overrideItemPriceSkuDict: {},
    amountOffItemSkuDict: {},
    percentageOffItemSkuDict: {},
    buy1Free1ItemSkuDict: {},
    deliveryItemSkuDict: {},
    takeawayItemSkuDict: {},

    overrideCategoryPriceNameDict: {},
    amountOffCategoryNameDict: {},
    percentageOffCategoryNameDict: {},
    buy1Free1CategoryNameDict: {},
    deliveryCategoryNameDict: {},
    takeawayCategoryNameDict: {},

    overrideItemPriceSkuDictLC: {},
    fiItemSkuDictLC: {},
    amountOffItemSkuDictLC: {},
    percentageOffItemSkuDictLC: {},
    buy1Free1ItemSkuDictLC: {},
    deliveryItemSkuDictLC: {},
    takeawayItemSkuDictLC: {},

    overrideCategoryPriceNameDictLC: {},
    fiCategoryNameDictLC: {},
    amountOffCategoryNameDictLC: {},
    percentageOffCategoryNameDictLC: {},
    buy1Free1CategoryNameDictLC: {},
    deliveryCategoryNameDictLC: {},
    takeawayCategoryNameDictLC: {},

    allMenuVouchers: [],

    availablePointsRedeemPackages: [],
    pointsRedeemItemSkuDict: {},
    pointsRedeemCategoryNameDict: {},

    userLoyaltyStamps: [],
    userLoyaltyStampsDict: {},

    loyaltyStampGetItemSkuDict: {},
    loyaltyStampGetCategoryNameDict: {},
    loyaltyStampBuyItemSkuDict: {},
    loyaltyStampBuyCategoryNameDict: {},

    userLoyaltyStampGetLsItemDict: {},

    allOutletsItemSkuDict: {},

    paymentDetails: null,

    registerUserOrder: {},

    claimTaggableVoucher: {},

    selectedAddress: '',
    selectedAddressNote: '',
    selectedAddressDetails: '',
    selectedAddressLocation: {
        lat: 0,
        lng: 0,
    },
    selectedAddressUserName: '',
    selectedAddressUserPhone: '', // 60125810210
    selectedUserEmail: '', // <EMAIL>

    selectedUserFirstName: '', // Herks
    selectedUserLastName: '', // Aw

    selectedUserRemarks: '',
    selectedUserDiet: '',
    selectedUserOccasion: '',

    selectedReservationId: '',
    selectedReservationStartTime: null, // null
    selectedReservationPax: '1', // 0 (should be '1')
    selectedReservationPaxPrev: '1',
    selectedReservationStartTimePrev: null,

    selectedReservationOutletSectionId: '',
    selectedReservationOutletSectionName: '',

    editUserReservation: {},

    // reservationIdTemp: {},

    ////////////////////////////////////////

    loyaltyHistoryCrmUser: {},
    loyaltyHistoryLCCTransactions: [],
    loyaltyHistoryLCCBalance: 0,

    ////////////////////////////////////////

    selectedTableOrders: [],

    selectedUserOrderOthers: [],

    selectedUserOrderAnonymous: [],
    selectedUserOrderAnonymousTakeaway: [],

    ////////////////////////////////////////

    selectedOutletTaggableVouchers: [], // only buyable vouchers

    availableTaggableVouchers: [],

    selectedOutletTaggableVouchersAll: [],

    ////////////////////////////////////////

    selectedPromoCodePromotion: {},

    selectedTaggableVoucher: null,

    selectedTaggableVoucherPurchase: null,

    selectedTaggableVoucherView: null,

    tvCartItems: [],
    tvCartItemsProcessed: [],

    userTaggableVouchers: [],

    availableUserTaggableVouchers: [],

    timestampPromotion: Date.now(),

    selectedUserOrderTakeaway: {},
    selectedUserOrderOthersTakeaway: [],

    /////////////////////////////////

    timestampOutletCategory: Date.now(),

    // 11/10/2022 For Available On function (Time) - Greg

    timeCheckItem: Date.now(),

    molpayResult: null,

    selectedOutletSections: [],

    selectedOutletTable: {},
    selectedOutletTables: [],

    selectedOutletSection: {},
    selectedOutletSections: [],

    selectedOrderToPayUserId: '',

    //////////////////////////////////////////

    // pay hybrid

    currCrmUser: null,

    payHybridBody: null,

    payTopupCreditBody: null,

    orderIdCreated: '',

    //////////////////////////////////////////

    menuItemDetailModal: false,

    orderPaymentStatus: null,

    currOutletShift: {},
    currOutletShiftStatus: 'CLOSED',

    //////////////////////////////////////////  

    // supplementModal: false,

    resFirstName: '',
    resLastName: '',
    resPhoneNum: '',
    resEmail: '',
    resRemarks: '',
    resDietaryRestrictions: '',
    resSpecialOccasions: '',

    isPlacingReservation: false,
    isPlacingReservationUpselling: false,

    isDepositOnly: false,

    resShowConfirmationPage: false,

    //////////////////////////////////////////

    selectedMerchant: {},

    reservationUpsellingItems: [],

    upsellingCampaignsReservation: [],

    //////////////////////////////////////////

    cachedAddOnChoiceIdDict: {},

    //////////////////////////////////////////

    userAvailLoyaltyStamps: [],
    availableUserLoyaltyStamps: [],

    availUserLoyaltyStamps: [],

    selectedOutletLoyaltyStamps: [],
    selectedOutletLoyaltyStampsAll: [],
    selectedStamp: null,
    userStampAmountSelected: null,

    //////////////////////////////////////////

    reservationConfig: null,

    currPageIframe: '',

    //////////////////////////////////////////

    emailEInvoice: true,
    alertObjeI: null,

    forceSignUp: false,

    //////////////////////////////////////////

    selectedBundleTaggableVoucher: null,

    orderToShow: {},

    //////////////////////////////////////////

    // topup credit

    selectedTopupCreditPurchase: null,
    selectedTopupCreditView: null,

    tcCartItems: [],
    tcCartItemsProcessed: [],

    topupCreditTypes: [],

    //////////////////////////////////////////

    selectedOutletLCCTransactions: [],
    selectedOutletLCCBalance: 0,
    // visibleCategories: [],

    //////////////////////////////////////////

    outletQueueNumberDict: {},

    //////////////////////////////////////////

    queueConfig: null,

    selectedAddOnIdForChoiceQtyDict: {}, // { OutletItemAddOn.uniqueId: 0 }

    isDepo: true,

    /////////////////////////////////
});

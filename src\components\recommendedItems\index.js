import React, { useState, useEffect } from 'react';
import {
    View,
    Image,
    ActivityIndicator,
    Text,
    Modal,
    useWindowDimensions,
    TouchableOpacity,
    TextInput,
    Dimensions,
    StyleSheet,
    FlatList,
    ScrollView
} from 'react-native';
import Colors from '../../constant/Colors';
import {
    APP_TYPE,
    CHARGES_TYPE,
    ORDER_TYPE,
    PRODUCT_PRICE_TYPE,
    UNIT_TYPE_SHORT,
    UPSELLING_SECTION,
    UPSELLING_SECTION_CODE,
    UPSELL_BY_TYPE,
    USER_ORDER_STATUS,
    WEB_ORDER_UPSELLING_LAYOUT,
} from "../../constant/common";
import moment from "moment";
import { checkIfVisibleItem, isMobile } from "../../util/commonFuncs";
import { CommonStore } from "../../store/commonStore";
import AsyncImage from "../asyncImage";
import Ionicons from "react-native-vector-icons/Ionicons";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { prefix } from '../../constant/env';
import { DataStore } from '../../store/dataStore';

import Entypo from "react-native-vector-icons/Entypo";
import { logEvent } from 'firebase/analytics';
import { ANALYTICS, ANALYTICS_PARSED } from '../../constant/analytics';
import { TempStore } from '../../store/tempStore';

const RecommendedItems = props => {
    const upsellingSection = props.upsellingSection;

    const { width: windowWidth, height: windowHeight } = useWindowDimensions();
    const cartItems = CommonStore.useState((s) => s.cartItems);
    const selectedOutletItemCategoriesDict = CommonStore.useState((s) => s.selectedOutletItemCategoriesDict);
    const overrideCategoryPriceNameDict = CommonStore.useState((s) => s.overrideCategoryPriceNameDict);
    const selectedOutletItemCategory = CommonStore.useState((s) => s.selectedOutletItemCategory);
    const orderType = CommonStore.useState((s) => s.orderType);
    const amountOffCategoryNameDict = CommonStore.useState((s) => s.amountOffCategoryNameDict);
    const percentageOffCategoryNameDict = CommonStore.useState((s) => s.percentageOffCategoryNameDict);
    const pointsRedeemCategoryNameDict = CommonStore.useState((s) => s.pointsRedeemCategoryNameDict);
    const buy1Free1CategoryNameDict = CommonStore.useState((s) => s.buy1Free1CategoryNameDict);
    const selectedOutlet = CommonStore.useState((s) => s.selectedOutlet);

    const checkCartOutlet = () => {
        if (
            selectedOutlet &&
            cartOutletId &&
            selectedOutlet.uniqueId !== cartOutletId
        ) {
            return true;
        }
        return false;
    };

    const linkToFunc = DataStore.useState(s => s.linkToFunc);

    const [tempItem, setTempItem] = useState({});
    const refresh = () => { };
    const pointsRedeemItemSkuDict = CommonStore.useState((s) => s.pointsRedeemItemSkuDict);
    const amountOffItemSkuDict = CommonStore.useState((s) => s.amountOffItemSkuDict);

    const [selectedOutletItems, setSelectedOutletItems] = useState([]);

    const selectedOutletItemsRaw = CommonStore.useState(
        (s) => s.selectedOutletItems.filter(
            (item) =>
                item.priceType === undefined ||
                item.priceType === PRODUCT_PRICE_TYPE.FIXED ||
                item.priceType === PRODUCT_PRICE_TYPE.UNIT
        )
    );

    const [effectiveDays, setEffectiveDays] = useState(moment().day());
    const [cartWarning, setCartWarning] = useState(false);
    const percentageOffItemSkuDict = CommonStore.useState((s) => s.percentageOffItemSkuDict);
    const cartOutletId = CommonStore.useState((s) => s.cartOutletId);
    const buy1Free1ItemSkuDict = CommonStore.useState((s) => s.buy1Free1ItemSkuDict);
    const overrideItemPriceSkuDict = CommonStore.useState((s) => s.overrideItemPriceSkuDict);

    const cartItemsProcessed = CommonStore.useState(s => s.cartItemsProcessed);
    const currCrmUser = CommonStore.useState(s => s.currCrmUser);
    const selectedOutletCRMTagsDict = CommonStore.useState(s => s.selectedOutletCRMTagsDict);
    const selectedOutletItem = CommonStore.useState(s => s.selectedOutletItem);

    const isPlacingReservationUpselling = CommonStore.useState(s => s.isPlacingReservationUpselling);
    const cartItemsReservation = CommonStore.useState(s => s.cartItemsReservation);
    const cartItemsProcessedReservation = CommonStore.useState(s => s.cartItemsProcessedReservation);

    const [toRecommendedItems, setToRecommendedItems] = useState([]);

    const [currUpsellingCampaign, setCurrUpsellingCampaign] = useState(null);

    const availableUpsellingCampaigns = CommonStore.useState(s => s.availableUpsellingCampaigns);

    useEffect(() => {
        var toRecommendedItemsTemp = [];

        if (availableUpsellingCampaigns.length > 0) {
            /////////////////////////////////////////////////////////////

            let availableUpsellingCampaignsFiltered = [];
            if (props.campaignData && props.campaignData.uniqueId) {
                availableUpsellingCampaignsFiltered = [props.campaignData];
            }
            else {
                availableUpsellingCampaignsFiltered = availableUpsellingCampaigns;
            }

            let toCompareItems = [];
            if (props.campaignData && props.campaignData.uniqueId) {
                if (props.campaignData.upsellingSection === UPSELLING_SECTION.AFTER_CART) {
                    toCompareItems = [selectedOutletItem];
                }
                else if (props.campaignData.upsellingSection === UPSELLING_SECTION.AFTER_CHECKOUT ||
                    props.campaignData.upsellingSection === UPSELLING_SECTION.AFTER_CHECKOUT_RECOMMENDATION) {
                    toCompareItems = cartItems;

                    if (isPlacingReservationUpselling && cartItemsReservation &&
                        cartItemsReservation.length > 0) {
                        toCompareItems = cartItemsReservation;
                    }
                }
                else {
                    toCompareItems = cartItemsProcessed;

                    // if (isPlacingReservationUpselling && global.cartItemsProcessedReservation &&
                    //     global.cartItemsProcessedReservation.length > 0) {
                    //     toCompareItems = global.cartItemsProcessedReservation;
                    // }
                }
            }
            else {
                toCompareItems = cartItemsProcessed;

                // if (isPlacingReservationUpselling && global.cartItemsProcessedReservation &&
                //     global.cartItemsProcessedReservation.length > 0) {
                //     toCompareItems = global.cartItemsProcessedReservation;
                // }
            }

            /////////////////////////////////////////////////////////////

            for (var campaignIndex = 0; campaignIndex < availableUpsellingCampaignsFiltered.length; campaignIndex++) {
                const upsellingCampaign = availableUpsellingCampaignsFiltered[campaignIndex];

                setCurrUpsellingCampaign(currUpsellingCampaign);

                const upsellingProductList = upsellingCampaign.productList;
                const upsellingProductIdList = upsellingCampaign.productList.map(product => product.productId);

                for (var upsellingIndex = 0; upsellingIndex < upsellingProductIdList.length; upsellingIndex++) {
                    for (var i = 0; i < selectedOutletItemsRaw.length; i++) {
                        if (upsellingProductIdList[upsellingIndex] === selectedOutletItemsRaw[i].uniqueId) {
                            if (checkIfVisibleItem(selectedOutletItemsRaw[i])) {
                                const upsellingItem = upsellingProductList.find(product => product.productId === selectedOutletItemsRaw[i].uniqueId);
                                const priceUpselling = upsellingItem.upsellPrice;

                                var isValidOrderType = selectedOutletItemsRaw[i].hideInOrderTypes && selectedOutletItemsRaw[i].hideInOrderTypes.length > 0
                                    ? (selectedOutletItemsRaw[i].hideInOrderTypes.includes(orderType)
                                        ? false
                                        : true)
                                    : true;

                                var isValidActive = (
                                    selectedOutletItemsRaw[i].isActive &&
                                    (selectedOutletItemsRaw[i].isAvailableDayActive
                                        ? (selectedOutletItemsRaw[i].effectiveTypeOptions.includes(effectiveDays) &&
                                            selectedOutletItemsRaw[i].effectiveStartTime && selectedOutletItemsRaw[i].effectiveEndTime &&
                                            moment().isSameOrAfter(
                                                moment(selectedOutletItemsRaw[i].effectiveStartTime)
                                                    .year(moment().year())
                                                    .month(moment().month())
                                                    .date(moment().date())
                                            )
                                            &&
                                            moment().isBefore
                                                (moment(selectedOutletItemsRaw[i].effectiveEndTime)
                                                    .year(moment().year())
                                                    .month(moment().month())
                                                    .date(moment().date())
                                                ))
                                        : true) &&
                                    (selectedOutletItemsRaw[i].isOnlineMenu !== undefined ? selectedOutletItemsRaw[i].isOnlineMenu : true) &&
                                    (selectedOutletItemsRaw[i].isStockCountActive !== undefined &&
                                        selectedOutletItemsRaw[i].isStockCountActive !== false &&
                                        selectedOutletItemsRaw[i].stockCount !== undefined &&
                                        selectedOutletItemsRaw[i].toSellIgnoreStock !== undefined
                                        ? selectedOutletItemsRaw[i].isStockCountActive &&
                                        selectedOutletItemsRaw[i].stockCount > 0 &&
                                        (selectedOutletItemsRaw[i].toSellIgnoreStock !== undefined
                                            ? selectedOutletItemsRaw[i].toSellIgnoreStock
                                            : true)
                                        : true)
                                );

                                if (isValidOrderType && isValidActive) {
                                    // var existingItem = cartItemsProcessed.find(item => item.itemId === selectedOutletItemsRaw[i].uniqueId);
                                    var existingItem = null;

                                    ////////////////////////////////////////////

                                    var cartOutletItemList = [];

                                    if (props.campaignData && props.campaignData.uniqueId) {
                                        if (props.campaignData.upsellingSection === UPSELLING_SECTION.AFTER_CART) {
                                            existingItem = toCompareItems.find(item => item.uniqueId === selectedOutletItemsRaw[i].uniqueId);
                                        }
                                        else if (props.campaignData.upsellingSection === UPSELLING_SECTION.AFTER_CHECKOUT ||
                                            props.campaignData.upsellingSection === UPSELLING_SECTION.AFTER_CHECKOUT_RECOMMENDATION) {

                                        }
                                        else {
                                            existingItem = toCompareItems.find(item => item.itemId === selectedOutletItemsRaw[i].uniqueId);
                                        }
                                    }
                                    else {
                                        existingItem = toCompareItems.find(item => item.itemId === selectedOutletItemsRaw[i].uniqueId);
                                    }

                                    ////////////////////////////////////////////

                                    if (!existingItem) {
                                        // didn't existed in cart, continue

                                        ////////////////////////////////////////////////////////////////

                                        // by customer tags

                                        if (upsellingItem.upsellByType === UPSELL_BY_TYPE.CUSTOMER) {
                                            if (upsellingItem.tagIdList && upsellingItem.tagIdList.length > 0) {
                                                // try compare with tags

                                                if (currCrmUser && currCrmUser.uniqueId) {
                                                    if (upsellingItem.tagIdList && upsellingItem.tagIdList.length > 0) {
                                                        for (var j = 0; j < upsellingItem.tagIdList.length; j++) {
                                                            var userTagId = upsellingItem.tagIdList[j];

                                                            if (selectedOutletCRMTagsDict[userTagId] && selectedOutletCRMTagsDict[userTagId].uniqueId) {
                                                                var userTag = selectedOutletCRMTagsDict[userTagId];

                                                                if (
                                                                    (userTag.emailList && userTag.emailList.includes(currCrmUser.userEmail))
                                                                    ||
                                                                    (userTag.phoneList && userTag.phoneList.includes(currCrmUser.userNumber))
                                                                ) {
                                                                    // toRecommendedItemsTemp.push(selectedOutletItemsRaw[i]);

                                                                    toRecommendedItemsTemp.push({
                                                                        ...selectedOutletItemsRaw[i],
                                                                        price: priceUpselling,

                                                                        priceUpselling: priceUpselling,
                                                                        upsellingCampaignId: upsellingCampaign.uniqueId,

                                                                        priceProduct: selectedOutletItemsRaw[i].price,

                                                                        upc: UPSELLING_SECTION_CODE[props.upsellingSection ? props.upsellingSection : UPSELLING_SECTION.IN_CART],
                                                                    });

                                                                    break;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                            else {
                                                if (toRecommendedItemsTemp.find(findItem => findItem.uniqueId === selectedOutletItemsRaw[i].uniqueId)) {
                                                    // means in the recommended list already, no need add
                                                }
                                                else {
                                                    toRecommendedItemsTemp.push({
                                                        ...selectedOutletItemsRaw[i],
                                                        price: priceUpselling,

                                                        priceUpselling: priceUpselling,
                                                        upsellingCampaignId: upsellingCampaign.uniqueId,

                                                        priceProduct: selectedOutletItemsRaw[i].price,

                                                        upc: UPSELLING_SECTION_CODE[props.upsellingSection ? props.upsellingSection : UPSELLING_SECTION.IN_CART],
                                                    });
                                                }
                                            }
                                        }
                                        else if (upsellingItem.upsellByType === UPSELL_BY_TYPE.ORDER_ITEM || upsellingItem.upsellByType === undefined) {
                                            if (upsellingItem.tagIdList && upsellingItem.tagIdList.length > 0) {
                                                // try compare with tags

                                                if (upsellingItem.tagIdList && upsellingItem.tagIdList.length > 0) {
                                                    ////////////////////////////////////////////

                                                    var cartOutletItemList = [];

                                                    if (props.campaignData && props.campaignData.uniqueId) {
                                                        if (props.campaignData.upsellingSection === UPSELLING_SECTION.AFTER_CART) {
                                                            cartOutletItemList = [selectedOutletItem];
                                                        }
                                                        else if (props.campaignData.upsellingSection === UPSELLING_SECTION.AFTER_CHECKOUT ||
                                                            props.campaignData.upsellingSection === UPSELLING_SECTION.AFTER_CHECKOUT_RECOMMENDATION) {
                                                            cartOutletItemList = toCompareItems
                                                                .filter(item => {
                                                                    var outletItem = selectedOutletItemsRaw.find(outletItem => outletItem.uniqueId === item.itemId);

                                                                    return outletItem ? true : false;
                                                                })
                                                                .map(item => {
                                                                    var outletItem = selectedOutletItemsRaw.find(outletItem => outletItem.uniqueId === item.itemId);

                                                                    return outletItem;
                                                                });
                                                        }
                                                        else {
                                                            cartOutletItemList = toCompareItems
                                                                .filter(item => {
                                                                    var outletItem = selectedOutletItemsRaw.find(outletItem => outletItem.uniqueId === item.itemId);

                                                                    return outletItem ? true : false;
                                                                })
                                                                .map(item => {
                                                                    var outletItem = selectedOutletItemsRaw.find(outletItem => outletItem.uniqueId === item.itemId);

                                                                    return outletItem;
                                                                });
                                                        }
                                                    }
                                                    else {
                                                        cartOutletItemList = toCompareItems
                                                            .filter(item => {
                                                                var outletItem = selectedOutletItemsRaw.find(outletItem => outletItem.uniqueId === item.itemId);

                                                                return outletItem ? true : false;
                                                            })
                                                            .map(item => {
                                                                var outletItem = selectedOutletItemsRaw.find(outletItem => outletItem.uniqueId === item.itemId);

                                                                return outletItem;
                                                            });
                                                    }

                                                    //////////////////////////

                                                    // 2024-03-01 - crash prevention

                                                    if (cartOutletItemList.find(findItem => findItem === undefined)) {
                                                        continue;
                                                    }

                                                    //////////////////////////

                                                    ////////////////////////////////////////////

                                                    var isValid = false;

                                                    for (var cartItemIndex = 0; cartItemIndex < cartOutletItemList.length; cartItemIndex++) {
                                                        if (cartOutletItemList[cartItemIndex] && cartOutletItemList[cartItemIndex].crmUserTagIdList && cartOutletItemList[cartItemIndex].crmUserTagIdList.length > 0) {
                                                            for (var j = 0; j < cartOutletItemList[cartItemIndex].crmUserTagIdList.length; j++) {
                                                                var productTagId = cartOutletItemList[cartItemIndex].crmUserTagIdList[j];

                                                                if (upsellingItem.tagIdList.includes(productTagId)) {
                                                                    isValid = true;
                                                                    break;
                                                                }
                                                            }
                                                        }

                                                        if (selectedOutletItemCategoriesDict && selectedOutletItemCategoriesDict[cartOutletItemList[cartItemIndex].categoryId] &&
                                                            selectedOutletItemCategoriesDict[cartOutletItemList[cartItemIndex].categoryId].crmUserTagIdList &&
                                                            selectedOutletItemCategoriesDict[cartOutletItemList[cartItemIndex].categoryId].crmUserTagIdList.length > 0) {
                                                            for (var j = 0; j < selectedOutletItemCategoriesDict[cartOutletItemList[cartItemIndex].categoryId].crmUserTagIdList.length; j++) {
                                                                var categoryIdTagId = selectedOutletItemCategoriesDict[cartOutletItemList[cartItemIndex].categoryId].crmUserTagIdList[j];

                                                                if (upsellingItem.tagIdList.includes(categoryIdTagId)) {
                                                                    isValid = true;
                                                                    break;
                                                                }
                                                            }
                                                        }

                                                        if (isValid) {
                                                            break;
                                                        }
                                                    }

                                                    if (!isValid) {
                                                        // means no matched tag, try to find by category

                                                        // for (var cartItemIndex = 0; cartItemIndex < cartOutletItemList.length; cartItemIndex++) {
                                                        //     if (cartOutletItemList[cartItemIndex].categoryId === selectedOutletItemsRaw[i].categoryId) {
                                                        //         isValid = true;
                                                        //         break;
                                                        //     }
                                                        // }
                                                    }
                                                }

                                                if (isValid) {
                                                    if (toRecommendedItemsTemp.find(findItem => findItem.uniqueId === selectedOutletItemsRaw[i].uniqueId)) {
                                                        // means in the recommended list already, no need add
                                                    }
                                                    else {
                                                        toRecommendedItemsTemp.push({
                                                            ...selectedOutletItemsRaw[i],
                                                            price: priceUpselling,

                                                            priceUpselling: priceUpselling,
                                                            upsellingCampaignId: upsellingCampaign.uniqueId,

                                                            priceProduct: selectedOutletItemsRaw[i].price,

                                                            upc: UPSELLING_SECTION_CODE[props.upsellingSection ? props.upsellingSection : UPSELLING_SECTION.IN_CART],
                                                        });
                                                    }
                                                }
                                            }
                                            else {
                                                if (toRecommendedItemsTemp.find(findItem => findItem.uniqueId === selectedOutletItemsRaw[i].uniqueId)) {
                                                    // means in the recommended list already, no need add
                                                }
                                                else {
                                                    toRecommendedItemsTemp.push({
                                                        ...selectedOutletItemsRaw[i],
                                                        price: priceUpselling,

                                                        priceUpselling: priceUpselling,
                                                        upsellingCampaignId: upsellingCampaign.uniqueId,

                                                        priceProduct: selectedOutletItemsRaw[i].price,

                                                        upc: UPSELLING_SECTION_CODE[props.upsellingSection ? props.upsellingSection : UPSELLING_SECTION.IN_CART],
                                                    });
                                                }
                                            }
                                        }

                                        ////////////////////////////////////////////////////////////////

                                        // var sameCategoryItem = cartItemsProcessed.find(item => item.categoryId === selectedOutletItemsRaw[i].categoryId);

                                        // if (sameCategoryItem) {
                                        //     // got item with same category, continue

                                        //     toRecommendedItemsTemp.push(selectedOutletItemsRaw[i]);
                                        // }
                                        // else {
                                        //     // try compare with tags

                                        //     if (currCrmUser && currCrmUser.uniqueId) {
                                        //         if (selectedOutletItemsRaw[i].crmUserTagIdList && selectedOutletItemsRaw[i].crmUserTagIdList.length > 0) {
                                        //             for (var j = 0; j < selectedOutletItemsRaw[i].crmUserTagIdList.length; j++) {
                                        //                 var userTagId = selectedOutletItemsRaw[i].crmUserTagIdList[j];

                                        //                 if (selectedOutletCRMTagsDict[userTagId] && selectedOutletCRMTagsDict[userTagId].uniqueId) {
                                        //                     var userTag = selectedOutletCRMTagsDict[userTagId];

                                        //                     if (
                                        //                         (userTag.emailList && userTag.emailList.includes(currCrmUser.userEmail))
                                        //                         ||
                                        //                         (userTag.phoneList && userTag.phoneList.includes(currCrmUser.userNumber))
                                        //                     ) {
                                        //                         toRecommendedItemsTemp.push(selectedOutletItemsRaw[i]);

                                        //                         break;
                                        //                     }
                                        //                 }
                                        //             }
                                        //         }
                                        //     }
                                        // }
                                    }

                                    // if (toRecommendedItemsTemp.length >= 3) {
                                    //     break;
                                    // }
                                }
                            }
                        }
                    }
                }

                // if (toRecommendedItemsTemp.length >= 3) {
                //     break;
                // }
            }
        }

        // setToRecommendedItems(toRecommendedItemsTemp.slice(0, 3));
        setToRecommendedItems(toRecommendedItemsTemp);

        TempStore.update((s) => {
            s.recommendedItems = toRecommendedItems
        })
    }, [
        selectedOutletItemsRaw, cartItems, cartItemsProcessed, currCrmUser, selectedOutletCRMTagsDict, orderType, availableUpsellingCampaigns, selectedOutletItemCategoriesDict,

        isPlacingReservationUpselling,
        cartItemsReservation,
        cartItemsProcessedReservation,
    ]);

    // useEffect(() => {
    //     var toRecommendedItemsTemp = [];

    //     for (var i = 0; i < selectedOutletItemsRaw.length; i++) {
    //         var isValidOrderType = selectedOutletItemsRaw[i].hideInOrderTypes && selectedOutletItemsRaw[i].hideInOrderTypes.length > 0
    //             ? (selectedOutletItemsRaw[i].hideInOrderTypes.includes(orderType)
    //                 ? false
    //                 : true)
    //             : true;

    //         var isValidActive = (
    //             selectedOutletItemsRaw[i].isActive &&
    //             (selectedOutletItemsRaw[i].isAvailableDayActive
    //                 ? (selectedOutletItemsRaw[i].effectiveTypeOptions.includes(effectiveDays) &&
    //                     selectedOutletItemsRaw[i].effectiveStartTime && selectedOutletItemsRaw[i].effectiveEndTime &&
    //                     moment().isSameOrAfter(
    //                         moment(selectedOutletItemsRaw[i].effectiveStartTime)
    //                             .year(moment().year())
    //                             .month(moment().month())
    //                             .date(moment().date())
    //                     )
    //                     &&
    //                     moment().isBefore
    //                         (moment(selectedOutletItemsRaw[i].effectiveEndTime)
    //                             .year(moment().year())
    //                             .month(moment().month())
    //                             .date(moment().date())
    //                         ))
    //                 : true) &&
    //             (selectedOutletItemsRaw[i].isOnlineMenu !== undefined ? selectedOutletItemsRaw[i].isOnlineMenu : true) &&
    //             (selectedOutletItemsRaw[i].isStockCountActive !== undefined &&
    //                 selectedOutletItemsRaw[i].isStockCountActive !== false &&
    //                 selectedOutletItemsRaw[i].stockCount !== undefined &&
    //                 selectedOutletItemsRaw[i].toSellIgnoreStock !== undefined
    //                 ? selectedOutletItemsRaw[i].isStockCountActive &&
    //                 selectedOutletItemsRaw[i].stockCount > 0 &&
    //                 (selectedOutletItemsRaw[i].toSellIgnoreStock !== undefined
    //                     ? selectedOutletItemsRaw[i].toSellIgnoreStock
    //                     : true)
    //                 : true)
    //         );

    //         if (isValidOrderType && isValidActive) {
    //             var existingItem = cartItemsProcessed.find(item => item.itemId === selectedOutletItemsRaw[i].uniqueId);

    //             if (!existingItem) {
    //                 // didn't existed in cart, continue

    //                 var sameCategoryItem = cartItemsProcessed.find(item => item.categoryId === selectedOutletItemsRaw[i].categoryId);

    //                 if (sameCategoryItem) {
    //                     // got item with same category, continue

    //                     toRecommendedItemsTemp.push(selectedOutletItemsRaw[i]);
    //                 }
    //                 else {
    //                     // try compare with tags

    //                     if (currCrmUser && currCrmUser.uniqueId) {
    //                         if (selectedOutletItemsRaw[i].crmUserTagIdList && selectedOutletItemsRaw[i].crmUserTagIdList.length > 0) {
    //                             for (var j = 0; j < selectedOutletItemsRaw[i].crmUserTagIdList.length; j++) {
    //                                 var userTagId = selectedOutletItemsRaw[i].crmUserTagIdList[j];

    //                                 if (selectedOutletCRMTagsDict[userTagId] && selectedOutletCRMTagsDict[userTagId].uniqueId) {
    //                                     var userTag = selectedOutletCRMTagsDict[userTagId];

    //                                     if (
    //                                         (userTag.emailList && userTag.emailList.includes(currCrmUser.userEmail))
    //                                         ||
    //                                         (userTag.phoneList && userTag.phoneList.includes(currCrmUser.userNumber))
    //                                     ) {
    //                                         toRecommendedItemsTemp.push(selectedOutletItemsRaw[i]);

    //                                         break;
    //                                     }
    //                                 }
    //                             }
    //                         }
    //                     }
    //                 }
    //             }

    //             if (toRecommendedItemsTemp.length >= 3) {
    //                 break;
    //             }
    //         }
    //     }

    //     setToRecommendedItems(toRecommendedItemsTemp);

    //     // setSelectedOutletItems(selectedOutletItemsRaw);
    // }, [selectedOutletItemsRaw, cartItemsProcessed, currCrmUser, selectedOutletCRMTagsDict, orderType]);

    ///////////////////////////////////////////////////////////////////////////////////////////////////

    const renderRecommendedItems = ({ item }) => {
        var quantity = 0;
        //const cartItem = cartItem.find(obj => obj.itemId === item.id);

        const itemsInCart = cartItems.filter((obj) => obj.itemId === item.uniqueId);
        if (itemsInCart) {
            for (const obj of itemsInCart) {
                quantity += parseInt(obj.quantity);
            }
        }

        var itemNameFontSize = 15;

        if (windowWidth <= 360) {
            itemNameFontSize = 13;
            //console.log(windowWidth)
        }

        const itemNameTextScale = {
            fontSize: itemNameFontSize,
        };

        let excludePromoVoucher = false;
        let outletCategory = selectedOutletItemCategoriesDict[item.categoryId];
        if (
            (item && item.excludePromoVoucher)
            ||
            (outletCategory && outletCategory.excludePromoVoucher)
        ) {
            excludePromoVoucher = true;
        }

        var overrideCategoryPrice = undefined;

        if (
            !excludePromoVoucher &&
            selectedOutletItemCategoriesDict[item.categoryId] &&
            overrideCategoryPriceNameDict[
            selectedOutletItemCategoriesDict[item.categoryId].name
            ] !== undefined
        ) {
            overrideCategoryPrice =
                overrideCategoryPriceNameDict[
                    selectedOutletItemCategoriesDict[item.categoryId].name
                ].overridePrice;
        }

        if (
            // item.categoryId === selectedOutletItemCategory.uniqueId &&
            (item.hideInOrderTypes && item.hideInOrderTypes.length > 0
                ? item.hideInOrderTypes.includes(orderType)
                    ? false
                    : true
                : true)
        ) {
            var amountOffCategory = undefined;
            if (
                selectedOutletItemCategoriesDict[item.categoryId] &&
                amountOffCategoryNameDict[
                selectedOutletItemCategoriesDict[item.categoryId].name
                ] !== undefined
            ) {
                amountOffCategory =
                    amountOffCategoryNameDict[
                    selectedOutletItemCategoriesDict[item.categoryId].name
                    ];
            }

            var percentageOffCategory = undefined;
            if (
                selectedOutletItemCategoriesDict[item.categoryId] &&
                percentageOffCategoryNameDict[
                selectedOutletItemCategoriesDict[item.categoryId].name
                ] !== undefined
            ) {
                percentageOffCategory =
                    percentageOffCategoryNameDict[
                    selectedOutletItemCategoriesDict[item.categoryId].name
                    ];
            }

            var pointsRedeemCategory = undefined;
            if (
                selectedOutletItemCategoriesDict[item.categoryId] &&
                pointsRedeemCategoryNameDict[
                selectedOutletItemCategoriesDict[item.categoryId].name
                ] !== undefined
            ) {
                pointsRedeemCategory =
                    pointsRedeemCategoryNameDict[
                    selectedOutletItemCategoriesDict[item.categoryId].name
                    ];
            }

            var buy1Free1Category = undefined;
            if (
                selectedOutletItemCategoriesDict[item.categoryId] &&
                buy1Free1CategoryNameDict[
                selectedOutletItemCategoriesDict[item.categoryId].name
                ] !== undefined
            ) {
                buy1Free1Category =
                    buy1Free1CategoryNameDict[
                    selectedOutletItemCategoriesDict[item.categoryId].name
                    ];
            }

            var extraPrice = 0;
            if (
                orderType === ORDER_TYPE.DELIVERY &&
                selectedOutlet &&
                selectedOutlet.deliveryPrice
            ) {
                extraPrice = selectedOutlet.deliveryPrice;
            } else if (
                orderType === ORDER_TYPE.PICKUP &&
                selectedOutlet &&
                selectedOutlet.pickUpPrice
            ) {
                extraPrice = selectedOutlet.pickUpPrice;
            }

            if (orderType === ORDER_TYPE.DELIVERY) {
                extraPrice = item.deliveryCharges || 0;

                if (
                    extraPrice &&
                    item.deliveryChargesType === CHARGES_TYPE.PERCENTAGE_BASED
                ) {
                    extraPrice = (item.price * extraPrice) / 100;
                }

                if (!item.deliveryChargesActive) {
                    extraPrice = 0;
                }
            }

            if (orderType === ORDER_TYPE.PICKUP) {
                extraPrice = item.pickUpCharges || 0;

                if (
                    extraPrice &&
                    item.pickUpChargesType === CHARGES_TYPE.PERCENTAGE_BASED
                ) {
                    extraPrice = (item.price * extraPrice) / 100;
                }

                if (!item.pickUpChargesActive) {
                    extraPrice = 0;
                }
            }

            const isNotAvailable = !item.isActive ||
                !(item.isAvailableDayActive
                    ? (item.effectiveTypeOptions.includes(effectiveDays) &&
                        item.effectiveStartTime && item.effectiveEndTime &&
                        moment().isSameOrAfter(
                            moment(item.effectiveStartTime)
                                .year(moment().year())
                                .month(moment().month())
                                .date(moment().date())
                        )
                        &&
                        moment().isBefore
                            (moment(item.effectiveEndTime)
                                .year(moment().year())
                                .month(moment().month())
                                .date(moment().date())
                            ))
                    : true) ||
                !(item.isOnlineMenu !== undefined
                    ? item.isOnlineMenu
                    : true) ||
                !(item.isStockCountActive !== undefined &&
                    item.isStockCountActive !== false &&
                    item.stockCount !== undefined &&
                    item.toSellIgnoreStock !== undefined
                    ? item.isStockCountActive &&
                    item.stockCount > 0 &&
                    (item.toSellIgnoreStock !== undefined
                        ? item.toSellIgnoreStock
                        : true)
                    : true);

            let specialTag = '';
            if (item.specialTags && item.specialTags.length > 0 &&
                typeof item.specialTags[0] === 'string') {
                specialTag = item.specialTags[0];
            }

            return (
                <TouchableOpacity
                    onPress={async () => {
                        if (global.outletName) {
                            logEvent(global.analytics, ANALYTICS.WO_UPSELL_ITEM_CLICK, {
                                eventNameParsed: ANALYTICS_PARSED.WO_UPSELL_ITEM_CLICK,

                                outletName: global.outletName ? `${global.outletName} (Web)` : '',

                                webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
                            });
                        }

                        if (checkCartOutlet()) {
                            // setState({ cartWarning: true, })

                            setTempItem(item);

                            setCartWarning(true);
                        } else {
                            if (
                                item.isActive &&
                                (item.isAvailableDayActive
                                    ? (item.effectiveTypeOptions.includes(effectiveDays) &&
                                        item.effectiveStartTime && item.effectiveEndTime &&
                                        moment().isSameOrAfter(
                                            moment(item.effectiveStartTime)
                                                .year(moment().year())
                                                .month(moment().month())
                                                .date(moment().date())
                                        )
                                        &&
                                        moment().isBefore
                                            (moment(item.effectiveEndTime)
                                                .year(moment().year())
                                                .month(moment().month())
                                                .date(moment().date())
                                            ))
                                    : true) &&
                                (item.isOnlineMenu !== undefined ? item.isOnlineMenu : true) &&
                                (item.isStockCountActive !== undefined &&
                                    item.isStockCountActive !== false &&
                                    item.stockCount !== undefined &&
                                    item.toSellIgnoreStock !== undefined
                                    ? item.isStockCountActive &&
                                    item.stockCount > 0 &&
                                    (item.toSellIgnoreStock !== undefined
                                        ? item.toSellIgnoreStock
                                        : true)
                                    : true)
                            ) {
                                // var priceUpselling = item.price;
                                // if (currUpsellingCampaign && currUpsellingCampaign.uniqueId) {
                                //     for (var upsellingIndex = 0; upsellingIndex < currUpsellingCampaign.productList.length; upsellingIndex++) {
                                //         if (currUpsellingCampaign.productList[upsellingIndex].productId === item.uniqueId) {

                                //         }
                                //     }
                                // }

                                CommonStore.update((s) => {
                                    s.selectedOutletItem = item;

                                    // s.selectedAddOnIdForChoiceQtyDict = {};
                                });

                                // const subdomain = await AsyncStorage.getItem("latestSubdomain");

                                // if (!subdomain) {
                                //   linkTo && linkTo(`${prefix}/outlet/menu/item`);
                                // } else {
                                //   linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu/item`);
                                // }

                                // linkTo && linkTo(`${prefix}/outlet/menu/item`);

                                global.isFromRecommendedItems = true;

                                global.currUpsellingCampaign = currUpsellingCampaign;

                                global.currUpsellingSection = props.upsellingSection;

                                if (isMobile() && selectedOutlet && true) {
                                    CommonStore.update((s) => {
                                        s.menuItemDetailModal = true;
                                    });

                                    global.menuItemDetailModal = true;

                                    window.history.pushState({
                                        page: 'menuItemDetailModal',
                                    }, '');

                                    CommonStore.update(s => {
                                        s.currPageIframe = 'MenuItemDetails';
                                    });

                                    // window.history.pushState(null, '', window.location.href);
                                    // window.history.forward();

                                    // const subdomain = await AsyncStorage.getItem("latestSubdomain");

                                    // if (!subdomain) {
                                    //     global.linkToFunc && global.linkToFunc(`${prefix}/outlet/menu/item`);
                                    // } else {
                                    //     global.linkToFunc && global.linkToFunc(`${prefix}/outlet/${subdomain}/menu/item`);
                                    // }

                                    // props.navigation.navigate(
                                    //     "Product Details - KooDoo Web Order",
                                    //     {
                                    //         refresh: refresh.bind(this),
                                    //         menuItem: item,
                                    //         outletData: selectedOutlet,
                                    //     }
                                    // );
                                }
                                else {
                                    props.navigation.navigate(
                                        "Product Details - KooDoo Web Order",
                                        {
                                            refresh: refresh.bind(this),
                                            menuItem: item,
                                            outletData: selectedOutlet,
                                        }
                                    );
                                }
                            } else {
                                // window.confirm(
                                //   'Info',
                                //   'Sorry, this product is not available for now.',
                                // );

                                CommonStore.update((s) => {
                                    s.alertObj = {
                                        title: "Info",
                                        message: "Sorry, this product is not available for now.",
                                    };
                                });
                            }
                        }
                    }}
                >
                    <View
                        style={{
                            flexDirection: "row",
                            paddingBottom: 20,
                            paddingTop: 5,
                            display: "flex",
                            // justifyContent: "space-between",
                            // borderWidth: 0.5,
                            // borderRadius: 5,
                            // marginHorizontal: 1,
                            //height: 250,
                            width: isMobile() ? windowWidth * 0.43 : windowWidth * 0.17,
                            // alignSelf: 'center',
                            alignItems: 'center',
                        }}
                    >
                        <View
                            style={{
                                flexDirection: "column",
                                alignContent: "center",
                                alignItems: "center",
                                width: '100%',
                                display: "flex",
                                justifyContent: "flex-start",
                                // backgroundColor: 'blue',
                            }}
                        >
                            <View style={{
                                width: '100%',
                            }}>
                                <View
                                    style={[
                                        {
                                            backgroundColor: Colors.secondaryColor,
                                            // width: 60,
                                            // height: 60,
                                            width: isMobile()
                                                ? windowWidth * 0.4
                                                : windowWidth * 0.05,
                                            height: isMobile()
                                                ? windowWidth * 0.32
                                                : windowWidth * 0.05,
                                            borderRadius: 2,
                                            marginBottom: 10,
                                        },
                                        item.image
                                            ? {}
                                            : {
                                                display: "flex",
                                                alignItems: "center",
                                                justifyContent: "center",
                                            },
                                    ]}
                                >
                                    {item.image ? (
                                        // <Image source={{ uri: item.image }} style={{
                                        //   width: windowWidth * 0.22,
                                        //   height: windowWidth * 0.22,
                                        //   borderRadius: 10
                                        // }} />
                                        <AsyncImage
                                            source={{ uri: item.image }}
                                            item={item}
                                            style={{
                                                width: isMobile()
                                                    ? windowWidth * 0.4
                                                    : windowWidth * 0.05,
                                                height: isMobile()
                                                    ? windowWidth * 0.32
                                                    : windowWidth * 0.05,
                                                borderRadius: 2,
                                            }}
                                        />
                                    ) : (
                                        // <Ionicons name="fast-food-outline" size={50} />
                                        <View
                                            style={{
                                                width: isMobile()
                                                    ? windowWidth * 0.4
                                                    : windowWidth * 0.05,
                                                height: isMobile()
                                                    ? windowWidth * 0.32
                                                    : windowWidth * 0.05,
                                                display: "flex",
                                                justifyContent: "center",
                                                alignItems: "center",
                                            }}
                                        >
                                            <Ionicons
                                                name="fast-food-outline"
                                                // size={45}
                                                size={
                                                    isMobile() ? windowWidth * 0.2 : windowWidth * 0.02
                                                }
                                            />
                                        </View>
                                    )}

                                    {(isNotAvailable || specialTag) ? (
                                        <View
                                            style={{
                                                position: "absolute",
                                                zIndex: 3,
                                            }}
                                        >
                                            <View
                                                style={{
                                                    // width: 120,
                                                    width: isMobile()
                                                        ? windowWidth * 0.43
                                                        : windowWidth * 0.06,
                                                    left: isMobile()
                                                        ? -windowWidth * 0.03
                                                        : -windowWidth * 0.01,
                                                    padding: 0,
                                                    paddingLeft: isMobile()
                                                        ? windowWidth * 0.02
                                                        : windowWidth * 0.005,
                                                    justifyContent: "center",
                                                    alignItems: "center",
                                                    backgroundColor: isNotAvailable ? Colors.tabRed : Colors.primaryColor,
                                                    height: 20,
                                                    borderTopRightRadius: 2,
                                                    borderBottomRightRadius: 2,

                                                    ...(!item.image && {
                                                        left: isMobile()
                                                            ? -windowWidth * 0.015
                                                            : -windowWidth * 0.005,
                                                        bottom: isMobile()
                                                            ? windowWidth * 0.074
                                                            : windowWidth * 0.014,
                                                    }),
                                                }}
                                            >
                                                <Text
                                                    style={{
                                                        color: "#FFF",
                                                        fontFamily: "NunitoSans-Bold",
                                                        fontSize: 10,
                                                        bottom: 1,
                                                    }}
                                                >
                                                    {isNotAvailable ? 'Not available' : specialTag}
                                                </Text>
                                            </View>
                                            <View
                                                style={{
                                                    left: isMobile()
                                                        ? -windowWidth * 0.029
                                                        : -windowWidth * 0.01,
                                                    bottom: "1%",
                                                    width: 0,
                                                    height: 0,
                                                    backgroundColor: "transparent",
                                                    borderStyle: "solid",
                                                    borderRightWidth: isMobile()
                                                        ? windowWidth * 0.03
                                                        : windowWidth * 0.01,
                                                    borderTopWidth: isMobile()
                                                        ? windowWidth * 0.03
                                                        : windowWidth * 0.01,
                                                    borderRightColor: "transparent",
                                                    borderTopColor: isNotAvailable ? Colors.tabRed : Colors.primaryColor,
                                                    transform: [{ rotate: "90deg" }],

                                                    ...(!item.image && {
                                                        left: isMobile()
                                                            ? -windowWidth * 0.0135
                                                            : -windowWidth * 0.005,
                                                        bottom: isMobile()
                                                            ? windowWidth * 0.074
                                                            : windowWidth * 0.014,
                                                    }),
                                                }}
                                            />
                                        </View>
                                    ) : (
                                        <></>
                                    )}
                                </View>

                                {pointsRedeemItemSkuDict[item.sku] !== undefined ||
                                    pointsRedeemCategory !== undefined ? (
                                    <View
                                        style={{
                                            marginTop: 5,
                                        }}
                                    >
                                        <Text
                                            style={{
                                                color: Colors.primaryColor,
                                                fontFamily: "NunitoSans-SemiBold",
                                                fontSize: 12,
                                                textAlign: "center",
                                            }}
                                        >
                                            {pointsRedeemItemSkuDict[item.sku] !== undefined
                                                ? `${pointsRedeemItemSkuDict[item.sku]
                                                    .conversionPointsFrom
                                                } Points to RM${pointsRedeemItemSkuDict[item.sku]
                                                    .conversionCurrencyTo
                                                }`
                                                : `${pointsRedeemCategory.conversionPointsFrom} Points to RM${pointsRedeemCategory.conversionCurrencyTo}`}
                                        </Text>
                                    </View>
                                ) : (
                                    <></>
                                )}

                                {buy1Free1ItemSkuDict[item.sku] !== undefined ||
                                    buy1Free1Category !== undefined ? (
                                    <View
                                        style={{
                                            marginTop: 5,
                                        }}
                                    >
                                        <Text
                                            style={{
                                                color: Colors.descriptionColor,
                                                fontFamily: "NunitoSans-SemiBold",
                                                fontSize: 12,
                                            }}
                                        >
                                            {`Bundle Deal`}
                                            {/* {buy1Free1ItemSkuDict[item.sku] !== undefined
                            ? `Buy ${
                                buy1Free1ItemSkuDict[item.sku].buyAmount
                              } Free ${buy1Free1ItemSkuDict[item.sku].getAmount}`
                            : `Buy ${buy1Free1Category.buyAmount} Free ${buy1Free1Category.getAmount}`} */}
                                        </Text>
                                    </View>
                                ) : (
                                    <></>
                                )}
                            </View>

                            <View
                                style={{
                                    //marginLeft: 15,
                                    // flexDirection: 'row',
                                    // flexShrink: 1,
                                    width: isMobile() ? "100%" : '65%',
                                    alignItems: 'flex-start',
                                    // backgroundColor: 'red',

                                    paddingLeft: isMobile() ? 1 : 0,
                                }}
                            >
                                <Text
                                    // numberOfLines={1}
                                    style={[
                                        itemNameTextScale,
                                        {
                                            fontSize: 14,
                                            textTransform: "uppercase",
                                            fontFamily: "NunitoSans-Bold",
                                            textAlign: 'left',
                                            marginBottom: 0,
                                            height: isMobile() ? 40 : 40,
                                        },
                                    ]}
                                    numberOfLines={isMobile() ? 2 : 2}
                                >
                                    {item.name}
                                </Text>

                                <View
                                    style={{
                                        flexDirection: "row",
                                        alignItems: "center",
                                    }}
                                >
                                    <Text
                                        style={{
                                            color: Colors.primaryColor,
                                            fontFamily: "NunitoSans-Bold",
                                            paddingTop: 5,
                                            fontSize: 14,
                                            textDecorationLine:
                                                (!excludePromoVoucher && (overrideItemPriceSkuDict[item.sku] !== undefined ||
                                                    overrideCategoryPrice !== undefined))
                                                    ? "line-through"
                                                    : "none",

                                            ...(((extraPrice + item.price) !== (extraPrice + item.priceProduct) && !(!excludePromoVoucher && (overrideItemPriceSkuDict[item.sku] !== undefined || overrideCategoryPrice !== undefined)))) && {
                                                fontSize: 10,
                                                paddingTop: 3,
                                                textDecorationLine: 'line-through'
                                            },

                                            ...(!excludePromoVoucher && (overrideItemPriceSkuDict[item.sku] !== undefined ||
                                                overrideCategoryPrice !== undefined)) && {
                                                fontSize: 10,
                                                paddingTop: 8,
                                            },
                                        }}
                                    >
                                        RM{parseFloat(extraPrice + item.priceProduct).toFixed(2)}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[item.unitType]}` : ''}
                                    </Text>

                                    {(((extraPrice + item.price) !== (extraPrice + item.priceProduct) && !(!excludePromoVoucher && (overrideItemPriceSkuDict[item.sku] !== undefined || overrideCategoryPrice !== undefined)))) ?
                                        <Text
                                            style={{
                                                color: Colors.secondaryColor,
                                                fontFamily: "NunitoSans-Bold",
                                                paddingTop: 0,
                                                fontSize: 14,
                                                marginLeft: 5,
                                            }}
                                        >
                                            RM{parseFloat(extraPrice + item.price).toFixed(2)}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[item.unitType]}` : ''}
                                        </Text> :
                                        <></>}

                                    {!excludePromoVoucher && (overrideItemPriceSkuDict[item.sku] !== undefined ||
                                        overrideCategoryPrice !== undefined) ? (
                                        <Text
                                            style={{
                                                color: Colors.secondaryColor,
                                                fontFamily: "NunitoSans-Bold",
                                                paddingTop: 5,
                                                fontSize: 14,
                                                marginLeft: 5,
                                            }}
                                        >
                                            RM
                                            {overrideItemPriceSkuDict[item.sku] &&
                                                overrideItemPriceSkuDict[item.sku].overridePrice !==
                                                undefined
                                                ? parseFloat(
                                                    overrideItemPriceSkuDict[item.sku].overridePrice
                                                ).toFixed(2)
                                                : parseFloat(overrideCategoryPrice).toFixed(2)}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[item.unitType]}` : ''}
                                        </Text>
                                    ) : (
                                        <></>
                                    )}
                                </View>

                                <View
                                    style={{
                                        flexDirection: "row",
                                        alignItems: "center",
                                    }}
                                >
                                    {amountOffItemSkuDict[item.sku] !== undefined ||
                                        amountOffCategory !== undefined ? (
                                        <Text
                                            style={{
                                                color: Colors.descriptionColor,
                                                fontFamily: "NunitoSans-SemiBold",
                                                paddingTop: 5,
                                                fontSize: 14,
                                                // marginLeft: 5,
                                            }}
                                        >
                                            {amountOffItemSkuDict[item.sku] !== undefined
                                                ? `Buy ${amountOffItemSkuDict[item.sku].quantityMin
                                                } pcs to enjoy RM${amountOffItemSkuDict[
                                                    item.sku
                                                ].amountOff.toFixed(2)} off\n(Min purchases: RM${amountOffItemSkuDict[item.sku].priceMin
                                                })`
                                                : `Buy ${amountOffCategory.quantityMin} pcs to enjoy ${amountOffCategory.amountOff.toFixed(
                                                    0
                                                )}% off\n(Min purchases: RM${amountOffCategory.priceMin
                                                })`}
                                        </Text>
                                    ) : (
                                        <></>
                                    )}
                                </View>

                                <View
                                    style={{
                                        flexDirection: "row",
                                        alignItems: "center",
                                    }}
                                >
                                    {percentageOffItemSkuDict[item.sku] !== undefined ||
                                        percentageOffCategory !== undefined ? (
                                        <Text
                                            style={{
                                                color: Colors.descriptionColor,
                                                fontFamily: "NunitoSans-SemiBold",
                                                paddingTop: 5,
                                                fontSize: 14,
                                                // marginLeft: 5,
                                            }}
                                        >
                                            {percentageOffItemSkuDict[item.sku] !== undefined
                                                ? `Buy ${percentageOffItemSkuDict[item.sku].quantityMin
                                                } pcs to enjoy ${percentageOffItemSkuDict[
                                                    item.sku
                                                ].percentageOff.toFixed(0)}% off\n(Min purchases: RM${percentageOffItemSkuDict[item.sku].priceMin
                                                })`
                                                : `Buy ${percentageOffCategory.quantityMin} pcs to enjoy ${percentageOffCategory.percentageOff.toFixed(
                                                    0
                                                )}% off\n(Min purchases: RM${percentageOffCategory.priceMin
                                                })`}
                                        </Text>
                                    ) : (
                                        <></>
                                    )}
                                </View>
                            </View>
                        </View>

                        {/* <View
                            style={{
                                flexDirection: "row",
                                // width: "20%",
                                // marginLeft: 60
                                // backgroundColor: 'red',
                                ...(!isMobile() && {
                                    // width: windowWidth,
                                    height: 26,
                                    // right: 0,
                                    alignSelf: "center",
                                }),
                            }}
                        >
                            <View
                                style={{
                                    backgroundColor: "#e3e1e1",
                                    // width: 67,
                                    // height: 24,

                                    width: 68,
                                    height: 26,

                                    // paddingVertical: 4,
                                    // paddingHorizontal: 20,

                                    borderRadius: 10,
                                    justifyContent: "center",
                                    alignSelf: "center",
                                }}
                            >
                                <TouchableOpacity
                                    onPress={async () => {
                                        if (checkCartOutlet()) {
                                            // setState({ cartWarning: true, })

                                            setTempItem(item);

                                            setCartWarning(true);
                                        } else {
                                            if (
                                                item.isActive &&
                                                (item.isAvailableDayActive
                                                    ? (item.effectiveTypeOptions.includes(effectiveDays) &&
                                                        item.effectiveStartTime && item.effectiveEndTime &&
                                                        moment().isSameOrAfter(
                                                            moment(item.effectiveStartTime)
                                                                .year(moment().year())
                                                                .month(moment().month())
                                                                .date(moment().date())
                                                        )
                                                        &&
                                                        moment().isBefore
                                                            (moment(item.effectiveEndTime)
                                                                .year(moment().year())
                                                                .month(moment().month())
                                                                .date(moment().date())
                                                            ))
                                                    : true) &&
                                                (item.isOnlineMenu !== undefined
                                                    ? item.isOnlineMenu
                                                    : true) &&
                                                (item.isStockCountActive !== undefined &&
                                                    item.isStockCountActive !== false &&
                                                    item.stockCount !== undefined &&
                                                    item.toSellIgnoreStock !== undefined
                                                    ? item.isStockCountActive &&
                                                    item.stockCount > 0 &&
                                                    (item.toSellIgnoreStock !== undefined
                                                        ? item.toSellIgnoreStock
                                                        : true)
                                                    : true)
                                            ) {
                                                CommonStore.update((s) => {
                                                    s.selectedOutletItem = item;
                                                });

                                                // const subdomain = await AsyncStorage.getItem("latestSubdomain");

                                                // if (!subdomain) {
                                                //   linkTo && linkTo(`${prefix}/outlet/menu/item`);
                                                // } else {
                                                //   linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu/item`);
                                                // }

                                                // linkTo && linkTo(`${prefix}/outlet/menu/item`);

                                                props.navigation.navigate(
                                                    "Product Details - KooDoo Web Order",
                                                    {
                                                        refresh: refresh.bind(this),
                                                        menuItem: item,
                                                        outletData: selectedOutlet,
                                                    }
                                                );
                                            } else {
                                                // window.confirm(
                                                //   'Info',
                                                //   'Sorry, this product is not available for now.',
                                                // );

                                                CommonStore.update((s) => {
                                                    s.alertObj = {
                                                        title: "Info",
                                                        message:
                                                            "Sorry, this product is not available for now.",
                                                    };
                                                });
                                            }
                                        }
                                    }}
                                >
                                    <Text
                                        style={{
                                            alignSelf: "center",
                                            color: "#8f8f8f",
                                            fontSize: 13,
                                            fontFamily: "NunitoSans-Bold",
                                        }}
                                    >
                                        {quantity > 0 ? quantity : "Add"}
                                    </Text>
                                </TouchableOpacity>
                            </View>
                        </View> */}
                    </View>
                </TouchableOpacity >
            );
        }
    };

    const renderUpsellingLarge = ({ item }) => {
        var quantity = 0;
        //const cartItem = cartItem.find(obj => obj.itemId === item.id);

        const itemsInCart = cartItems.filter((obj) => obj.itemId === item.uniqueId);
        if (itemsInCart) {
            for (const obj of itemsInCart) {
                quantity += parseInt(obj.quantity);
            }
        }

        var itemNameFontSize = 15;

        if (windowWidth <= 360) {
            itemNameFontSize = 13;
            //console.log(windowWidth)
        }

        const itemNameTextScale = {
            fontSize: itemNameFontSize,
        };

        let excludePromoVoucher = false;
        let outletCategory = selectedOutletItemCategoriesDict[item.categoryId];
        if (
            (item && item.excludePromoVoucher)
            ||
            (outletCategory && outletCategory.excludePromoVoucher)
        ) {
            excludePromoVoucher = true;
        }

        var overrideCategoryPrice = undefined;

        if (
            !excludePromoVoucher &&
            selectedOutletItemCategoriesDict[item.categoryId] &&
            overrideCategoryPriceNameDict[
            selectedOutletItemCategoriesDict[item.categoryId].name
            ] !== undefined
        ) {
            overrideCategoryPrice =
                overrideCategoryPriceNameDict[
                    selectedOutletItemCategoriesDict[item.categoryId].name
                ].overridePrice;
        }

        if (
            // item.categoryId === selectedOutletItemCategory.uniqueId &&
            (item.hideInOrderTypes && item.hideInOrderTypes.length > 0
                ? item.hideInOrderTypes.includes(orderType)
                    ? false
                    : true
                : true)
        ) {
            var amountOffCategory = undefined;
            if (
                selectedOutletItemCategoriesDict[item.categoryId] &&
                amountOffCategoryNameDict[
                selectedOutletItemCategoriesDict[item.categoryId].name
                ] !== undefined
            ) {
                amountOffCategory =
                    amountOffCategoryNameDict[
                    selectedOutletItemCategoriesDict[item.categoryId].name
                    ];
            }

            var percentageOffCategory = undefined;
            if (
                selectedOutletItemCategoriesDict[item.categoryId] &&
                percentageOffCategoryNameDict[
                selectedOutletItemCategoriesDict[item.categoryId].name
                ] !== undefined
            ) {
                percentageOffCategory =
                    percentageOffCategoryNameDict[
                    selectedOutletItemCategoriesDict[item.categoryId].name
                    ];
            }

            var pointsRedeemCategory = undefined;
            if (
                selectedOutletItemCategoriesDict[item.categoryId] &&
                pointsRedeemCategoryNameDict[
                selectedOutletItemCategoriesDict[item.categoryId].name
                ] !== undefined
            ) {
                pointsRedeemCategory =
                    pointsRedeemCategoryNameDict[
                    selectedOutletItemCategoriesDict[item.categoryId].name
                    ];
            }

            var buy1Free1Category = undefined;
            if (
                selectedOutletItemCategoriesDict[item.categoryId] &&
                buy1Free1CategoryNameDict[
                selectedOutletItemCategoriesDict[item.categoryId].name
                ] !== undefined
            ) {
                buy1Free1Category =
                    buy1Free1CategoryNameDict[
                    selectedOutletItemCategoriesDict[item.categoryId].name
                    ];
            }

            var extraPrice = 0;
            if (
                orderType === ORDER_TYPE.DELIVERY &&
                selectedOutlet &&
                selectedOutlet.deliveryPrice
            ) {
                extraPrice = selectedOutlet.deliveryPrice;
            } else if (
                orderType === ORDER_TYPE.PICKUP &&
                selectedOutlet &&
                selectedOutlet.pickUpPrice
            ) {
                extraPrice = selectedOutlet.pickUpPrice;
            }

            if (orderType === ORDER_TYPE.DELIVERY) {
                extraPrice = item.deliveryCharges || 0;

                if (
                    extraPrice &&
                    item.deliveryChargesType === CHARGES_TYPE.PERCENTAGE_BASED
                ) {
                    extraPrice = (item.price * extraPrice) / 100;
                }

                if (!item.deliveryChargesActive) {
                    extraPrice = 0;
                }
            }

            if (orderType === ORDER_TYPE.PICKUP) {
                extraPrice = item.pickUpCharges || 0;

                if (
                    extraPrice &&
                    item.pickUpChargesType === CHARGES_TYPE.PERCENTAGE_BASED
                ) {
                    extraPrice = (item.price * extraPrice) / 100;
                }

                if (!item.pickUpChargesActive) {
                    extraPrice = 0;
                }
            }

            const isNotAvailable = !item.isActive ||
                !(item.isAvailableDayActive
                    ? (item.effectiveTypeOptions.includes(effectiveDays) &&
                        item.effectiveStartTime && item.effectiveEndTime &&
                        moment().isSameOrAfter(
                            moment(item.effectiveStartTime)
                                .year(moment().year())
                                .month(moment().month())
                                .date(moment().date())
                        )
                        &&
                        moment().isBefore
                            (moment(item.effectiveEndTime)
                                .year(moment().year())
                                .month(moment().month())
                                .date(moment().date())
                            ))
                    : true) ||
                !(item.isOnlineMenu !== undefined
                    ? item.isOnlineMenu
                    : true) ||
                !(item.isStockCountActive !== undefined &&
                    item.isStockCountActive !== false &&
                    item.stockCount !== undefined &&
                    item.toSellIgnoreStock !== undefined
                    ? item.isStockCountActive &&
                    item.stockCount > 0 &&
                    (item.toSellIgnoreStock !== undefined
                        ? item.toSellIgnoreStock
                        : true)
                    : true);

            let specialTag = '';
            if (item.specialTags && item.specialTags.length > 0 &&
                typeof item.specialTags[0] === 'string') {
                specialTag = item.specialTags[0];
            }

            return (
                <TouchableOpacity
                    onPress={async () => {
                        if (global.outletName) {
                            logEvent(global.analytics, ANALYTICS.WO_UPSELL_ITEM_CLICK, {
                                eventNameParsed: ANALYTICS_PARSED.WO_UPSELL_ITEM_CLICK,

                                outletName: global.outletName ? `${global.outletName} (Web)` : '',

                                webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
                            });
                        }

                        if (checkCartOutlet()) {
                            // setState({ cartWarning: true, })

                            setTempItem(item);

                            setCartWarning(true);
                        } else {
                            if (
                                item.isActive &&
                                (item.isAvailableDayActive
                                    ? (item.effectiveTypeOptions.includes(effectiveDays) &&
                                        item.effectiveStartTime && item.effectiveEndTime &&
                                        moment().isSameOrAfter(
                                            moment(item.effectiveStartTime)
                                                .year(moment().year())
                                                .month(moment().month())
                                                .date(moment().date())
                                        )
                                        &&
                                        moment().isBefore
                                            (moment(item.effectiveEndTime)
                                                .year(moment().year())
                                                .month(moment().month())
                                                .date(moment().date())
                                            ))
                                    : true) &&
                                (item.isOnlineMenu !== undefined ? item.isOnlineMenu : true) &&
                                (item.isStockCountActive !== undefined &&
                                    item.isStockCountActive !== false &&
                                    item.stockCount !== undefined &&
                                    item.toSellIgnoreStock !== undefined
                                    ? item.isStockCountActive &&
                                    item.stockCount > 0 &&
                                    (item.toSellIgnoreStock !== undefined
                                        ? item.toSellIgnoreStock
                                        : true)
                                    : true)
                            ) {

                                CommonStore.update((s) => {
                                    s.selectedOutletItem = item;

                                    // s.selectedAddOnIdForChoiceQtyDict = {};
                                });

                                global.isFromRecommendedItems = true;

                                global.currUpsellingCampaign = currUpsellingCampaign;

                                global.currUpsellingSection = props.upsellingSection;

                                if (isMobile() && selectedOutlet && true) {
                                    CommonStore.update((s) => {
                                        s.menuItemDetailModal = true;
                                    });

                                    global.menuItemDetailModal = true;

                                    window.history.pushState({
                                        page: 'menuItemDetailModal',
                                    }, '');

                                    CommonStore.update(s => {
                                        s.currPageIframe = 'MenuItemDetails';
                                    });
                                }
                                else {
                                    props.navigation.navigate(
                                        "Product Details - KooDoo Web Order",
                                        {
                                            refresh: refresh.bind(this),
                                            menuItem: item,
                                            outletData: selectedOutlet,
                                        }
                                    );
                                }
                            } else {
                                // window.confirm(
                                //   'Info',
                                //   'Sorry, this product is not available for now.',
                                // );

                                CommonStore.update((s) => {
                                    s.alertObj = {
                                        title: "Info",
                                        message: "Sorry, this product is not available for now.",
                                    };
                                });
                            }
                        }
                    }}
                >
                    <View
                        style={{
                            flexDirection: "row",
                            paddingBottom: 20,
                            paddingTop: 5,
                            display: "flex",
                            width: isMobile() ? windowWidth * 0.43 : windowWidth * 0.17,
                            // alignSelf: 'center',
                            alignItems: 'center',
                        }}
                    >
                        <View
                            style={{
                                flexDirection: "column",
                                alignContent: "flex-start",
                                alignItems: "flex-start",
                                width: '100%',
                                display: "flex",
                                justifyContent: "flex-start",
                                // backgroundColor: 'blue',
                            }}
                        >
                            <View style={{
                            }}>
                                <View
                                    style={[
                                        {
                                            backgroundColor: Colors.secondaryColor,
                                            // width: 60,
                                            // height: 60,
                                            width: isMobile()
                                                ? windowWidth * 0.37
                                                : windowWidth * 0.05,
                                            height: isMobile()
                                                ? windowWidth * 0.32
                                                : windowWidth * 0.05,
                                            borderRadius: 2,
                                            marginBottom: 10,
                                        },
                                        item.image
                                            ? {}
                                            : {
                                                display: "flex",
                                                alignItems: "center",
                                                justifyContent: "center",
                                            },
                                    ]}
                                >
                                    {item.image ? (
                                        <AsyncImage
                                            source={{ uri: item.image }}
                                            item={item}
                                            style={{
                                                width: isMobile()
                                                    ? windowWidth * 0.37
                                                    : windowWidth * 0.05,
                                                height: isMobile()
                                                    ? windowWidth * 0.32
                                                    : windowWidth * 0.05,
                                                borderRadius: 2,
                                            }}
                                        />
                                    ) : (
                                        // <Ionicons name="fast-food-outline" size={50} />
                                        <View
                                            style={{
                                                width: isMobile()
                                                    ? windowWidth * 0.37
                                                    : windowWidth * 0.05,
                                                height: isMobile()
                                                    ? windowWidth * 0.32
                                                    : windowWidth * 0.05,
                                                display: "flex",
                                                justifyContent: "center",
                                                alignItems: "center",
                                            }}
                                        >
                                            <Ionicons
                                                name="fast-food-outline"
                                                // size={45}
                                                size={
                                                    isMobile() ? windowWidth * 0.12 : windowWidth * 0.02
                                                }
                                            />
                                        </View>
                                    )}

                                    {(isNotAvailable || specialTag) ? (
                                        <View
                                            style={{
                                                position: "absolute",
                                                zIndex: 3,
                                            }}
                                        >
                                            <View
                                                style={{
                                                    // width: 120,
                                                    width: isMobile()
                                                        ? windowWidth * 0.33
                                                        : windowWidth * 0.06,
                                                    left: isMobile()
                                                        ? -windowWidth * 0.03
                                                        : -windowWidth * 0.01,
                                                    padding: 0,
                                                    paddingLeft: isMobile()
                                                        ? windowWidth * 0.02
                                                        : windowWidth * 0.005,
                                                    justifyContent: "center",
                                                    alignItems: "center",
                                                    backgroundColor: isNotAvailable ? Colors.tabRed : Colors.primaryColor,
                                                    height: 20,
                                                    borderTopRightRadius: 2,
                                                    borderBottomRightRadius: 2,

                                                    ...(!item.image && {
                                                        left: isMobile()
                                                            ? -windowWidth * 0.015
                                                            : -windowWidth * 0.005,
                                                        bottom: isMobile()
                                                            ? windowWidth * 0.074
                                                            : windowWidth * 0.014,
                                                    }),
                                                }}
                                            >
                                                <Text
                                                    style={{
                                                        color: "#FFF",
                                                        fontFamily: "NunitoSans-Bold",
                                                        fontSize: 10,
                                                        bottom: 1,
                                                    }}
                                                >
                                                    {isNotAvailable ? 'Not available' : specialTag}
                                                </Text>
                                            </View>
                                            <View
                                                style={{
                                                    left: isMobile()
                                                        ? -windowWidth * 0.029
                                                        : -windowWidth * 0.01,
                                                    bottom: "1%",
                                                    width: 0,
                                                    height: 0,
                                                    backgroundColor: "transparent",
                                                    borderStyle: "solid",
                                                    borderRightWidth: isMobile()
                                                        ? windowWidth * 0.03
                                                        : windowWidth * 0.01,
                                                    borderTopWidth: isMobile()
                                                        ? windowWidth * 0.03
                                                        : windowWidth * 0.01,
                                                    borderRightColor: "transparent",
                                                    borderTopColor: isNotAvailable ? Colors.tabRed : Colors.primaryColor,
                                                    transform: [{ rotate: "90deg" }],

                                                    ...(!item.image && {
                                                        left: isMobile()
                                                            ? -windowWidth * 0.0135
                                                            : -windowWidth * 0.005,
                                                        bottom: isMobile()
                                                            ? windowWidth * 0.074
                                                            : windowWidth * 0.014,
                                                    }),
                                                }}
                                            />
                                        </View>
                                    ) : (
                                        <></>
                                    )}
                                </View>

                                {pointsRedeemItemSkuDict[item.sku] !== undefined ||
                                    pointsRedeemCategory !== undefined ? (
                                    <View
                                        style={{
                                            marginTop: 5,
                                        }}
                                    >
                                        <Text
                                            style={{
                                                color: Colors.primaryColor,
                                                fontFamily: "NunitoSans-SemiBold",
                                                fontSize: 12,
                                                textAlign: "center",
                                            }}
                                        >
                                            {pointsRedeemItemSkuDict[item.sku] !== undefined
                                                ? `${pointsRedeemItemSkuDict[item.sku]
                                                    .conversionPointsFrom
                                                } Points to RM${pointsRedeemItemSkuDict[item.sku]
                                                    .conversionCurrencyTo
                                                }`
                                                : `${pointsRedeemCategory.conversionPointsFrom} Points to RM${pointsRedeemCategory.conversionCurrencyTo}`}
                                        </Text>
                                    </View>
                                ) : (
                                    <></>
                                )}

                                {buy1Free1ItemSkuDict[item.sku] !== undefined ||
                                    buy1Free1Category !== undefined ? (
                                    <View
                                        style={{
                                            marginTop: 5,
                                        }}
                                    >
                                        <Text
                                            style={{
                                                color: Colors.descriptionColor,
                                                fontFamily: "NunitoSans-SemiBold",
                                                fontSize: 12,
                                            }}
                                        >
                                            {`Bundle Deal`}
                                        </Text>
                                    </View>
                                ) : (
                                    <></>
                                )}
                            </View>

                            <View
                                style={{
                                    //marginLeft: 15,
                                    // flexDirection: 'row',
                                    // flexShrink: 1,
                                    width: isMobile() ? "100%" : '65%',
                                    // height: windowWidth * 0.32,
                                    alignItems: 'flex-start',

                                    //paddingLeft: isMobile() ? 10 : 0,
                                    // marginBottom: 10,
                                }}
                            >
                                {/* <View style={{ flex: 1 }}> */}
                                <Text
                                    // numberOfLines={1}
                                    style={[
                                        itemNameTextScale,
                                        {
                                            fontSize: 14,
                                            textTransform: "uppercase",
                                            fontFamily: "NunitoSans-Bold",
                                            textAlign: 'left',
                                            marginBottom: 0,
                                            height: isMobile() ? 40 : 40,
                                        },
                                    ]}
                                    numberOfLines={isMobile() ? 2 : 2}
                                >
                                    {item.name}
                                </Text>

                                <View
                                    style={{
                                        flexDirection: "row",
                                        alignItems: "center",
                                    }}
                                >
                                    <Text
                                        style={{
                                            color: Colors.primaryColor,
                                            fontFamily: "NunitoSans-Bold",
                                            paddingTop: 5,
                                            fontSize: 14,
                                            textDecorationLine:
                                                (!excludePromoVoucher && (overrideItemPriceSkuDict[item.sku] !== undefined ||
                                                    overrideCategoryPrice !== undefined))
                                                    ? "line-through"
                                                    : "none",

                                            ...(((extraPrice + item.price) !== (extraPrice + item.priceProduct) && !(!excludePromoVoucher && (overrideItemPriceSkuDict[item.sku] !== undefined || overrideCategoryPrice !== undefined)))) && {
                                                fontSize: 10,
                                                paddingTop: 3,
                                                textDecorationLine: 'line-through'
                                            },

                                            ...(!excludePromoVoucher && (overrideItemPriceSkuDict[item.sku] !== undefined ||
                                                overrideCategoryPrice !== undefined)) && {
                                                fontSize: 10,
                                                paddingTop: 8,
                                            },
                                        }}
                                    >
                                        RM{parseFloat(extraPrice + item.priceProduct).toFixed(2)}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[item.unitType]}` : ''}
                                    </Text>

                                    {(((extraPrice + item.price) !== (extraPrice + item.priceProduct) && !(!excludePromoVoucher && (overrideItemPriceSkuDict[item.sku] !== undefined || overrideCategoryPrice !== undefined)))) ?
                                        <Text
                                            style={{
                                                color: Colors.secondaryColor,
                                                fontFamily: "NunitoSans-Bold",
                                                paddingTop: 0,
                                                fontSize: 14,
                                                marginLeft: 5,
                                                marginTop: 2,
                                            }}
                                        >
                                            RM{parseFloat(extraPrice + item.price).toFixed(2)}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[item.unitType]}` : ''}
                                        </Text> :
                                        <></>}

                                    {!excludePromoVoucher && (overrideItemPriceSkuDict[item.sku] !== undefined ||
                                        overrideCategoryPrice !== undefined) ? (
                                        <Text
                                            style={{
                                                color: Colors.secondaryColor,
                                                fontFamily: "NunitoSans-Bold",
                                                paddingTop: 5,
                                                fontSize: 14,
                                                marginLeft: 5,

                                            }}
                                        >
                                            RM
                                            {overrideItemPriceSkuDict[item.sku] &&
                                                overrideItemPriceSkuDict[item.sku].overridePrice !==
                                                undefined
                                                ? parseFloat(
                                                    overrideItemPriceSkuDict[item.sku].overridePrice
                                                ).toFixed(2)
                                                : parseFloat(overrideCategoryPrice).toFixed(2)}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[item.unitType]}` : ''}
                                        </Text>
                                    ) : (
                                        <></>
                                    )}
                                </View>
                                {/* </View> */}
                                {/* <View style={{ flex: 1 }}> */}
                                <View
                                    style={{
                                        flexDirection: "row",
                                        alignItems: "center",
                                    }}
                                >
                                    {amountOffItemSkuDict[item.sku] !== undefined ||
                                        amountOffCategory !== undefined ? (
                                        <Text
                                            style={{
                                                color: Colors.descriptionColor,
                                                fontFamily: "NunitoSans-SemiBold",
                                                paddingTop: 5,
                                                fontSize: 14,
                                                // marginLeft: 5,
                                            }}
                                        >
                                            {amountOffItemSkuDict[item.sku] !== undefined
                                                ? `Buy ${amountOffItemSkuDict[item.sku].quantityMin
                                                } pcs to enjoy RM${amountOffItemSkuDict[
                                                    item.sku
                                                ].amountOff.toFixed(2)} off\n(Min purchases: RM${amountOffItemSkuDict[item.sku].priceMin
                                                })`
                                                : `Buy ${amountOffCategory.quantityMin} pcs to enjoy ${amountOffCategory.amountOff.toFixed(
                                                    0
                                                )}% off\n(Min purchases: RM${amountOffCategory.priceMin
                                                })`}
                                        </Text>
                                    ) : (
                                        <></>
                                    )}
                                </View>

                                <View
                                    style={{
                                        flexDirection: "row",
                                        alignItems: "center",
                                    }}
                                >
                                    {percentageOffItemSkuDict[item.sku] !== undefined ||
                                        percentageOffCategory !== undefined ? (
                                        <Text
                                            style={{
                                                color: Colors.descriptionColor,
                                                fontFamily: "NunitoSans-SemiBold",
                                                paddingTop: 5,
                                                fontSize: 14,
                                                // marginLeft: 5,
                                            }}
                                        >
                                            {percentageOffItemSkuDict[item.sku] !== undefined
                                                ? `Buy ${percentageOffItemSkuDict[item.sku].quantityMin
                                                } pcs to enjoy ${percentageOffItemSkuDict[
                                                    item.sku
                                                ].percentageOff.toFixed(0)}% off\n(Min purchases: RM${percentageOffItemSkuDict[item.sku].priceMin
                                                })`
                                                : `Buy ${percentageOffCategory.quantityMin} pcs to enjoy ${percentageOffCategory.percentageOff.toFixed(
                                                    0
                                                )}% off\n(Min purchases: RM${percentageOffCategory.priceMin
                                                })`}
                                        </Text>
                                    ) : (
                                        <></>
                                    )}
                                </View>
                                {/* </View> */}
                            </View>
                        </View>
                    </View>
                </TouchableOpacity >
            );
        }
    };

    const renderRecommendedItemsLarge = ({ item }) => {
        var quantity = 0;
        //const cartItem = cartItem.find(obj => obj.itemId === item.id);

        const itemsInCart = cartItems.filter((obj) => obj.itemId === item.uniqueId);
        if (itemsInCart) {
            for (const obj of itemsInCart) {
                quantity += parseInt(obj.quantity);
            }
        }

        var itemNameFontSize = 15;

        if (windowWidth <= 360) {
            itemNameFontSize = 13;
            //console.log(windowWidth)
        }

        const itemNameTextScale = {
            fontSize: itemNameFontSize,
        };

        let excludePromoVoucher = false;
        let outletCategory = selectedOutletItemCategoriesDict[item.categoryId];
        if (
            (item && item.excludePromoVoucher)
            ||
            (outletCategory && outletCategory.excludePromoVoucher)
        ) {
            excludePromoVoucher = true;
        }

        var overrideCategoryPrice = undefined;

        if (
            !excludePromoVoucher &&
            selectedOutletItemCategoriesDict[item.categoryId] &&
            overrideCategoryPriceNameDict[
            selectedOutletItemCategoriesDict[item.categoryId].name
            ] !== undefined
        ) {
            overrideCategoryPrice =
                overrideCategoryPriceNameDict[
                    selectedOutletItemCategoriesDict[item.categoryId].name
                ].overridePrice;
        }

        if (
            // item.categoryId === selectedOutletItemCategory.uniqueId &&
            (item.hideInOrderTypes && item.hideInOrderTypes.length > 0
                ? item.hideInOrderTypes.includes(orderType)
                    ? false
                    : true
                : true)
        ) {
            var amountOffCategory = undefined;
            if (
                selectedOutletItemCategoriesDict[item.categoryId] &&
                amountOffCategoryNameDict[
                selectedOutletItemCategoriesDict[item.categoryId].name
                ] !== undefined
            ) {
                amountOffCategory =
                    amountOffCategoryNameDict[
                    selectedOutletItemCategoriesDict[item.categoryId].name
                    ];
            }

            var percentageOffCategory = undefined;
            if (
                selectedOutletItemCategoriesDict[item.categoryId] &&
                percentageOffCategoryNameDict[
                selectedOutletItemCategoriesDict[item.categoryId].name
                ] !== undefined
            ) {
                percentageOffCategory =
                    percentageOffCategoryNameDict[
                    selectedOutletItemCategoriesDict[item.categoryId].name
                    ];
            }

            var pointsRedeemCategory = undefined;
            if (
                selectedOutletItemCategoriesDict[item.categoryId] &&
                pointsRedeemCategoryNameDict[
                selectedOutletItemCategoriesDict[item.categoryId].name
                ] !== undefined
            ) {
                pointsRedeemCategory =
                    pointsRedeemCategoryNameDict[
                    selectedOutletItemCategoriesDict[item.categoryId].name
                    ];
            }

            var buy1Free1Category = undefined;
            if (
                selectedOutletItemCategoriesDict[item.categoryId] &&
                buy1Free1CategoryNameDict[
                selectedOutletItemCategoriesDict[item.categoryId].name
                ] !== undefined
            ) {
                buy1Free1Category =
                    buy1Free1CategoryNameDict[
                    selectedOutletItemCategoriesDict[item.categoryId].name
                    ];
            }

            var extraPrice = 0;
            if (
                orderType === ORDER_TYPE.DELIVERY &&
                selectedOutlet &&
                selectedOutlet.deliveryPrice
            ) {
                extraPrice = selectedOutlet.deliveryPrice;
            } else if (
                orderType === ORDER_TYPE.PICKUP &&
                selectedOutlet &&
                selectedOutlet.pickUpPrice
            ) {
                extraPrice = selectedOutlet.pickUpPrice;
            }

            if (orderType === ORDER_TYPE.DELIVERY) {
                extraPrice = item.deliveryCharges || 0;

                if (
                    extraPrice &&
                    item.deliveryChargesType === CHARGES_TYPE.PERCENTAGE_BASED
                ) {
                    extraPrice = (item.price * extraPrice) / 100;
                }

                if (!item.deliveryChargesActive) {
                    extraPrice = 0;
                }
            }

            if (orderType === ORDER_TYPE.PICKUP) {
                extraPrice = item.pickUpCharges || 0;

                if (
                    extraPrice &&
                    item.pickUpChargesType === CHARGES_TYPE.PERCENTAGE_BASED
                ) {
                    extraPrice = (item.price * extraPrice) / 100;
                }

                if (!item.pickUpChargesActive) {
                    extraPrice = 0;
                }
            }

            const isNotAvailable = !item.isActive ||
                !(item.isAvailableDayActive
                    ? (item.effectiveTypeOptions.includes(effectiveDays) &&
                        item.effectiveStartTime && item.effectiveEndTime &&
                        moment().isSameOrAfter(
                            moment(item.effectiveStartTime)
                                .year(moment().year())
                                .month(moment().month())
                                .date(moment().date())
                        )
                        &&
                        moment().isBefore
                            (moment(item.effectiveEndTime)
                                .year(moment().year())
                                .month(moment().month())
                                .date(moment().date())
                            ))
                    : true) ||
                !(item.isOnlineMenu !== undefined
                    ? item.isOnlineMenu
                    : true) ||
                !(item.isStockCountActive !== undefined &&
                    item.isStockCountActive !== false &&
                    item.stockCount !== undefined &&
                    item.toSellIgnoreStock !== undefined
                    ? item.isStockCountActive &&
                    item.stockCount > 0 &&
                    (item.toSellIgnoreStock !== undefined
                        ? item.toSellIgnoreStock
                        : true)
                    : true);

            let specialTag = '';
            if (item.specialTags && item.specialTags.length > 0 &&
                typeof item.specialTags[0] === 'string') {
                specialTag = item.specialTags[0];
            }

            return (
                <TouchableOpacity
                    onPress={async () => {
                        logEvent(global.analytics, ANALYTICS.WO_UPSELL_ITEM_CLICK, {
                            eventNameParsed: ANALYTICS_PARSED.WO_UPSELL_ITEM_CLICK,

                            outletName: global.outletName ? `${global.outletName} (Web)` : '',

                            webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
                        });

                        if (checkCartOutlet()) {
                            // setState({ cartWarning: true, })

                            setTempItem(item);

                            setCartWarning(true);
                        } else {
                            if (
                                item.isActive &&
                                (item.isAvailableDayActive
                                    ? (item.effectiveTypeOptions.includes(effectiveDays) &&
                                        item.effectiveStartTime && item.effectiveEndTime &&
                                        moment().isSameOrAfter(
                                            moment(item.effectiveStartTime)
                                                .year(moment().year())
                                                .month(moment().month())
                                                .date(moment().date())
                                        )
                                        &&
                                        moment().isBefore
                                            (moment(item.effectiveEndTime)
                                                .year(moment().year())
                                                .month(moment().month())
                                                .date(moment().date())
                                            ))
                                    : true) &&
                                (item.isOnlineMenu !== undefined ? item.isOnlineMenu : true) &&
                                (item.isStockCountActive !== undefined &&
                                    item.isStockCountActive !== false &&
                                    item.stockCount !== undefined &&
                                    item.toSellIgnoreStock !== undefined
                                    ? item.isStockCountActive &&
                                    item.stockCount > 0 &&
                                    (item.toSellIgnoreStock !== undefined
                                        ? item.toSellIgnoreStock
                                        : true)
                                    : true)
                            ) {
                                // var priceUpselling = item.price;
                                // if (currUpsellingCampaign && currUpsellingCampaign.uniqueId) {
                                //     for (var upsellingIndex = 0; upsellingIndex < currUpsellingCampaign.productList.length; upsellingIndex++) {
                                //         if (currUpsellingCampaign.productList[upsellingIndex].productId === item.uniqueId) {

                                //         }
                                //     }
                                // }

                                CommonStore.update((s) => {
                                    s.selectedOutletItem = item;

                                    // s.selectedAddOnIdForChoiceQtyDict = {};
                                });

                                // const subdomain = await AsyncStorage.getItem("latestSubdomain");

                                // if (!subdomain) {
                                //   linkTo && linkTo(`${prefix}/outlet/menu/item`);
                                // } else {
                                //   linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu/item`);
                                // }

                                // linkTo && linkTo(`${prefix}/outlet/menu/item`);

                                global.isFromRecommendedItems = true;

                                global.currUpsellingCampaign = currUpsellingCampaign;

                                global.currUpsellingSection = props.upsellingSection;

                                if (isMobile() && selectedOutlet && true) {
                                    CommonStore.update((s) => {
                                        s.menuItemDetailModal = true;
                                    });

                                    global.menuItemDetailModal = true;

                                    window.history.pushState({
                                        page: 'menuItemDetailModal',
                                    }, '');

                                    CommonStore.update(s => {
                                        s.currPageIframe = 'MenuItemDetails';
                                    });

                                    // window.history.pushState(null, '', window.location.href);
                                    // window.history.forward();

                                    // const subdomain = await AsyncStorage.getItem("latestSubdomain");

                                    // if (!subdomain) {
                                    //     global.linkToFunc && global.linkToFunc(`${prefix}/outlet/menu/item`);
                                    // } else {
                                    //     global.linkToFunc && global.linkToFunc(`${prefix}/outlet/${subdomain}/menu/item`);
                                    // }

                                    // props.navigation.navigate(
                                    //     "Product Details - KooDoo Web Order",
                                    //     {
                                    //         refresh: refresh.bind(this),
                                    //         menuItem: item,
                                    //         outletData: selectedOutlet,
                                    //     }
                                    // );
                                }
                                else {
                                    props.navigation.navigate(
                                        "Product Details - KooDoo Web Order",
                                        {
                                            refresh: refresh.bind(this),
                                            menuItem: item,
                                            outletData: selectedOutlet,
                                        }
                                    );
                                }
                            } else {
                                // window.confirm(
                                //   'Info',
                                //   'Sorry, this product is not available for now.',
                                // );

                                CommonStore.update((s) => {
                                    s.alertObj = {
                                        title: "Info",
                                        message: "Sorry, this product is not available for now.",
                                    };
                                });
                            }
                        }
                    }}
                >
                    <View
                        style={{
                            flexDirection: "column",
                            //paddingHorizontal: 20,
                            paddingBottom: 15,
                            //paddingTop: 10,
                            display: "flex",
                            alignContent: "center",
                            //flexDirection: "row",
                            //borderWidth: 0.5,
                            //borderColor: '#E5E5E5',
                            //borderRadius: 10,
                            //marginHorizontal: 3,
                            //height: 250,
                            width: isMobile() ? windowWidth * 0.85 : windowWidth * 0.17,
                            alignSelf: 'center',
                            alignItems: 'center',

                            // shadowColor: '#000',
                            // shadowOffset: {
                            //     width: 0,
                            //     height: 3,
                            // },
                            // shadowOpacity: 0.29,
                            // shadowRadius: 4.65,
                            // //shadowRadius:10,

                            // elevation: 7,

                            //backgroundColor: 'blue'
                            //marginBottom: -10,
                        }}
                    >
                        <View
                            style={{
                                flexDirection: "column",
                                alignContent: "center",
                                alignItems: "center",
                                width: '100%',
                                display: "flex",
                                justifyContent: "flex-start",
                                //backgroundColor: 'blue',

                            }}
                        >
                            <View style={{ marginBottom: 15 }}>
                                <View
                                    style={[
                                        {
                                            // backgroundColor: Colors.secondaryColor,
                                            // width: 60,
                                            // height: 60,
                                            width: isMobile()
                                                ? windowWidth * 0.89
                                                : windowWidth * 0.05,

                                            height: Dimensions.get('window').height * 0.2,
                                        },
                                    ]}
                                >
                                    {item.image ? (
                                        <AsyncImage
                                            source={{ uri: item.image }}
                                            item={item}
                                            style={{
                                                width: windowWidth * 0.89,
                                                height: Dimensions.get('window').height * 0.2,

                                                // borderTopLeftRadius: 10,
                                                // borderTopRightRadius: 10,
                                                marginTop: 10,

                                                alignItems: "center",
                                                justifyContent: "center",

                                                marginBottom: 15,
                                            }}
                                        />
                                    ) : (
                                        // <Ionicons name="fast-food-outline" size={50} />
                                        <View
                                            style={{
                                                width: windowWidth * 0.89,
                                                height: Dimensions.get('window').height * 0.2,
                                                display: "flex",
                                                justifyContent: "center",
                                                alignItems: "center",
                                                backgroundColor: Colors.secondaryColor,
                                                marginTop: 10,

                                                // borderTopLeftRadius: 10,
                                                // borderTopRightRadius: 10,

                                                alignItems: "center",
                                                justifyContent: "center",

                                                marginBottom: 15,
                                            }}
                                        >
                                            <Ionicons
                                                name="fast-food-outline"
                                                // size={45}
                                                size={
                                                    isMobile() ? windowWidth * 0.1 : windowWidth * 0.02
                                                }
                                            />
                                        </View>
                                    )}

                                    {(isNotAvailable || specialTag) ? (
                                        <View
                                            style={{
                                                position: "absolute",
                                                zIndex: 3,
                                            }}
                                        >
                                            <View
                                                style={{
                                                    // width: 120,
                                                    width: isMobile()
                                                        ? windowWidth * 0.33
                                                        : windowWidth * 0.06,
                                                    left: isMobile()
                                                        ? -windowWidth * 0.03
                                                        : -windowWidth * 0.01,
                                                    padding: 0,
                                                    paddingLeft: isMobile()
                                                        ? windowWidth * 0.02
                                                        : windowWidth * 0.005,
                                                    justifyContent: "center",
                                                    alignItems: "center",
                                                    backgroundColor: isNotAvailable ? Colors.tabRed : Colors.primaryColor,
                                                    height: 20,
                                                    borderTopRightRadius: 2,
                                                    borderBottomRightRadius: 2,

                                                    ...(!item.image && {
                                                        left: isMobile()
                                                            ? -windowWidth * 0.015
                                                            : -windowWidth * 0.005,
                                                        bottom: isMobile()
                                                            ? windowWidth * 0.074
                                                            : windowWidth * 0.014,
                                                    }),
                                                }}
                                            >
                                                <Text
                                                    style={{
                                                        color: "#FFF",
                                                        fontFamily: "NunitoSans-Bold",
                                                        fontSize: 10,
                                                        bottom: 1,
                                                    }}
                                                >
                                                    {isNotAvailable ? 'Not available' : specialTag}
                                                </Text>
                                            </View>
                                            {/* <View
                                                style={{
                                                    left: isMobile()
                                                        ? -windowWidth * 0.029
                                                        : -windowWidth * 0.01,
                                                    bottom: "1%",
                                                    width: 0,
                                                    height: 0,
                                                    backgroundColor: "transparent",
                                                    borderStyle: "solid",
                                                    borderRightWidth: isMobile()
                                                        ? windowWidth * 0.03
                                                        : windowWidth * 0.01,
                                                    borderTopWidth: isMobile()
                                                        ? windowWidth * 0.03
                                                        : windowWidth * 0.01,
                                                    borderRightColor: "transparent",
                                                    borderTopColor: "red",
                                                    transform: [{ rotate: "90deg" }],

                                                    // ...(!item.image && {
                                                    //     left: isMobile()
                                                    //         ? -windowWidth * 0.0135
                                                    //         : -windowWidth * 0.005,
                                                    //     bottom: isMobile()
                                                    //         ? windowWidth * 0.074
                                                    //         : windowWidth * 0.014,
                                                    // }),
                                                }}
                                            /> */}
                                        </View>
                                    ) : (
                                        <></>
                                    )}

                                </View>

                                {/* {pointsRedeemItemSkuDict[item.sku] !== undefined ||
                                    pointsRedeemCategory !== undefined ? (
                                    <View
                                        style={{
                                            marginTop: 5,
                                        }}
                                    >
                                        <Text
                                            style={{
                                                color: Colors.primaryColor,
                                                fontFamily: "NunitoSans-SemiBold",
                                                fontSize: 12,
                                                textAlign: "center",
                                            }}
                                        >
                                            {pointsRedeemItemSkuDict[item.sku] !== undefined
                                                ? `${pointsRedeemItemSkuDict[item.sku]
                                                    .conversionPointsFrom
                                                } Points to RM${pointsRedeemItemSkuDict[item.sku]
                                                    .conversionCurrencyTo
                                                }`
                                                : `${pointsRedeemCategory.conversionPointsFrom} Points to RM${pointsRedeemCategory.conversionCurrencyTo}`}
                                        </Text>
                                    </View>
                                ) : (
                                    <></>
                                )} */}

                                {/* {buy1Free1ItemSkuDict[item.sku] !== undefined ||
                                    buy1Free1Category !== undefined ? (
                                    <View
                                        style={{
                                            marginTop: 5,
                                        }}
                                    >
                                        <Text
                                            style={{
                                                color: Colors.descriptionColor,
                                                fontFamily: "NunitoSans-SemiBold",
                                                fontSize: 12,
                                                textAlign: "center",
                                            }}
                                        >
                                            {`Bundle Deal`}
                                            {/* {buy1Free1ItemSkuDict[item.sku] !== undefined
                            ? `Buy ${
                                buy1Free1ItemSkuDict[item.sku].buyAmount
                              } Free ${buy1Free1ItemSkuDict[item.sku].getAmount}`
                            : `Buy ${buy1Free1Category.buyAmount} Free ${buy1Free1Category.getAmount}`} 
                                        </Text>
                                    </View>
                                ) : (
                                    <></>
                                )} */}
                            </View>

                            <View
                                style={{
                                    marginLeft: -20,
                                    //marginTop: -25,
                                    // flexDirection: 'row',
                                    // flexShrink: 1,
                                    width: isMobile() ? "100%" : '65%',
                                    //height: 100,
                                    //alignItems: 'flex-start',
                                    //backgroundColor: 'red',

                                    paddingLeft: isMobile() ? 1 : 0,
                                }}
                            >

                                <Text
                                    // numberOfLines={1}
                                    style={[
                                        itemNameTextScale,
                                        {
                                            fontSize: 14,
                                            textTransform: "uppercase",
                                            fontFamily: "NunitoSans-Bold",
                                            textAlign: 'left',
                                            //marginBottom: 10,
                                            marginTop: 5,
                                            //height: isMobile() ? 60 : 40,
                                            marginLeft: 10,
                                        },
                                    ]}
                                    numberOfLines={isMobile() ? 3 : 2}
                                >
                                    {item.name}
                                </Text>

                                <Text
                                    // numberOfLines={1}
                                    style={[
                                        itemNameTextScale,
                                        {
                                            fontSize: 14,
                                            //textTransform: "uppercase",
                                            fontFamily: "NunitoSans-SemiBold",
                                            textAlign: 'left',
                                            marginBottom: 5,
                                            //marginTop: -10,
                                            marginLeft: 10,
                                            //height: isMobile() ? 60 : 40,
                                        },
                                    ]}
                                    numberOfLines={isMobile() ? 3 : 2}
                                >
                                    {item.description}
                                </Text>

                                <View
                                    style={{
                                        flexDirection: "row",
                                        alignItems: "center",
                                        marginLeft: 10,
                                        marginTop: -5,
                                    }}
                                >
                                    <Text
                                        style={{
                                            color: Colors.primaryColor,
                                            fontFamily: "NunitoSans-Bold",
                                            paddingTop: 0,
                                            fontSize: 14,
                                            textDecorationLine:
                                                (!excludePromoVoucher && (overrideItemPriceSkuDict[item.sku] !== undefined ||
                                                    overrideCategoryPrice !== undefined))
                                                    ? "line-through"
                                                    : "none",

                                            ...(((extraPrice + item.price) !== (extraPrice + item.priceProduct) && !(!excludePromoVoucher && (overrideItemPriceSkuDict[item.sku] !== undefined || overrideCategoryPrice !== undefined)))) && {
                                                fontSize: 10,
                                                paddingTop: 3,
                                                textDecorationLine: 'line-through'
                                            },

                                            ...(!excludePromoVoucher && (overrideItemPriceSkuDict[item.sku] !== undefined ||
                                                overrideCategoryPrice !== undefined)) && {
                                                fontSize: 10,
                                                paddingTop: 3,
                                            },
                                        }}
                                    >
                                        RM{parseFloat(extraPrice + item.priceProduct).toFixed(2)}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[item.unitType]}` : ''}
                                    </Text>

                                    {(((extraPrice + item.price) !== (extraPrice + item.priceProduct) && !(!excludePromoVoucher && (overrideItemPriceSkuDict[item.sku] !== undefined || overrideCategoryPrice !== undefined)))) ?
                                        <Text
                                            style={{
                                                color: Colors.secondaryColor,
                                                fontFamily: "NunitoSans-Bold",
                                                paddingTop: 0,
                                                fontSize: 14,
                                                marginLeft: 5,
                                            }}
                                        >
                                            RM{parseFloat(extraPrice + item.price).toFixed(2)}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[item.unitType]}` : ''}
                                        </Text> :
                                        <></>}

                                    {!excludePromoVoucher && (overrideItemPriceSkuDict[item.sku] !== undefined ||
                                        overrideCategoryPrice !== undefined) ? (
                                        <Text
                                            style={{
                                                color: Colors.secondaryColor,
                                                fontFamily: "NunitoSans-Bold",
                                                //paddingTop: 5,
                                                fontSize: 14,
                                                //marginLeft: 5,
                                            }}
                                        >
                                            RM
                                            {overrideItemPriceSkuDict[item.sku] &&
                                                overrideItemPriceSkuDict[item.sku].overridePrice !==
                                                undefined
                                                ? parseFloat(
                                                    overrideItemPriceSkuDict[item.sku].overridePrice
                                                ).toFixed(2)
                                                : parseFloat(overrideCategoryPrice).toFixed(2)}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[item.unitType]}` : ''}
                                        </Text>
                                    ) : (
                                        <></>
                                    )}
                                </View>

                                {/* <View
                                    style={{
                                        flexDirection: "row",
                                        alignItems: "center",
                                    }}
                                >
                                    <Text
                                        style={{
                                            color: Colors.primaryColor,
                                            fontFamily: "NunitoSans-Bold",
                                            paddingTop: 5,
                                            fontSize: 16,
                                        }}
                                    >
                                        100 reviews
                                    </Text>

                                    {!excludePromoVoucher && (overrideItemPriceSkuDict[item.sku] !== undefined ||
                                        overrideCategoryPrice !== undefined) ? (
                                        <Text
                                            style={{
                                                color: Colors.secondaryColor,
                                                fontFamily: "NunitoSans-Bold",
                                                paddingTop: 5,
                                                fontSize: 16,
                                                marginLeft: 5,
                                            }}
                                        >
                                            RM
                                            {overrideItemPriceSkuDict[item.sku] &&
                                                overrideItemPriceSkuDict[item.sku].overridePrice !==
                                                undefined
                                                ? parseFloat(
                                                    overrideItemPriceSkuDict[item.sku].overridePrice
                                                ).toFixed(2)
                                                : parseFloat(overrideCategoryPrice).toFixed(2)}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[item.unitType]}` : ''}
                                        </Text>
                                    ) : (
                                        <></>
                                    )}
                                </View> */}

                                {/* <View
                                    style={{
                                        flexDirection: "row",
                                        alignItems: "center",
                                    }}
                                >
                                    {amountOffItemSkuDict[item.sku] !== undefined ||
                                        amountOffCategory !== undefined ? (
                                        <Text
                                            style={{
                                                color: Colors.descriptionColor,
                                                fontFamily: "NunitoSans-SemiBold",
                                                paddingTop: 5,
                                                fontSize: 14,
                                                // marginLeft: 5,
                                            }}
                                        >
                                            {amountOffItemSkuDict[item.sku] !== undefined
                                                ? `Buy ${amountOffItemSkuDict[item.sku].quantityMin
                                                } ~ ${amountOffItemSkuDict[item.sku].quantityMax
                                                } pcs to enjoy RM${amountOffItemSkuDict[
                                                    item.sku
                                                ].amountOff.toFixed(2)} off\n(Min purchases: RM${amountOffItemSkuDict[item.sku].priceMin
                                                })`
                                                : `Buy ${amountOffCategory.quantityMin} ~ ${amountOffCategory.quantityMax
                                                } pcs to enjoy ${amountOffCategory.amountOff.toFixed(
                                                    0
                                                )}% off\n(Min purchases: RM${amountOffCategory.priceMin
                                                })`}
                                        </Text>
                                    ) : (
                                        <></>
                                    )}
                                </View> */}

                                {/* <View
                                    style={{
                                        flexDirection: "row",
                                        alignItems: "center",
                                    }}
                                >
                                    {percentageOffItemSkuDict[item.sku] !== undefined ||
                                        percentageOffCategory !== undefined ? (
                                        <Text
                                            style={{
                                                color: Colors.descriptionColor,
                                                fontFamily: "NunitoSans-SemiBold",
                                                paddingTop: 5,
                                                fontSize: 14,
                                                // marginLeft: 5,
                                            }}
                                        >
                                            {percentageOffItemSkuDict[item.sku] !== undefined
                                                ? `Buy ${percentageOffItemSkuDict[item.sku].quantityMin
                                                } ~ ${percentageOffItemSkuDict[item.sku].quantityMax
                                                } pcs to enjoy ${percentageOffItemSkuDict[
                                                    item.sku
                                                ].percentageOff.toFixed(0)}% off\n(Min purchases: RM${percentageOffItemSkuDict[item.sku].priceMin
                                                })`
                                                : `Buy ${percentageOffCategory.quantityMin} ~ ${percentageOffCategory.quantityMax
                                                } pcs to enjoy ${percentageOffCategory.percentageOff.toFixed(
                                                    0
                                                )}% off\n(Min purchases: RM${percentageOffCategory.priceMin
                                                })`}
                                        </Text>
                                    ) : (
                                        <></>
                                    )}
                                </View> */}
                            </View>
                        </View>

                        {/* <View
                            style={{
                                flexDirection: "row",
                                // width: "20%",
                                // marginLeft: 60
                                // backgroundColor: 'red',
                                ...(!isMobile() && {
                                    // width: windowWidth,
                                    height: 26,
                                    // right: 0,
                                    alignSelf: "center",
                                }),
                            }}
                        >
                            <View
                                style={{
                                    backgroundColor: "#e3e1e1",
                                    // width: 67,
                                    // height: 24,

                                    width: 68,
                                    height: 26,

                                    // paddingVertical: 4,
                                    // paddingHorizontal: 20,

                                    borderRadius: 10,
                                    justifyContent: "center",
                                    alignSelf: "center",
                                }}
                            >
                                <TouchableOpacity
                                    onPress={async () => {
                                        if (checkCartOutlet()) {
                                            // setState({ cartWarning: true, })

                                            setTempItem(item);

                                            setCartWarning(true);
                                        } else {
                                            if (
                                                item.isActive &&
                                                (item.isAvailableDayActive
                                                    ? (item.effectiveTypeOptions.includes(effectiveDays) &&
                                                        item.effectiveStartTime && item.effectiveEndTime &&
                                                        moment().isSameOrAfter(
                                                            moment(item.effectiveStartTime)
                                                                .year(moment().year())
                                                                .month(moment().month())
                                                                .date(moment().date())
                                                        )
                                                        &&
                                                        moment().isBefore
                                                            (moment(item.effectiveEndTime)
                                                                .year(moment().year())
                                                                .month(moment().month())
                                                                .date(moment().date())
                                                            ))
                                                    : true) &&
                                                (item.isOnlineMenu !== undefined
                                                    ? item.isOnlineMenu
                                                    : true) &&
                                                (item.isStockCountActive !== undefined &&
                                                    item.isStockCountActive !== false &&
                                                    item.stockCount !== undefined &&
                                                    item.toSellIgnoreStock !== undefined
                                                    ? item.isStockCountActive &&
                                                    item.stockCount > 0 &&
                                                    (item.toSellIgnoreStock !== undefined
                                                        ? item.toSellIgnoreStock
                                                        : true)
                                                    : true)
                                            ) {
                                                CommonStore.update((s) => {
                                                    s.selectedOutletItem = item;
                                                });

                                                // const subdomain = await AsyncStorage.getItem("latestSubdomain");

                                                // if (!subdomain) {
                                                //   linkTo && linkTo(`${prefix}/outlet/menu/item`);
                                                // } else {
                                                //   linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu/item`);
                                                // }

                                                // linkTo && linkTo(`${prefix}/outlet/menu/item`);

                                                props.navigation.navigate(
                                                    "Product Details - KooDoo Web Order",
                                                    {
                                                        refresh: refresh.bind(this),
                                                        menuItem: item,
                                                        outletData: selectedOutlet,
                                                    }
                                                );
                                            } else {
                                                // window.confirm(
                                                //   'Info',
                                                //   'Sorry, this product is not available for now.',
                                                // );

                                                CommonStore.update((s) => {
                                                    s.alertObj = {
                                                        title: "Info",
                                                        message:
                                                            "Sorry, this product is not available for now.",
                                                    };
                                                });
                                            }
                                        }
                                    }}
                                >
                                    <Text
                                        style={{
                                            alignSelf: "center",
                                            color: "#8f8f8f",
                                            fontSize: 13,
                                            fontFamily: "NunitoSans-Bold",
                                        }}
                                    >
                                        {quantity > 0 ? quantity : "Add"}
                                    </Text>
                                </TouchableOpacity>
                            </View>
                        </View> */}
                    </View>
                </TouchableOpacity >
            );
        }
    };

    return (
        <>
            {
                toRecommendedItems.length > 0 ?
                    <>
                        {
                            (
                                props.upsellingSection === undefined ||
                                props.upsellingSection === UPSELLING_SECTION.AFTER_CART ||
                                props.upsellingSection === UPSELLING_SECTION.AFTER_CHECKOUT ||
                                props.upsellingSection === UPSELLING_SECTION.IN_CART
                            )
                                ?
                                selectedOutlet.webOrderUpsellingLayout === WEB_ORDER_UPSELLING_LAYOUT.NORMAL || selectedOutlet.webOrderUpsellingLayout === undefined ?
                                    <View style={{
                                        width: '100%', flexWrap: 'wrap', paddingHorizontal: 0, justifyContent: 'center', alignItems: 'center',
                                        // backgroundColor: 'red',
                                    }}>
                                        <View style={{
                                            display: 'flex',
                                            flexDirection: 'column',
                                            alignItems: 'center',
                                            justifyContent: 'center',

                                            // backgroundColor: 'red',
                                            width: windowWidth * 0.85,
                                        }}>
                                            <Text style={{
                                                fontSize: 20,
                                                fontFamily: "NunitoSans-Bold",
                                                marginTop: props.campaignIndex === 0 ? 0 : 15,
                                                textAlign: 'left',
                                                // alignItems: 'center',
                                                // justifyContent: 'center',
                                                // backgroundColor: 'blue',
                                                width: windowWidth * 0.85,
                                                // alignSelf: 'center',                                            

                                                marginBottom: 5,

                                                // paddingLeft: 2,
                                            }}>
                                                {(props.campaignData && props.campaignData.campaignName) ? props.campaignData.campaignName : `Don't miss out on these items!`}
                                            </Text>

                                            <ScrollView horizontal={false} showsHorizontalScrollIndicator={false} style={{ width: '100%', }}>
                                                <FlatList
                                                    numColumns={2}
                                                    horizontal={false}
                                                    data={toRecommendedItems.slice().sort((a, b) => {
                                                        return (a.orderIndex ? a.orderIndex : toRecommendedItems.length) -
                                                            (b.orderIndex ? b.orderIndex : toRecommendedItems.length)
                                                    })}
                                                    renderItem={renderRecommendedItems}
                                                    keyExtractor={(item, index) => index}
                                                    contentContainerStyle={{
                                                        // paddingLeft: 10,
                                                        // paddingRight: 10,

                                                        //paddingBottom: 70,
                                                        // backgroundColor: 'red',
                                                    }}
                                                />
                                            </ScrollView>
                                        </View>
                                        <View
                                            style={{
                                                height: 1,
                                                width: "100%",
                                                backgroundColor: "#C2C1C0",
                                                opacity: 0.2,
                                                marginBottom: 4,

                                                shadowColor: "#000",
                                                shadowOffset: {
                                                    width: 0,
                                                    height: 1,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 2.22,
                                                elevation: 3,
                                            }}
                                        ></View>
                                    </View>
                                    :
                                    <View style={{
                                        width: '100%', flexWrap: 'wrap', paddingHorizontal: 0, justifyContent: 'center', alignItems: 'center',
                                        // backgroundColor: 'red',
                                    }}>
                                        <View style={{
                                            display: 'flex',
                                            flexDirection: 'column',
                                            alignItems: 'center',
                                            justifyContent: 'center',

                                            //backgroundColor: 'red',
                                            width: windowWidth * 0.85,
                                        }}>
                                            <Text style={{
                                                fontSize: 20,
                                                fontFamily: "NunitoSans-Bold",
                                                marginTop: props.campaignIndex === 0 ? 0 : 15,
                                                textAlign: 'left',
                                                // alignItems: 'center',
                                                // justifyContent: 'center',
                                                // backgroundColor: 'blue',
                                                width: windowWidth * 0.85,
                                                // alignSelf: 'center',                                            

                                                marginBottom: 5,

                                                // paddingLeft: 2,
                                            }}>
                                                {(props.campaignData && props.campaignData.campaignName) ? props.campaignData.campaignName : `Don't miss out on these items!`}
                                            </Text>

                                            <ScrollView showsVerticalScrollIndicator={false} style={{ width: windowWidth * 0.85, paddingBottom: 20 }}>
                                                <FlatList
                                                    numColumns={2}
                                                    data={toRecommendedItems.slice().sort((a, b) => {
                                                        return (a.orderIndex ? a.orderIndex : toRecommendedItems.length) -
                                                            (b.orderIndex ? b.orderIndex : toRecommendedItems.length)
                                                    })}
                                                    renderItem={renderUpsellingLarge}
                                                    keyExtractor={(item, index) => index}
                                                    contentContainerStyle={{
                                                        // paddingLeft: 10,
                                                        // paddingRight: 10,

                                                        //paddingBottom: 70,
                                                        width: windowWidth * 0.85,
                                                    }}
                                                />
                                            </ScrollView>
                                        </View>
                                        <View
                                            style={{
                                                height: 1,
                                                width: "100%",
                                                backgroundColor: "#C2C1C0",
                                                opacity: 0.2,
                                                marginBottom: 4,

                                                shadowColor: "#000",
                                                shadowOffset: {
                                                    width: 0,
                                                    height: 1,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 2.22,
                                                elevation: 3,
                                            }}
                                        ></View>
                                    </View>
                                :
                                // for recommendations
                                props.campaignData === undefined ?
                                    <></>
                                    :
                                    (
                                        <View style={{
                                            width: '100%', flexWrap: 'wrap', paddingHorizontal: 0, justifyContent: 'center', alignItems: 'center',
                                            // backgroundColor: 'red',
                                        }}>
                                            <View style={{
                                                display: 'flex',
                                                flexDirection: 'column',
                                                alignItems: 'center',
                                                justifyContent: 'center',

                                                // backgroundColor: 'red',
                                                width: windowWidth * 0.85,
                                            }}>
                                                <Text style={{
                                                    fontSize: 20,
                                                    fontFamily: "NunitoSans-Bold",
                                                    marginTop: 15,
                                                    textAlign: 'left',
                                                    // alignItems: 'center',
                                                    // justifyContent: 'center',
                                                    // backgroundColor: 'blue',
                                                    width: '100%',
                                                    // alignSelf: 'center',

                                                    marginBottom: 5,
                                                    // marginRight: 12,
                                                }}>
                                                    {(props.campaignData && props.campaignData.campaignName) ? props.campaignData.campaignName : `Don't miss out on these items!`}
                                                </Text>

                                                <ScrollView showsVerticalScrollIndicator={false}>
                                                    <FlatList
                                                        data={toRecommendedItems.slice().sort((a, b) => {
                                                            return (a.orderIndex ? a.orderIndex : toRecommendedItems.length) -
                                                                (b.orderIndex ? b.orderIndex : toRecommendedItems.length)
                                                        })}
                                                        //data={dummyItems}
                                                        renderItem={renderRecommendedItemsLarge}
                                                        keyExtractor={(item, index) => index}
                                                        contentContainerStyle={{
                                                            // paddingLeft: 10,
                                                            // paddingRight: 10,

                                                            paddingBottom: 30,
                                                            // backgroundColor: 'red',
                                                        }}
                                                    />
                                                </ScrollView>
                                            </View>
                                            <View
                                                style={{
                                                    height: 1,
                                                    width: "100%",
                                                    backgroundColor: "#C2C1C0",
                                                    opacity: 0.2,
                                                    marginBottom: 4,

                                                    shadowColor: "#000",
                                                    shadowOffset: {
                                                        width: 0,
                                                        height: 1,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 2.22,
                                                    elevation: 3,
                                                }}
                                            ></View>
                                        </View>
                                    )
                        }
                    </>
                    : <></>
            }
        </>
    )
}

export default RecommendedItems;
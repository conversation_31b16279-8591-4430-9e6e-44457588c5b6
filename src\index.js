import { AppRegistry } from "react-native";
import * as Sen<PERSON> from "@sentry/browser";
import App from "./App";
import { TempStore } from "./store/tempStore";

Sentry.init({
  // glitchtip
  dsn : process.env.NODE_ENV === 'production' ? 'https://<EMAIL>/6288' : undefined,
  
  // sentry
  // dsn : process.env.NODE_ENV === 'production' ? 'https://<EMAIL>/4507229827891200' : undefined,
  
  // dsn : 'https://<EMAIL>/6288',
});

if (process.env.NODE_ENV === 'PROD') {
  console.log = () => { }
  console.error = () => { }
  console.debug = () => { }
}

AppRegistry.registerComponent("App", () => App);

AppRegistry.runApplication("App", {
  rootTag: document.getElementById("root")
});

if ('serviceWorker' in navigator) {
  navigator.serviceWorker
    .register(`${process.env.PUBLIC_URL}/firebase-messaging-sw.js`)
    .then(function (registration) {
      console.log('Service Worker registered with scope:', registration.scope);
      global.newsw = registration;

      TempStore.update(s => {
        s.newsw = registration;
      });
    })
    .catch(function (error) {
      console.error('Service Worker registration failed:', error);
    });
}

import React, { useState, useEffect } from 'react';
import {
    View,
    Image,
    ActivityIndicator,
    Text,
    Modal,
    useWindowDimensions,
    TouchableOpacity,
    TextInput,
    Dimensions,
    StyleSheet,
    ScrollView,
    Platform,
    Linking
} from 'react-native';
import Colors from '../../constant/Colors';
import { getCachedUrlContent, getImageFromFirebaseStorage, getWhatsAppLink, isMobile, signInWithPhoneForCRMUser, updateWebTokenAnonymous } from '../../util/commonFuncs';
import AntDesign from "react-native-vector-icons/AntDesign";
import { UserStore } from '../../store/userStore';
import { TempStore } from '../../store/tempStore';
// import firebase from 'firebase';
import { getFirestore, collection, query, where, getDocs, limit } from "firebase/firestore";
import { Collections } from '../../constant/firebase';
import { CommonStore } from '../../store/commonStore';
import ApiClient from '../../util/ApiClient';
import API from '../../constant/API';
import moment from 'moment';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { GR_STATUS, ORDER_TYPE } from '../../constant/common';
import { apiUrl, prefix } from '../../constant/env';
import FontAwesome from "react-native-vector-icons/FontAwesome";

import Toastify from 'toastify-js';
import "toastify-js/src/toastify.css";
import AsyncImage from '../asyncImage';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Entypo from "react-native-vector-icons/Entypo";
import html2canvas from 'html2canvas';

import Checkbox from 'rc-checkbox';
import DropDownPicker from 'react-native-dropdown-picker';
import { saveAs } from 'file-saver';
import Marquee from "react-fast-marquee";

import secureLocalStorage from "react-secure-storage";
import { PaymentStore } from '../../store/paymentStore';
import { CheckBox } from "react-native-web";
import { EI_USER_FIELDS } from '../../constant/einvoice';

const VOUCHER_PROMOTION_INFO_PAGE = {
    REGISTER_USER: 'REGISTER_USER',
    CART_INFO: 'CART_INFO',
};

const onBeforeUnload = () => {
    if (global.createUserOrderBody) {
        // means still existed, help to create order

        var createUserOrderBodyLocal = {
            ...global.createUserOrderBody,
        };

        global.createUserOrderBody = null;

        // ApiClient.POST(API.createUserOrder, createUserOrderBodyLocal, {
        //     timeout: 100000,
        // }).then(async (result) => {
        //     if (createUserOrderBodyLocal.orderType === ORDER_TYPE.PICKUP) {
        //         global.takeawayOrders.push(result);
        //     }
        // });        

        // const headers = {
        //     type: 'application/json',
        //     'Authorization': `Bearer ${global.accessToken}`,            
        // };
        // const blob = new Blob([JSON.stringify(createUserOrderBodyLocal)], headers);
        // navigator.sendBeacon(apiUrl + API.createUserOrderBeacon, blob);

        ////////////////////////////////////////////////////////////////////////

        // fetch(apiUrl + API.createUserOrderBeacon, {
        //     method: 'POST', // *GET, POST, PUT, DELETE, etc.
        //     mode: 'cors', // no-cors, *cors, same-origin
        //     cache: 'no-cache', // *default, no-cache, reload, force-cache, only-if-cached
        //     // credentials: 'same-origin', // include, *same-origin, omit
        //     headers: {
        //         'Content-Type': 'application/json',
        //         'Authorization': `Bearer ${global.accessToken}`,            
        //     },
        //     redirect: 'follow', // manual, *follow, error
        //     referrerPolicy: 'no-referrer', // no-referrer, *no-referrer-when-downgrade, origin, origin-when-cross-origin, same-origin, strict-origin, strict-origin-when-cross-origin, unsafe-url
        //     body: JSON.stringify(createUserOrderBodyLocal) // body data type must match "Content-Type" header
        // });

        ////////////////////////////////////////////////////////////////////////

        ApiClient.POST(API.createUserOrderBeacon, createUserOrderBodyLocal, {
            // timeout: 100000,
        }).then(async (result) => {
            if (createUserOrderBodyLocal.orderType === ORDER_TYPE.PICKUP) {
                global.takeawayOrders.push(result);
            }
        });

        ///////////////////////////////////////////        

        // ApiClient.POST(API.deleteUserCart, global.deleteUserCartBody || null).then((result) => {
        //     if (result && result.status === "success") {
        //         console.log("ok");
        //     }
        // });

        global.deleteUserCartBody = null;

        ////////////////////////////////////////////////////////////////////////
    }
};

window.addEventListener('beforeUnload', onBeforeUnload);
window.addEventListener('beforeunload', onBeforeUnload);
window.addEventListener('unload', onBeforeUnload);
// window.addEventListener('visibilitychange', onBeforeUnload);
// window.addEventListener('pagehide', onBeforeUnload);

var cooldownTimerTime = 10; // 10

const VoucherPromotionInfo = props => {
    const {
        linkToFunc,
    } = props;

    const {
        width: windowWidth,
        height: windowHeight,
    } = useWindowDimensions();

    const [showVoucherPromotionInfoModal, setShowVoucherPromotionInfoModal] = useState(false);

    const [currVoucherPromotionInfoPage, setCurrVoucherPromotionInfoPage] = useState(VOUCHER_PROMOTION_INFO_PAGE.REGISTER_USER);

    const [verifySMSModal, setVerifySMSModal] = useState(false);
    const [verificationCode, setVerificationCode] = useState('');
    const [isVerified, setIsVerified] = useState(false); // herks test

    const [name, setName] = useState('');
    const [phone, setPhone] = useState('');
    const [rating, setRating] = useState(5);
    const [review, setReview] = useState('A very good enjoyable experience.');
    const [birthday, setBirthday] = useState(moment(Date.now()));
    const [address, setAddress] = useState('');
    const [lat, setLat] = useState(0);
    const [lng, setLng] = useState(0);

    const [sentCode, setSentCode] = useState(true);
    const [existingCustomer, setExistingCustomer] = useState(false); //For testing UI purpose

    const [outletId, setOutletId] = useState('');
    const [outletName, setOutletName] = useState('');
    const [outletCover, setOutletCover] = useState('');
    const [merchantId, setMerchantId] = useState('');
    const [merchantName, setMerchantName] = useState('');

    // const registerUserOrder = CommonStore.useState(s => s.registerUserOrder);
    const [registerUserOrder, setRegisterUserOrder] = useState({});

    const [cooldownTimer, setCooldownTimer] = useState(null);
    const [cooldownTimerTimeState, setCooldownTimerTimeState] = useState(cooldownTimerTime);
    const [cooldownActive, setCooldownActive] = useState(true);

    const [submittedGoogleReview, setSubmittedGoogleReview] = useState(false);
    const [showGRIframe, setShowGRIframe] = useState(false);
    const [GRStatus, setGRStatus] = useState(GR_STATUS.PENDING);

    const isLoading = CommonStore.useState(s => s.isLoading);

    const selectedOutlet = CommonStore.useState(s => s.selectedOutlet);
    const selectedMerchant = CommonStore.useState(s => s.selectedMerchant);

    const showVoucherInfo = TempStore.useState(s => s.showVoucherInfo);
    const showVoucherPromotionInterestedInfo = TempStore.useState(s => s.showVoucherPromotionInterestedInfo);
    const showNamePhoneFieldsForRegister = TempStore.useState(s => s.showNamePhoneFieldsForRegister);
    const isPaidFirstOrder = TempStore.useState(s => s.isPaidFirstOrder);

    const cartItems = TempStore.useState(s => s.cartItemsT);
    const cartItemsProcessed = TempStore.useState(s => s.cartItemsProcessedT);
    const cartOutletId = TempStore.useState(s => s.cartOutletIdT);

    const userEmail = UserStore.useState((s) => s.email);
    const userName = UserStore.useState((s) => s.name);
    const userNumber = UserStore.useState((s) => s.number);

    const gReview = UserStore.useState((s) => s.gReview);

    const userIdAnonymous = UserStore.useState(s => s.userIdAnonymous);

    const claimVoucherAnonymous = TempStore.useState(s => s.claimVoucherAnonymous);
    const [isTNCExpand, setIsTNCExpand] = useState(false);
    const [voucherPopupFullScreen, setVoucherPopupFullScreen] = useState(false);

    const epStateTo = UserStore.useState((s) => s.epStateTo);
    const epNameTo = UserStore.useState((s) => s.epNameTo);
    const epPhoneTo = UserStore.useState((s) => s.epPhoneTo);
    const epAddr1To = UserStore.useState((s) => s.epAddr1To);
    const epCityTo = UserStore.useState((s) => s.epCityTo);
    const epCodeTo = UserStore.useState((s) => s.epCodeTo);
    const epIdTypeTo = UserStore.useState((s) => s.epIdTypeTo);
    const epIdTo = UserStore.useState((s) => s.epIdTo);
    const epTinTo = UserStore.useState((s) => s.epTinTo);
    const epEmailTo = UserStore.useState((s) => s.epEmailTo);
    const crmUserId = UserStore.useState((s) => s.crmUserId);

    const [eInvoiceInfo, setEInvoiceInfo] = useState(false);
    const [epStateToTemp, setEpStateToTemp] = useState('sgr');
    const [epNameToTemp, setEpNameToTemp] = useState('');
    const [epPhoneToTemp, setEpPhoneToTemp] = useState('');
    const [epAddr1ToTemp, setEpAddr1ToTemp] = useState('');
    const [epCityToTemp, setEpCityToTemp] = useState('');
    const [epCodeToTemp, setEpCodeToTemp] = useState(''); // postcode
    const [epIdTypeToTemp, setEpIdTypeToTemp] = useState('NRIC');
    const [epIdToTemp, setEpIdToTemp] = useState('');
    const [epTinToTemp, setEpTinToTemp] = useState('');
    const [epEmailToTemp, setEpEmailToTemp] = useState('');
    const [openProvince, setOpenProvince] = useState(false);
    const [openIdType, setOpenIdType] = useState(false);
    const toUpdateEPDetails = useState(false);
    const emailEInvoice = CommonStore.useState((s) => s.emailEInvoice);
    const tickboxConsent = TempStore.useState(s => s.tickboxConsent);


    const idtypeOption = [
        {
            label: 'IC',
            value: 'NRIC',
        },
        {
            label: 'Passport',
            value: 'PASSPORT',
        },
        {
            label: 'MyTentera',
            value: 'ARMY',
        },
    ];

    const provinceOption = [
        {
            label: 'Johor',
            value: 'jhr',
        },
        {
            label: 'Kedah',
            value: 'kdh',
        },
        {
            label: 'Kelantan',
            value: 'ktn',
        },
        {
            label: 'Melaka',
            value: 'mlk',
        },
        {
            label: 'Negeri Sembilan',
            value: 'nsn',
        },
        {
            label: 'Pahang',
            value: 'phg',
        },
        {
            label: 'Perak',
            value: 'prk',
        },
        {
            label: 'Perlis',
            value: 'pls',
        },
        {
            label: 'Pulau Pinang',
            value: 'png',
        },
        {
            label: 'Selangor',
            value: 'sgr',
        },
        {
            label: 'Terengganu',
            value: 'trg',
        },
        {
            label: 'Kuala Lumpur',
            value: 'kul',
        },
        {
            label: 'Putra Jaya',
            value: 'pjy',
        },
        {
            label: 'Sarawak',
            value: 'srw',
        },
        {
            label: 'Sabah',
            value: 'sbh',
        },
        {
            label: 'Labuan',
            value: 'lbn',
        },
    ];

    // const userCart = CommonStore.useState((s) => s.userCart);

    // useEffect(() => {
    //     return () => {
    //         if (global.createUserOrderBody) {
    //             var createUserOrderBodyLocal = {
    //                 ...global.createUserOrderBody,
    //             };

    //             global.createUserOrderBody = null;

    //             ApiClient.POST(API.createUserOrderBeacon, createUserOrderBodyLocal, {
    //                 timeout: 100000,
    //             }).then(async (result) => {
    //                 if (createUserOrderBodyLocal.orderType === ORDER_TYPE.PICKUP) {
    //                     global.takeawayOrders.push(result);
    //                 }
    //             });

    //             ///////////////////////////////////////////        

    //             ApiClient.POST(API.deleteUserCart, global.deleteUserCartBody || null).then((result) => {
    //                 if (result && result.status === "success") {
    //                     console.log("ok");
    //                 }
    //             });

    //             global.deleteUserCartBody = null;
    //         }
    //     };
    // }, []);

    /////////////////////////////////////////

    // 2023-12-05 - for google review stuff

    // useEffect(() => {
    //     if (showGRIframe && GRStatus === GR_STATUS.PENDING &&
    //         selectedOutlet && selectedOutlet.placeUrl) {
    //         // setTimeout(() => {
    //         //     global.intervalGR = setInterval(() => {
    //         //         // var pageGoogleSearch = document.querySelectorAll('[style="font-family: Verdana, sans; cursor: pointer; font-size: 16px; text-anchor: start; font-weight: normal;"]');

    //         //         const idGR = document.querySelector("#idGR");

    //         //         console.log(idGR);
    //         //         console.log(idGR.document);

    //         //         // Find the 'a' element
    //         //         const aElement = idGR.document.querySelector('a[data-index="2"][role="tab"]');

    //         //         // Check if the 'a' element exists
    //         //         if (aElement) {
    //         //             // Check if the third child has the attribute 'data-vt'
    //         //             const childElement = aElement.querySelector(':third-child[data-vt="1"]');

    //         //             // Check if the child has a <span> element with the text 'Reviews'
    //         //             const spanElement = childElement.querySelector('span');

    //         //             if (spanElement && spanElement.textContent.trim() === 'Reviews') {
    //         //                 console.log('Found the desired element:', aElement);
    //         //             } else {
    //         //                 console.log('No matching element found (1)');
    //         //             }
    //         //         } else {
    //         //             console.log('No matching element found (2)');
    //         //         }
    //         //     }, 100);
    //         // }, 5000);
    //     }
    //     else {
    //         if (global.intervalGR !== null) {
    //             clearInterval(global.intervalGR);
    //         }
    //     }
    // }, [GRStatus, showGRIframe, selectedOutlet]);

    /////////////////////////////////////////

    // 2024-08-20 - hide first
    // useEffect(() => {
    //     if (showNamePhoneFieldsForRegister === false || emailEInvoice === true) {
    //         setEInvoiceInfo(true);
    //     } else {
    //         setEInvoiceInfo(false);
    //     }
    // }, [showNamePhoneFieldsForRegister, emailEInvoice])

    useEffect(() => {
        if (userName && userNumber) {
            setName(userName);
            setPhone(userNumber);
            setIsVerified(true);
        }
    }, [userName, userNumber]);

    useEffect(() => {
        if (epStateTo) {
            setEpStateToTemp(epStateTo);
        }

        if (epNameTo) {
            setEpNameToTemp(epNameTo);
        }
        else if (userName) {
            setEpNameToTemp(userName);
        }

        if (epPhoneTo) {
            setEpPhoneToTemp(epPhoneTo);
        }
        else if (userNumber) {
            setEpPhoneToTemp(userNumber);
        }

        if (epAddr1To) {
            setEpAddr1ToTemp(epAddr1To);
        }

        if (epCityTo) {
            setEpCityToTemp(epCityTo);
        }

        if (epCodeTo) {
            setEpCodeToTemp(epCodeTo);
        }

        if (epIdTypeTo) {
            setEpIdTypeToTemp(epIdTypeTo);
        }

        if (epIdTo) {
            setEpIdToTemp(epIdTo);
        }

        if (epTinTo) {
            setEpTinToTemp(epTinTo);
        }

        if (epEmailTo) {
            setEpEmailToTemp(epEmailTo);
        }
    }, [
        epStateTo,
        epNameTo,
        epPhoneTo,
        epAddr1To,
        epCityTo,
        epCodeTo,
        epIdTypeTo,
        epIdTo,
        epTinTo,
        epEmailTo,

        userName,
        userNumber,
    ]);

    ////////////////////////////////

    // 2024-06-21 - to read e-invoice related data from local

    useEffect(() => {
        if (showVoucherPromotionInfoModal) {
            const commonUserData = secureLocalStorage.getItem('commonUserData');

            if (commonUserData) {
                if (commonUserData.epStateTo) {
                    setEpStateToTemp(commonUserData.epStateTo);
                }

                if (commonUserData.epNameTo) {
                    setEpNameToTemp(commonUserData.epNameTo);
                }

                if (commonUserData.epPhoneTo) {
                    setEpPhoneToTemp(commonUserData.epPhoneTo);
                }

                if (commonUserData.epAddr1To) {
                    setEpAddr1ToTemp(commonUserData.epAddr1To);
                }

                if (commonUserData.epCityTo) {
                    setEpCityToTemp(commonUserData.epCityTo);
                }

                if (commonUserData.epCodeTo) {
                    setEpCodeToTemp(commonUserData.epCodeTo);
                }

                if (commonUserData.epIdTypeTo) {
                    setEpIdTypeToTemp(commonUserData.epIdTypeTo);
                }

                if (commonUserData.epIdTo) {
                    setEpIdToTemp(commonUserData.epIdTo);
                }

                if (commonUserData.epTinTo) {
                    setEpTinToTemp(commonUserData.epTinTo);
                }

                if (commonUserData.epEmailTo) {
                    setEpEmailToTemp(commonUserData.epEmailTo);
                }
            }
        }
    }, [showVoucherPromotionInfoModal]);

    ////////////////////////////////

    useEffect(() => {
        if (selectedOutlet && selectedOutlet.uniqueId && selectedMerchant && selectedMerchant.uniqueId) {
            setOutletId(selectedOutlet.uniqueId);
            setOutletName(selectedOutlet.name);
            setOutletCover(selectedOutlet.cover);
            setMerchantId(selectedOutlet.merchantId);
            setMerchantName(selectedMerchant.name);
        }
    }, [selectedOutlet, selectedMerchant]);

    useEffect(() => {
        if (showVoucherInfo || showVoucherPromotionInterestedInfo) {
            setShowVoucherPromotionInfoModal(true);

            var cooldownTimerTemp = setInterval(() => {
                // setCooldownTimerTime(cooldownTimerTime - 1);
                cooldownTimerTime -= 1;

                setCooldownTimerTimeState(cooldownTimerTime);
            }, 1000);

            setCooldownTimer(cooldownTimerTemp);
        }
        else {
            setShowVoucherPromotionInfoModal(false);
        }

        return () => {
            clearInterval(cooldownTimer);
            cooldownTimerTime = 10;
            setCooldownTimerTimeState(cooldownTimerTime);
            setCooldownActive(true);
        }
    }, [
        showVoucherInfo,
        showVoucherPromotionInterestedInfo,
    ]);

    useEffect(async () => {
        if (cooldownTimerTimeState <= 0 && cooldownActive) {
            // just place the order

            if (global.createUserOrderBody) {
                // means still existed, help to create order

                var createUserOrderBodyLocal = {
                    ...global.createUserOrderBody,
                };

                global.createUserOrderBody = null;

                ApiClient.POST(API.createUserOrder, createUserOrderBodyLocal, {
                    timeout: 100000,
                }).then(async (result) => {
                    if (createUserOrderBodyLocal.orderType === ORDER_TYPE.PICKUP) {
                        global.takeawayOrders.push(result);
                    }

                    CommonStore.update(s => {
                        // 2022-10-08 - Clear cart items
                        s.cartItems = [];
                        s.cartItemsProcessed = [];

                        s.selectedOutletItem = {};
                        s.selectedOutletItemAddOn = {};
                        s.selectedOutletItemAddOnChoice = {};
                        s.onUpdatingCartItem = null;
                    });

                    // 2024-12-12 - clear cart items
                    PaymentStore.update(s => {
                        s.cartItemsPayment = [];
                        s.outletIdPayment = '';
                        s.dateTimePayment = moment().valueOf();
                        s.orderTypePayment = '';
                    });

                    const latestSubdomain = await AsyncStorage.getItem(
                        "latestSubdomain"
                    );

                    if (latestSubdomain) {
                        if (createUserOrderBodyLocal.orderType === ORDER_TYPE.DINEIN) {
                            linkToFunc &&
                                linkToFunc(`${prefix}/outlet/${latestSubdomain}/order-history`);
                        }
                        else if (createUserOrderBodyLocal.orderType === ORDER_TYPE.PICKUP) {
                            linkToFunc &&
                                linkToFunc(`${prefix}/outlet/${latestSubdomain}/order-history-t`);
                        }
                    }
                });

                TempStore.update(s => {
                    s.showVoucherInfo = null;
                    s.showVoucherPromotionInterestedInfo = false;
                    s.isPaidFirstOrder = false;

                    s.claimableVoucher = null;

                    s.showNamePhoneFieldsForRegister = true;
                });

                clearInterval(cooldownTimer);
                cooldownTimerTime = 10;
                setCooldownTimerTimeState(cooldownTimerTime);
                setCooldownActive(true);

                // await deleteUserCart();

                CommonStore.update(s => {
                    s.timestampOutletCategory = Date.now();
                });
            }

            TempStore.update(s => {
                s.showVoucherInfo = null;
                s.showVoucherPromotionInterestedInfo = false;
                s.isPaidFirstOrder = false;

                s.cartItemsT = [];
                s.cartItemsProcessedT = [];

                s.cartOutletIdT = (selectedOutlet && selectedOutlet.uniqueId) ? selectedOutlet.uniqueId : null;

                s.claimableVoucher = null;

                s.showNamePhoneFieldsForRegister = true;
            });

            setShowVoucherPromotionInfoModal(false);
            setCurrVoucherPromotionInfoPage(VOUCHER_PROMOTION_INFO_PAGE.REGISTER_USER);

            clearInterval(cooldownTimer);
            cooldownTimerTime = 10;
            setCooldownTimerTimeState(cooldownTimerTime);
            setCooldownActive(true);
        }
    }, [cooldownTimerTimeState, cooldownActive]);

    const deleteUserCart = async () => {
        ApiClient.POST(API.deleteUserCart, global.deleteUserCartBody || null).then((result) => {
            if (result && result.status === "success") {
                console.log("ok");
            }
        });

        global.deleteUserCartBody = null;
    };

    console.log('=====================test=====================');
    console.log(name);
    console.log(phone);
    console.log('=====================test=====================');

    // send a verification code to user phone number
    const sendVerifyOTP = async () => {
        var userPhone = phone.replaceAll('-', '');

        if (!userPhone) {
            setVerifySMSModal(false);
            // CommonStore.update((s) => {
            //     s.alertObj = {
            //         title: "Error",
            //         message: "Please key in your contact.",
            //     };
            // });
            window.confirm(
                "Please key in your phone number."
            );
            return;
        }

        if (userPhone && /\D/.test(userPhone)) {
            setVerifySMSModal(false);
            // CommonStore.update((s) => {
            //     s.alertObj = {
            //         title: "Error",
            //         message: "Only digits allowed for contact.",
            //     };
            // });
            window.confirm(
                "Only digits allowed for phone number."
            );
            return;
        }

        if (userPhone && !(userPhone.length === 10 || userPhone.length === 11)) {
            if (
                (userPhone.startsWith('011') && userPhone.length === 11)
                ||
                (userPhone.startsWith('6011') && userPhone.length === 12)
            ) {
                // still valid, do nothing
            }
            else {
                setVerifySMSModal(false);
                // CommonStore.update((s) => {
                //     s.alertObj = {
                //         title: "Error",
                //         message: "Invalid contact format.\neg: 60123456789.",
                //     };
                // });
                window.confirm(
                    "Invalid phone number format.\neg: 60123456789."
                );
                return;
            }
        }

        if (!userPhone.startsWith('6')) {
            userPhone = '6' + userPhone;
        }

        const data = {
            phoneNumber: userPhone,
        };

        const response = await ApiClient.POST(API.sendOTPVerificationToSMSWeb, data);

        if (response) {
            // alert(`Success: Code has been sent to \n ${data.phoneNumber} !`);
            // CommonStore.update((s) => {
            //     s.alertObj = {
            //         title: "Success",
            //         message: `Code has been sent to \n ${data.phoneNumber} !`,
            //     };
            // });
            window.confirm(
                `Code has been sent to ${data.phoneNumber}.`
            );
        } else {
            setVerifySMSModal(false);
            // CommonStore.update((s) => {
            //     s.alertObj = {
            //         title: "Failed",
            //         message: `Code failed to send to \n ${data.phoneNumber} !`,
            //     };
            // });
            window.confirm(
                `Please wait for 3 minutes before send again.`
            );
        }
    }

    // check the verification code user inputted
    const verifyRegisterOTP = async (state) => {
        if (!verificationCode || verificationCode.length !== 6) {
            window.confirm(
                `Empty or invalid code, please try again.`
            );
            return;
        }

        var userPhone = phone.replaceAll('-', '');

        if (!userPhone.startsWith('6')) {
            userPhone = '6' + userPhone;
        }

        // const OTPSnapshot = await firebase.firestore().collection(Collections.OTPCache)
        //     .where('phoneNumber', '==', userPhone)
        //     .limit(1)
        //     .get();

        const OTPSnapshot = await getDocs(
            query(
                collection(global.db, Collections.OTPCache),
                where('phoneNumber', '==', userPhone),
                limit(1),
            )
        );

        var OTP = null;
        if (!OTPSnapshot.empty) {
            OTP = OTPSnapshot.docs[0].data();
        } else {
            setVerifySMSModal(false);
            // CommonStore.update((s) => {
            //     s.alertObj = {
            //         title: "Invalid code",
            //         message: `Please try again.`,
            //     };
            // });
            window.confirm(
                `Invalid code, please try again.`
            );
            return
        }

        if (OTP.code == verificationCode) {
            setIsVerified(true);
            setVerifySMSModal(false);
            // CommonStore.update((s) => {
            //     s.alertObj = {
            //         title: "Verification Success",
            //         message: `Please proceed to register !`,
            //     };
            // });
            window.confirm(
                `Verified successfully.`
            );
        }
    }

    const registerUser = async () => {
        // 2023-05-06 - No need verify first
        // if (!isVerified) {
        //     // CommonStore.update((s) => {
        //     //     s.alertObj = {
        //     //         title: "Error",
        //     //         message: "Please verify your phone number first.",
        //     //     };
        //     // });
        //     window.confirm(
        //         `Please verify your phone number first.`
        //     );
        //     return;
        // }

        var userPhone = phone.replaceAll('-', '').replaceAll(' ', '').replaceAll('+', '');

        // if (userPhone.startsWith('6')) {
        //     userPhone = userPhone.slice(1);
        // }

        if (!userPhone.startsWith('6')) {
            userPhone = '6' + userPhone;
        }

        if (userPhone && name) {

        }
        else {
            window.confirm('Please fill in the name and phone number before proceed.');

            return;
        }

        if (userPhone && !(userPhone.length === 10 || userPhone.length === 11)) {
            if (
                (userPhone.startsWith('011') && userPhone.length === 11)
                ||
                (userPhone.startsWith('6011') && userPhone.length === 12)
            ) {
                // still valid, do nothing
            }
            else {
                window.confirm(
                    "Invalid phone number format.\neg: 60123456789."
                );
                return;
            }
        }

        let epPhoneToTempParsed = epPhoneToTemp;
        if (!epPhoneToTempParsed.startsWith('6')) {
            epPhoneToTempParsed = `6${epPhoneToTempParsed}`;
        }

        let epPhoneToParsed = epPhoneTo;
        if (!epPhoneToTempParsed.startsWith('6')) {
            epPhoneToParsed = `6${epPhoneToParsed}`;
        }

        let toUpdateEiDetails = false;
        if (
            epStateToTemp !== epStateTo ||
            epNameToTemp !== epNameTo ||
            // epPhoneToTempParsed !== epPhoneToParsed ||
            userPhone !== epPhoneToParsed ||
            epAddr1ToTemp !== epAddr1To ||
            epCityToTemp !== epCityTo ||
            epCodeToTemp !== epCodeTo ||
            epIdTypeToTemp !== epIdTypeTo ||
            epIdToTemp !== epIdTo ||
            epTinToTemp !== epTinTo ||
            epEmailToTemp !== epEmailTo
        ) {
            toUpdateEiDetails = true;
        }

        if (eInvoiceInfo) {
            // if (
            //     false
            //     // epEmailToTemp === '' ||
            //     // epIdToTemp === '' ||
            //     // epTinToTemp === '' ||
            //     // epAddr1ToTemp === '' ||
            //     // epCityToTemp === '' ||
            //     // epStateToTemp === '' ||
            //     // epCodeToTemp === ''
            // ) {
            //     alert(
            //         // 'Error, Please fill in all required information:\nName\nContact number\nID\nTIN\nStreet\nCity\nState\nPost code',
            //         'Error, Please fill in all required information:\nID',
            //         [{ text: 'OK', onPress: () => { } }],
            //         { cancelable: false },
            //     );
            //     return;
            // }

            let missingFields = ``;

            if (
                (
                    selectedOutlet.eiAskFields === undefined
                    ||
                    (
                        selectedOutlet.eiAskFields.length > 0
                        &&
                        selectedOutlet.eiAskFields.includes(EI_USER_FIELDS.EMAIL)
                    )
                )
                &&
                epEmailToTemp === ''
            ) {
                missingFields += `Email\n`;
            }

            if (
                (
                    selectedOutlet.eiAskFields === undefined
                    ||
                    (
                        selectedOutlet.eiAskFields.length > 0
                        &&
                        selectedOutlet.eiAskFields.includes(EI_USER_FIELDS.ID)
                    )
                )
                &&
                epIdToTemp === ''
            ) {
                missingFields += `ID\n`;
            }

            if (
                (
                    selectedOutlet.eiAskFields === undefined
                    ||
                    (
                        selectedOutlet.eiAskFields.length > 0
                        &&
                        selectedOutlet.eiAskFields.includes(EI_USER_FIELDS.TIN)
                    )
                )
                &&
                epTinToTemp === ''
            ) {
                missingFields += `TIN\n`;
            }

            if (
                (
                    selectedOutlet.eiAskFields === undefined
                    ||
                    (
                        selectedOutlet.eiAskFields.length > 0
                        &&
                        selectedOutlet.eiAskFields.includes(EI_USER_FIELDS.ADDRESS)
                    )
                )
                &&
                (
                    epAddr1ToTemp === ''
                    ||
                    epCityToTemp === ''
                    ||
                    epStateToTemp === ''
                    ||
                    epCodeToTemp === ''
                )
            ) {
                missingFields += `Street\nCity\nState\nPost code\n`;
            }

            if (missingFields.length > 0) {
                alert(
                    `Error, Please fill in all required information:\n${missingFields}`,
                    [{ text: 'OK', onPress: () => { } }],
                    { cancelable: false },
                );
                return;
            }

            // if (
            //     epEmailToTemp === ''
            // ) {
            //     alert(
            //         // 'Error, Please fill in all required information:\nName\nContact number\nID\nTIN\nStreet\nCity\nState\nPost code',
            //         'Error, Please fill in all required information:\nID',
            //         [{ text: 'OK', onPress: () => { } }],
            //         { cancelable: false },
            //     );
            //     return;
            // }
        }

        // if (showVoucherInfo && showVoucherInfo.voucherQuantity > 0) {

        // }
        // else {
        //     window.confirm('This voucher is already fully redeemed.');

        //     return;
        // }

        // var userPhone = phone.replaceAll('-', '');

        // if (userPhone.startsWith('6')) {
        //     userPhone = userPhone.slice(1);
        // }

        //////////////////////////////////////////////////

        // 2023-12-04 - to check for google submission flow

        let isValidToProceed = true;
        let toOpenGoogleReviewTabAfterClosed = false;
        let toUpdateGR = false;

        if (!submittedGoogleReview && isMobile()) {
            // means this user haven't proceed to submit a google review

            if (!gReview && selectedOutlet && selectedOutlet.placeUrl && (
                rating > 0
                ||
                review.length > 0 // if filled in something already, force the user to fill all
            )) {
                // means this crm user havent submitted google review before (after saved to db)

                if (rating <= 0) {
                    window.confirm(
                        "The minimum rating is 1 star."
                    );
                    return;
                }
                else if (review.length < 10) {
                    window.confirm(
                        "The minimum length of the review is 10 characters."
                    );
                    return;
                }

                // all ok, open the link

                // isValidToProceed = false; // set to false, so that don't proceed to registered the actual operation first

                // setShowGRIframe(true);

                if (rating >= 4) {
                    // 4 stars and above

                    toOpenGoogleReviewTabAfterClosed = true;

                    navigator.clipboard.writeText(review);

                    Toastify({
                        text: `Review copied to clipboard!`,
                        duration: 3000,
                        // destination: "https://github.com/apvarun/toastify-js",
                        newWindow: true,
                        close: false,
                        gravity: "top", // `top` or `bottom`
                        position: "right", // `left`, `center` or `right`
                        stopOnFocus: true, // Prevents dismissing of toast on hover
                        style: {
                            background: "linear-gradient(to right, #4E9F7D, #75bd9f)",
                            color: 'white',

                            // marginLeft: '15px !important',
                            // marginRight: '15px !important',
                        },
                        onClick: function () { } // Callback after click
                    }).showToast();
                }

                toUpdateGR = true;
            }
            else {
                // this user already done in the past
            }
        }

        //////////////////////////////////////////////////

        if (
            isValidToProceed
            // false
        ) {
            const body = {
                userPhone: userPhone,
                outletId: outletId,
                merchantId: merchantId,
                merchantName: merchantName,
                outletName: registerUserOrder.outletName || outletName,
                merchantLogo: registerUserOrder.merchantLogo || '',
                outletCover: registerUserOrder.outletCover || '',
                userName: name,

                dob: moment(birthday).valueOf(),

                address: address,
                lat: lat,
                lng: lng,

                // userOrderId: (registerUserOrder.uniqueId && !registerUserOrder.isCashbackClaimed) ? registerUserOrder.uniqueId : null,
                taggableVoucherId: (showVoucherInfo && showVoucherInfo.uniqueId) ? showVoucherInfo.uniqueId : null,

                isVerified: isVerified,

                userIdAnonymous: userIdAnonymous,
                toConvertAnonymousPoints: true,

                //////////////////////

                // google review

                rating: rating,
                review: review,
                toUpdateGR: toUpdateGR,
                outletEmail: selectedOutlet.email,

                //////////////////////

                // 2024-02-29 - loyalty voucher support

                loyaltyCampaignId: (showVoucherInfo && showVoucherInfo.loyaltyCampaignId) ? (showVoucherInfo.loyaltyCampaignId) : '',
                batchId: (showVoucherInfo && showVoucherInfo.batchId) ? (showVoucherInfo.batchId) : '',
                batchIndex: (showVoucherInfo && showVoucherInfo.batchIndex) ? (showVoucherInfo.batchIndex) : '',

                //////////////////////

                // 2024-06-18 e-invoice
                toUpdateEiDetails: toUpdateEiDetails ? toUpdateEiDetails : false,
                epNameTo: name ? name : epNameTo,
                // epPhoneTo: epPhoneToTemp ? epPhoneToTemp : epPhoneTo,
                epPhoneTo: userPhone,
                epAddr1To: epAddr1ToTemp ? epAddr1ToTemp : epAddr1To,
                epCityTo: epCityToTemp ? epCityToTemp : epCityTo,
                epCodeTo: epCodeToTemp ? epCodeToTemp : epCodeTo,
                epStateTo: epStateToTemp ? epStateToTemp : epStateTo,
                emailSecond: epEmailToTemp ? epEmailToTemp : epEmailTo,
                tin: epTinToTemp ? epTinToTemp : epTinTo,
                eiIdType: epIdTypeToTemp ? epIdTypeToTemp : epIdTypeTo,
                eiId: epIdToTemp ? epIdToTemp : epIdTo,

                tracking: '7',
            };

            CommonStore.update(s => {
                s.isLoading = true;
            });

            // const userSnapshot = await firebase.firestore()
            //     .collection(Collections.User)
            //     // .where('uniqueName', '==', uniqueName)
            //     .where('number', '==', userPhone)
            //     .limit(1)
            //     .get();

            // var findUser = null;
            // if (!userSnapshot.empty) {
            //     findUser = userSnapshot.docs[0].data();
            // }

            if (
                // !findUser
                true
            ) {
                // let token = await AsyncStorage.getItem('accessToken');
                // if (!token) {
                //     token = global.accessToken;
                // }

                // fetch(`${apiUrl + API.claimTaggableVoucherKweb}`, {
                //     method: "POST",
                //     body: JSON.stringify(body),
                //     headers: {
                //         "Content-type": "application/json; charset=UTF-8",
                //         'Authorization': `Bearer ${token}`,
                //     },
                // }).then(async (result) => {
                ApiClient.POST(API.claimTaggableVoucherKweb, body).then(async (result) => {
                    ////////////////////////////////////////////////////////////////////////////////

                    setTimeout(async () => {
                        // write in the user name and phone first

                        await AsyncStorage.setItem('storedUserName', name);
                        await AsyncStorage.setItem('storedUserPhone', userPhone);

                        global.storedUserName = name;
                        global.storedUserPhone = userPhone;

                        await signInWithPhoneForCRMUser(selectedOutlet);
                    }, 100);

                    ////////////////////////////////////////////////////////////////////////////////

                    if (global.voucherScreenshotUrl && global.voucherScreenshotName) {
                        saveAs(global.voucherScreenshotUrl, global.voucherScreenshotName);
                    }

                    ////////////////////////////////////////////////////////////////////////////////

                    // 2024-03-05 - screenshot voucher, while waiting for api to completed

                    // try {
                    //     const screenshotContainer = document.getElementById('screenshotContainer');

                    //     if (screenshotContainer) {
                    //         // Use html2canvas to capture the container element
                    //         html2canvas(screenshotContainer, {
                    //             width: Platform.OS === 'ios' ? (windowWidth + windowWidth * 0.3) : windowWidth,
                    //             height: Platform.OS === 'ios' ? (windowHeight + windowHeight * 0.3) : windowHeight,
                    //             windowWidth: Platform.OS === 'ios' ? (windowWidth + windowWidth * 0.3) : windowWidth,
                    //             windowHeight: Platform.OS === 'ios' ? (windowHeight + windowHeight * 0.3) : windowHeight,
                    //             x: Platform.OS === 'ios' ? (windowWidth * 0.15) : 0,
                    //             y: Platform.OS === 'ios' ? (windowHeight * 0.15) : 0,
                    //             scrollY: windowHeight * 0.3,
                    //             scrollX: 0,
                    //             allowTaint: true,
                    //             letterRendering: 1,
                    //             useCORS: true,
                    //         }).then(canvas => {
                    //             // Convert the canvas to a data URI
                    //             // const screenshotUrl = canvas.toDataURL('image/png').replace("image/png", "image/octet-stream");
                    //             const screenshotUrl = canvas.toDataURL('image/png');

                    //             // Create a temporary link element
                    //             const link = document.createElement('a');
                    //             link.target = '_blank';
                    //             link.href = screenshotUrl;
                    //             link.download = `${selectedOutlet.name}-${showVoucherInfo.campaignName}-${moment(showVoucherInfo.promoDateStart).format('DD MMM YY')}-${moment(showVoucherInfo.promoDateEnd).format('DD MMM YY')}.png`;

                    //             // Trigger the download
                    //             document.body.appendChild(link);
                    //             link.click();

                    //             // Clean up
                    //             document.body.removeChild(link);
                    //         });
                    //     }
                    // }
                    // catch (ex) {
                    //     console.error(ex);
                    // }

                    ////////////////////////////////////////////////////////////////////////////////

                    if (result && result.status === "success") {
                        // here can start proceed to next page, to show cart modal page (if applicable)                    

                        // CommonStore.update(s => {
                        //     s.registerUserOrder = result.userOrder || {};
                        // });

                        // CommonStore.update((s) => {
                        //     s.alertObj = {
                        //         title: "Success",
                        //         message: "Account creation success! Please check your phone for the password.",
                        //     };                        

                        //     // s.isAuthenticating = false;
                        // });  

                        if (showVoucherInfo) {
                            // window.confirm(
                            //     `${result.message}`
                            // );

                            CommonStore.update((s) => {
                                s.alertObj = {
                                    title: "Info",
                                    message: `${result.message}`,
                                };
                            });
                        }
                        else if (showVoucherPromotionInterestedInfo) {
                            // window.confirm(
                            //     `Subscribed successfully! We will update you if there are any upcoming promotions or vouchers.`
                            // );

                            CommonStore.update((s) => {
                                s.alertObj = {
                                    title: "Info",
                                    message: `Subscribed successfully! We will update you if there are any upcoming promotions or vouchers.`,
                                };
                            });
                        }

                        ///////////////////////////////////////////////////////////

                        var userCrm = result.userCrm;
                        var userActual = result.userActual;
                        var userTaggableVoucher = result.data;
                        // var taggableVoucher = result.taggableVoucher;

                        ///////////////////////////////////////////////////////////

                        // determine the next flow:
                        // a. check if is voucher modal, and if claimed, if not claimed?
                        // b. check if is promo interested modal, can just 

                        ///////////////////////////////////////////////////////////

                        updateWebTokenAnonymous();

                        ///////////////////////////////////////////////////////////

                        if (showVoucherInfo) {
                            if (userCrm && userActual && userTaggableVoucher) {
                                // can proceed to next page (cart info)

                                TempStore.update(s => {
                                    s.userCrm = userCrm;
                                    s.userActual = userActual;
                                    s.userTaggableVoucher = userTaggableVoucher;
                                });

                                setCurrVoucherPromotionInfoPage(VOUCHER_PROMOTION_INFO_PAGE.CART_INFO);

                                // now should showing a spinner ex: checking if is applicable voucher...
                            }
                            else {
                                TempStore.update(s => {
                                    s.showVoucherInfo = null;
                                    s.showVoucherPromotionInterestedInfo = false;
                                    s.isPaidFirstOrder = false;

                                    s.cartItemsT = [];
                                    s.cartItemsProcessedT = [];

                                    s.cartOutletIdT = (selectedOutlet && selectedOutlet.uniqueId) ? selectedOutlet.uniqueId : null;

                                    s.claimableVoucher = null;

                                    s.showNamePhoneFieldsForRegister = true;
                                });

                                setShowVoucherPromotionInfoModal(false);
                                setCurrVoucherPromotionInfoPage(VOUCHER_PROMOTION_INFO_PAGE.REGISTER_USER);

                                clearInterval(cooldownTimer);
                                cooldownTimerTime = 10;
                                setCooldownTimerTimeState(cooldownTimerTime);
                                setCooldownActive(true);
                            }
                        }
                        else if (showVoucherPromotionInterestedInfo) {
                            TempStore.update(s => {
                                s.showVoucherInfo = null;
                                s.showVoucherPromotionInterestedInfo = false;
                                s.isPaidFirstOrder = false;

                                s.cartItemsT = [];
                                s.cartItemsProcessedT = [];

                                s.cartOutletIdT = (selectedOutlet && selectedOutlet.uniqueId) ? selectedOutlet.uniqueId : null;

                                s.claimableVoucher = null;

                                s.showNamePhoneFieldsForRegister = true;
                            });

                            setShowVoucherPromotionInfoModal(false);
                            setCurrVoucherPromotionInfoPage(VOUCHER_PROMOTION_INFO_PAGE.REGISTER_USER);

                            clearInterval(cooldownTimer);
                            cooldownTimerTime = 10;
                            setCooldownTimerTimeState(cooldownTimerTime);
                            setCooldownActive(true);
                        }
                    }
                    else {
                        // CommonStore.update((s) => {
                        //     s.alertObj = {
                        //         title: "Error",
                        //         message: "Failed to create the account.",
                        //     };
                        // });

                        if (showVoucherInfo) {
                            // window.confirm(
                            //     `Unable to claim the voucher for now.`
                            // );

                            CommonStore.update((s) => {
                                s.alertObj = {
                                    title: "Info",
                                    message: `Unable to claim the voucher for now.`,
                                };
                            });
                        }
                        else if (showVoucherPromotionInterestedInfo) {
                            // window.confirm(
                            //     `Unable to subscribe to the outlet for now.`
                            // );

                            CommonStore.update((s) => {
                                s.alertObj = {
                                    title: "Info",
                                    message: `Unable to subscribe to the outlet for now, please try again later.`,
                                };
                            });
                        }
                    }

                    CommonStore.update(s => {
                        s.isLoading = false;
                    });

                    if (global.createUserOrderBody) {
                        // append info

                        if (!userPhone.startsWith('6')) {
                            userPhone = `6${userPhone}`;
                        }

                        global.createUserOrderBody.userName = name;
                        global.createUserOrderBody.userPhone = userPhone;

                        await AsyncStorage.setItem('storedUserName', name);
                        await AsyncStorage.setItem('storedUserPhone', userPhone);

                        global.storedUserName = name;
                        global.storedUserPhone = userPhone;
                    }

                    if (global.createUserOrderBody) {
                        // means still existed, help to create order

                        var createUserOrderBodyLocal = {
                            ...global.createUserOrderBody,
                        };

                        createUserOrderBodyLocal.userEiIdType = epIdTypeToTemp ? epIdTypeToTemp : epIdTypeTo;
                        createUserOrderBodyLocal.userEiId = epIdToTemp ? epIdToTemp : epIdTo;
                        createUserOrderBodyLocal.userTin = epTinToTemp ? epTinToTemp : epTinTo;
                        createUserOrderBodyLocal.userEmailSecond = epEmailToTemp ? epEmailToTemp : epEmailTo;

                        createUserOrderBodyLocal.epStateTo = epStateToTemp ? epStateToTemp : epStateTo;
                        createUserOrderBodyLocal.epNameTo = epNameToTemp ? epNameToTemp : epNameTo;
                        createUserOrderBodyLocal.epPhoneTo = userPhone ? userPhone : userNumber;
                        createUserOrderBodyLocal.epAddr1To = epAddr1ToTemp ? epAddr1ToTemp : epAddr1To;
                        createUserOrderBodyLocal.epCityTo = epCityToTemp ? epCityToTemp : epCityTo;
                        createUserOrderBodyLocal.epCodeTo = epCodeToTemp ? epCodeToTemp : epCodeTo;

                        createUserOrderBodyLocal.toUpdateEPDetails = toUpdateEPDetails ? toUpdateEPDetails : false;
                        createUserOrderBodyLocal.crmUserId = crmUserId ? crmUserId : '';

                        global.createUserOrderBody = null;

                        ApiClient.POST(API.createUserOrder, createUserOrderBodyLocal, {
                            timeout: 100000,
                        }).then(async (result) => {
                            if (createUserOrderBodyLocal.orderType === ORDER_TYPE.PICKUP) {
                                global.takeawayOrders.push(result);
                            }

                            CommonStore.update(s => {
                                // 2022-10-08 - Clear cart items
                                s.cartItems = [];
                                s.cartItemsProcessed = [];

                                s.selectedOutletItem = {};
                                s.selectedOutletItemAddOn = {};
                                s.selectedOutletItemAddOnChoice = {};
                                s.onUpdatingCartItem = null;
                            });

                            // 2024-12-12 - clear cart items
                            PaymentStore.update(s => {
                                s.cartItemsPayment = [];
                                s.outletIdPayment = '';
                                s.dateTimePayment = moment().valueOf();
                                s.orderTypePayment = '';
                            });

                            const latestSubdomain = await AsyncStorage.getItem(
                                "latestSubdomain"
                            );

                            if (latestSubdomain) {
                                if (createUserOrderBodyLocal.orderType === ORDER_TYPE.DINEIN) {
                                    linkToFunc &&
                                        linkToFunc(`${prefix}/outlet/${latestSubdomain}/order-history`);
                                }
                                else if (createUserOrderBodyLocal.orderType === ORDER_TYPE.PICKUP) {
                                    linkToFunc &&
                                        linkToFunc(`${prefix}/outlet/${latestSubdomain}/order-history-t`);
                                }
                            }
                        });

                        TempStore.update(s => {
                            s.showVoucherInfo = null;
                            s.showVoucherPromotionInterestedInfo = false;
                            s.isPaidFirstOrder = false;

                            s.claimableVoucher = null;

                            s.showNamePhoneFieldsForRegister = true;
                        });

                        clearInterval(cooldownTimer);
                        cooldownTimerTime = 10;
                        setCooldownTimerTimeState(cooldownTimerTime);
                        setCooldownActive(true);

                        // await deleteUserCart();

                        CommonStore.update(s => {
                            s.timestampOutletCategory = Date.now();
                        });

                        // open webpage

                        console.log('open webpage (1)');

                        if (toOpenGoogleReviewTabAfterClosed &&
                            selectedOutlet && selectedOutlet.placeUrl) {
                            if (selectedOutlet.GRPopup) {
                                window.open(selectedOutlet.placeUrl, '_blank');
                            }
                            else {
                                window.location.replace(selectedOutlet.placeUrl);
                            }
                        }
                    }
                    else {
                        // open webpage

                        console.log('open webpage (2)');

                        if (toOpenGoogleReviewTabAfterClosed &&
                            selectedOutlet && selectedOutlet.placeUrl) {
                            if (selectedOutlet.GRPopup) {
                                window.open(selectedOutlet.placeUrl, '_blank');
                            }
                            else {
                                window.location.replace(selectedOutlet.placeUrl);
                            }
                        }
                    }

                    //////////////////////////////////////////////////////

                    // 2024-11-26 - redirect to whatsapp first

                    // claim voucher whatsapp 

                    if (result && result.status === "success" && selectedOutlet.cvWa) {
                        // means voucher claimed successfully

                        if (showVoucherInfo && showVoucherInfo.campaignName) {
                            const phoneNumber = selectedOutlet.phone; // Replace with the recipient's phone number
                            const message = `Hi! I would like to claim the voucher of ${showVoucherInfo.campaignName}`;

                            const whatsappLink = getWhatsAppLink(phoneNumber, message);
                            // Opens the WhatsApp link in a new tab
                            window.open(whatsappLink, "_blank");
                        }
                    }

                    //////////////////////////////////////////////////////
                });
            }
            else {
                // CommonStore.update(s => {
                //     s.isLoading = false;
                // });

                // setExistingCustomer(true);
            }
        }
    };

    const saveScreenshotAsLink = () => {
        //////////////////////////////////////////////////////////////////////////////
        // 2024-03-05 - screenshot voucher, while waiting for api to completed

        // try {
        //     CommonStore.update(s => {
        //         s.isLoading = true;
        //     });

        //     const screenshotContainer = document.getElementById('screenshotContainer');

        //     // const userInfoClaimVoucherButton = document.getElementById('userInfoClaimVoucherButton');

        //     if (
        //         screenshotContainer
        //         // && userInfoClaimVoucherButton
        //     ) {
        //         // Use html2canvas to capture the container element
        //         html2canvas(screenshotContainer, {
        //             width: Platform.OS === 'ios' ? (windowWidth + windowWidth * 0.3) : windowWidth,
        //             height: Platform.OS === 'ios' ? (windowHeight + windowHeight * 0.3) : windowHeight,
        //             windowWidth: Platform.OS === 'ios' ? (windowWidth + windowWidth * 0.3) : windowWidth,
        //             windowHeight: Platform.OS === 'ios' ? (windowHeight + windowHeight * 0.3) : windowHeight,
        //             x: Platform.OS === 'ios' ? (windowWidth * 0.15) : 0,
        //             y: Platform.OS === 'ios' ? (windowHeight * 0.15) : 0,
        //             scrollY: windowHeight * 0.3,
        //             scrollX: 0,
        //             allowTaint: true,
        //             letterRendering: 1,
        //             useCORS: true,
        //         }).then(canvas => {
        //             // Convert the canvas to a data URI
        //             // const screenshotUrl = canvas.toDataURL('image/png').replace("image/png", "image/octet-stream");
        //             const screenshotUrl = canvas.toDataURL('image/png');

        //             // // Create a temporary link element
        //             // const link = document.createElement('a');
        //             // link.target = '_blank';
        //             // link.href = screenshotUrl;
        //             // link.download = `${selectedOutlet.name}-${claimableVoucher.campaignName}-${moment(claimableVoucher.promoDateStart).format('DD MMM YY')}-${moment(claimableVoucher.promoDateEnd).format('DD MMM YY')}.png`;

        //             // // Trigger the download
        //             // document.body.appendChild(link);
        //             // link.click();

        //             // // Clean up
        //             // document.body.removeChild(link);

        //             // userInfoClaimVoucherButton.href = screenshotUrl;
        //             // userInfoClaimVoucherButton.download = `${selectedOutlet.name}-${claimableVoucher.campaignName}-${moment(claimableVoucher.promoDateStart).format('DD MMM YY')}-${moment(claimableVoucher.promoDateEnd).format('DD MMM YY')}.png`;

        //             global.voucherScreenshotUrl = screenshotUrl;
        //             global.voucherScreenshotName = `${selectedOutlet.name}-${showVoucherInfo.campaignName}-${moment(showVoucherInfo.promoDateStart).format('DD MMM YY')}-${moment(showVoucherInfo.promoDateEnd).format('DD MMM YY')}.png`;

        //             CommonStore.update(s => {
        //                 s.isLoading = false;
        //             });
        //         });
        //     }
        // }
        // catch (ex) {
        //     console.error(ex);

        //     CommonStore.update(s => {
        //         s.isLoading = false;
        //     });
        // }

        //////////////////////////////////////////////////////////////////////////////
    };

    useEffect(() => {
        if (selectedOutlet && selectedOutlet.forceSignUp && selectedOutlet.forceSignUp === true) {
            setCooldownActive(false);
        };
    }, [selectedOutlet, showVoucherPromotionInfoModal])

    const placeUserOrder = () => {
        if (global.createUserOrderBody) {
            // means still existed, help to create order

            var createUserOrderBodyLocal = {
                ...global.createUserOrderBody,
            };

            // this no need first

            // createUserOrderBodyLocal.userEiIdType = epIdTypeToTemp ? epIdTypeToTemp : epIdTypeTo;
            // createUserOrderBodyLocal.userEiId = epIdToTemp ? epIdToTemp : epIdTo;
            // createUserOrderBodyLocal.userTin = epTinToTemp ? epTinToTemp : epTinTo;
            // createUserOrderBodyLocal.userEmailSecond = epEmailToTemp ? epEmailToTemp : epEmailTo;

            // createUserOrderBodyLocal.epStateTo = epStateToTemp ? epStateToTemp : epStateTo;
            // createUserOrderBodyLocal.epNameTo = epNameToTemp ? epNameToTemp : epNameTo;
            // createUserOrderBodyLocal.epPhoneTo = userNumber;
            // createUserOrderBodyLocal.epAddr1To = epAddr1ToTemp ? epAddr1ToTemp : epAddr1To;
            // createUserOrderBodyLocal.epCityTo = epCityToTemp ? epCityToTemp : epCityTo;
            // createUserOrderBodyLocal.epCodeTo = epCodeToTemp ? epCodeToTemp : epCodeTo;

            // createUserOrderBodyLocal.toUpdateEPDetails = toUpdateEPDetails ? toUpdateEPDetails : false;
            // createUserOrderBodyLocal.crmUserId = crmUserId ? crmUserId : '';

            global.createUserOrderBody = null;

            ApiClient.POST(API.createUserOrder, createUserOrderBodyLocal, {
                timeout: 100000,
            }).then(async (result) => {
                if (createUserOrderBodyLocal.orderType === ORDER_TYPE.PICKUP) {
                    global.takeawayOrders.push(result);
                }

                CommonStore.update(s => {
                    // 2022-10-08 - Clear cart items
                    s.cartItems = [];
                    s.cartItemsProcessed = [];

                    s.selectedOutletItem = {};
                    s.selectedOutletItemAddOn = {};
                    s.selectedOutletItemAddOnChoice = {};
                    s.onUpdatingCartItem = null;
                });

                const latestSubdomain = await AsyncStorage.getItem(
                    "latestSubdomain"
                );

                if (latestSubdomain) {
                    if (createUserOrderBodyLocal.orderType === ORDER_TYPE.DINEIN) {
                        linkToFunc &&
                            linkToFunc(`${prefix}/outlet/${latestSubdomain}/order-history`);
                    }
                    else if (createUserOrderBodyLocal.orderType === ORDER_TYPE.PICKUP) {
                        linkToFunc &&
                            linkToFunc(`${prefix}/outlet/${latestSubdomain}/order-history-t`);
                    }
                }
            });

            TempStore.update(s => {
                s.showVoucherInfo = null;
                s.showVoucherPromotionInterestedInfo = false;
                s.isPaidFirstOrder = false;

                s.claimableVoucher = null;

                s.showNamePhoneFieldsForRegister = true;
            });

            clearInterval(cooldownTimer);
            cooldownTimerTime = 10;
            setCooldownTimerTimeState(cooldownTimerTime);
            setCooldownActive(true);

            // await deleteUserCart();

            CommonStore.update(s => {
                s.timestampOutletCategory = Date.now();
            });
        }
    };

    return (
        <>
            {
                showVoucherInfo
                    ?
                    <Modal
                        style={{
                            // width: windowWidth,
                            // height: windowHeight,
                        }}
                        visible={showVoucherInfo}
                        transparent={voucherPopupFullScreen ? false : true}
                        animationType={"none"}
                    >
                        <View
                            style={{
                                flex: 1,
                                // backgroundColor: 'rgba(0, 0, 0, 0.7)',
                                backgroundColor: Colors.secondaryColor,
                                justifyContent: 'center',
                                alignItems: 'center',
                            }}
                            nativeID="screenshotContainer"
                        >
                            <TouchableOpacity
                                onPress={() => {
                                    TempStore.update(s => {
                                        s.showVoucherInfo = null;
                                    });

                                    placeUserOrder();
                                }}
                                style={{ justifyContent: 'flex-start', width: '100%', height: windowHeight * 0.055, opacity: 0, backgroundColor: 'transparent' }}>
                            </TouchableOpacity>

                            <TouchableOpacity
                                testID="closeButton"
                                style={[
                                    styles.closeButton,
                                    {
                                        right: isMobile()
                                            ? windowWidth * 0.04
                                            : windowWidth * 0.01,
                                        top: isMobile()
                                            ? windowWidth * 0.04
                                            : windowWidth * 0.01,
                                    },
                                ]}
                                onPress={() => {
                                    TempStore.update(s => {
                                        s.showVoucherInfo = null;
                                    });

                                    placeUserOrder();
                                }}
                            >
                                <AntDesign
                                    name="closecircle"
                                    size={25}
                                    color={Colors.blackColor}
                                />
                            </TouchableOpacity>

                            <View style={{ backgroundColor: 'white', borderRadius: 20, height: windowHeight * 0.88, width: windowWidth * 0.9, overflow: 'hidden' }}>{/* height: windowHeight * 0.85 */}
                                <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{ alignItems: 'center', justifyContent: 'center', }}>
                                    {/* {!isLoading ? ( */}
                                    <>
                                        {/* <View style={{ justifyContent: 'center', alignItems: 'center', marginTop: 25, marginBottom: 10, }}>
                                            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 26, }}>
                                                Congratulation!
                                            </Text>
                                        </View> */}
                                        <View
                                            style={{
                                                alignItems: 'center',
                                                backgroundColor: '#ffffff',
                                                justifyContent: 'center',
                                                borderColor: Colors.fieldtTxtColor,
                                                marginTop: windowWidth * 0.04,
                                            }}>
                                            {(showVoucherInfo && showVoucherInfo.image) ?
                                                <>
                                                    <AsyncImage
                                                        source={{ uri: showVoucherInfo.image }}
                                                        item={showVoucherInfo}
                                                        style={{
                                                            width: windowWidth * 0.82,
                                                            height: voucherPopupFullScreen ? windowWidth * 0.75 : windowHeight * 0.32,
                                                            marginBottom: 10,
                                                            borderRadius: 20,
                                                        }}
                                                    />
                                                </>
                                                :
                                                <>
                                                    <View style={{
                                                        backgroundColor: Colors.secondaryColor,
                                                        width: windowWidth * 0.82,
                                                        height: voucherPopupFullScreen ? windowWidth * 0.75 : windowHeight * 0.32,
                                                        marginBottom: 10,
                                                        alignSelf: 'center',
                                                        justifyContent: 'center',
                                                        alignItems: 'center',
                                                        borderRadius: 20,
                                                    }}>
                                                        <Ionicons name="fast-food-outline" size={voucherPopupFullScreen ? 200 : 80} />
                                                    </View>
                                                </>
                                            }
                                            <Text
                                                numberOfLines={2}
                                                style={{
                                                    width: windowWidth * 0.82,
                                                    // fontSize: showVoucherInfo && showVoucherInfo.campaignName && showVoucherInfo.campaignName.length > 15 ? '2em' : "2.5em",
                                                    fontSize: 24,
                                                    fontFamily: "NunitoSans-Bold",
                                                    marginBottom: 10,
                                                    textAlign: 'center',
                                                }}>
                                                {showVoucherInfo && showVoucherInfo.campaignName ? showVoucherInfo.campaignName : 'N/A'}
                                            </Text>
                                            <Marquee style={{ marginBottom: 10, }}>
                                                <Text
                                                    numberOfLines={1}
                                                    style={{
                                                        width: windowWidth * 0.82,
                                                        fontSize: 16,
                                                        fontFamily: "NunitoSans-SemiBold",
                                                        color: Colors.blackColor,
                                                        marginBottom: 10,
                                                        textAlign: 'center',
                                                    }}>
                                                    {showVoucherInfo && showVoucherInfo.campaignDescription ? showVoucherInfo.campaignDescription : 'N/A'}
                                                </Text>
                                            </Marquee>
                                            <View style={{
                                                marginBottom: 15,
                                                ...!voucherPopupFullScreen && {
                                                    width: windowWidth * 0.9,
                                                    paddingHorizontal: 20,
                                                    justifyContent: "center",
                                                    alignItems: 'center'
                                                }
                                            }}>
                                                {(selectedOutlet && selectedOutlet.tickTerms) ? (
                                                    <View
                                                        style={{
                                                            margin: 10,
                                                            flexDirection: "row",
                                                            justifyContent: 'center',
                                                            width: "100%",
                                                        }}
                                                    >
                                                        <CheckBox
                                                            style={{
                                                                marginVertical: 2,
                                                            }}
                                                            value={
                                                                tickboxConsent
                                                            }
                                                            onValueChange={() => {
                                                                if (tickboxConsent == true) {
                                                                    TempStore.update(s => { s.tickboxConsent = false; })
                                                                } else {
                                                                    TempStore.update(s => { s.tickboxConsent = true; })
                                                                }
                                                            }}
                                                        />

                                                        <Text style={{ paddingHorizontal: 10 }}>
                                                            {'By logging in, you agree to MyKoodoo '}
                                                            <Text style={{ color: 'blue', textDecorationLine: 'underline' }}
                                                                onPress={() => { Linking.openURL('https://mykoodoo.com/terms/') }}>{'Terms of Use'}</Text>
                                                            {' & '}
                                                            <Text style={{ color: 'blue', textDecorationLine: 'underline' }}
                                                                onPress={() => { Linking.openURL('https://mykoodoo.com/privacy-policy/') }}>{'Privacy Policy'}</Text>
                                                        </Text>
                                                    </View>
                                                ) : (
                                                    <Text style={{
                                                        fontSize: 16,
                                                        fontFamily: "NunitoSans-SemiBold",
                                                        marginBottom: 5,
                                                    }}>
                                                        Let us know who to send this voucher
                                                    </Text>
                                                )}

                                                <View style={{
                                                    flexDirection: 'row',
                                                    justifyContent: 'center',
                                                    justifyContent: 'space-between',
                                                    alignItems: 'center',
                                                    marginBottom: 0,
                                                }}>
                                                    <View style={{
                                                        flexDirection: 'row',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        width: '100%',
                                                    }}>
                                                        <TextInput
                                                            style={[
                                                                styles.textInput,
                                                                {
                                                                    // marginHorizontal: 24,
                                                                    width: windowWidth * 0.6,
                                                                    // height: 45,
                                                                    textAlign: 'center',
                                                                    paddingVertical: 10,

                                                                    height: 'unset',
                                                                },
                                                            ]}
                                                            multiline={false}
                                                            clearButtonMode="while-editing"
                                                            placeholder="Name (Ex: John)"
                                                            onChangeText={(text) => {
                                                                setName(text)
                                                            }}
                                                            onFocus={() => {
                                                                setCooldownActive(false);
                                                            }}
                                                            onBlur={saveScreenshotAsLink}
                                                            value={name}
                                                        />
                                                    </View>
                                                </View>
                                                <View style={{
                                                    flexDirection: 'row',
                                                    justifyContent: 'center',
                                                    justifyContent: 'space-between',
                                                    alignItems: 'center',
                                                    marginBottom: 0,
                                                }}>
                                                    <View style={{
                                                        flexDirection: 'row',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        width: '100%',
                                                    }}>
                                                        <TextInput
                                                            style={[
                                                                styles.textInput,
                                                                {
                                                                    // marginHorizontal: 24,
                                                                    width: windowWidth * 0.6,
                                                                    // height: 45,
                                                                    textAlign: 'center',
                                                                    paddingVertical: 10,

                                                                    height: 'unset',
                                                                },
                                                            ]}
                                                            multiline={false}
                                                            clearButtonMode="while-editing"
                                                            placeholder="Phone (Ex: 60123456789)"
                                                            onChangeText={(text) => {
                                                                setPhone(text);
                                                            }}
                                                            onFocus={() => {
                                                                setCooldownActive(false);
                                                            }}
                                                            onBlur={saveScreenshotAsLink}
                                                            value={phone}
                                                            keyboardType={"decimal-pad"}
                                                        />
                                                    </View>
                                                    <View style={{}}>

                                                    </View>
                                                </View>
                                            </View>
                                            {/* {(showVoucherInfo && showVoucherInfo.voucherTerms) ? */}
                                            <View style={{
                                                marginHorizontal: 30, marginBottom: 10,
                                                ...!voucherPopupFullScreen && {
                                                    paddingHorizontal: 20,
                                                    width: windowWidth * 0.9,
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    opacity: (showVoucherInfo && showVoucherInfo.voucherTerms) ? 100 : 0,
                                                }
                                            }}>

                                                {/* <Text
                                                        style={{
                                                            fontSize: 16,
                                                            fontFamily: "NunitoSans-SemiBold",
                                                            marginBottom: 5,
                                                        }}>
                                                        Terms and Conditions
                                                    </Text> */}
                                                <TouchableOpacity
                                                    disabled={!(showVoucherInfo && showVoucherInfo.voucherTerms)}
                                                    onPress={() => {
                                                        alert(`${(showVoucherInfo && showVoucherInfo.voucherTerms) ? showVoucherInfo.voucherTerms : 'N/A'}`);
                                                    }}>
                                                    <Text numberOfLines={1}
                                                        style={{
                                                            width: windowWidth * 0.82,
                                                            fontSize: 16,
                                                            fontFamily: "NunitoSans-SemiBold",
                                                            marginBottom: 5,
                                                            color: Colors.blackColor,
                                                            textAlign: 'center',
                                                        }}>
                                                        {(showVoucherInfo && showVoucherInfo.voucherTerms) ? showVoucherInfo.voucherTerms : 'N/A'}
                                                    </Text>
                                                </TouchableOpacity>
                                            </View>
                                            {/* : null} */}

                                            < View style={{ flexDirection: 'row', marginBottom: 5 }}>
                                                <Text style={{
                                                    fontSize: "0.8em",
                                                    fontFamily: "NunitoSans-SemiBold",
                                                    color: Colors.blackColor,
                                                }}>
                                                    {'Valid from '}
                                                </Text>
                                                <Text style={{
                                                    fontSize: "0.8em",
                                                    fontFamily: "NunitoSans-SemiBold",
                                                    color: Colors.blackColor,
                                                }}>
                                                    {
                                                        showVoucherInfo
                                                            ?
                                                            (
                                                                (
                                                                    showVoucherInfo.activationDays !== undefined
                                                                    &&
                                                                    showVoucherInfo.expirationDays !== undefined
                                                                )
                                                                    ?
                                                                    `${moment().add(showVoucherInfo.activationDays, 'day').format('DD MMM YY')} to ${moment().add(showVoucherInfo.activationDays + showVoucherInfo.expirationDays, 'day').format('DD MMM YY')}`
                                                                    :
                                                                    `${moment(showVoucherInfo.promoDateStart).format('DD MMM YY')} to ${moment(showVoucherInfo.promoDateEnd).format('DD MMM YY')}`
                                                            )
                                                            :
                                                            'N/A'
                                                    }
                                                </Text>
                                            </View>
                                            <View>
                                            </View>
                                        </View>

                                        {/* Left semi-circle */}
                                        <View style={{
                                            width: 70,
                                            height: 70,
                                            backgroundColor: Colors.primaryColor,
                                            borderRadius: 120,
                                            position: 'absolute',
                                            left: -35, // Adjust as needed
                                            bottom: '19%' // Adjust as needed //30%
                                        }}></View>

                                        {/* Right semi-circle */}
                                        <View style={{
                                            width: 70,
                                            height: 70,
                                            backgroundColor: Colors.primaryColor,
                                            borderRadius: 120,
                                            position: 'absolute',
                                            right: -35, // Adjust as needed
                                            bottom: '21%' // Adjust as needed //76%
                                        }}></View>
                                    </>
                                    {/* ) : (
                                        <>
                                            <View style={{ justifyContent: 'center', alignSelf: 'center', marginTop: windowHeight * 0.35, alignItems: 'center' }}>
                                                <ActivityIndicator color={Colors.primaryColor} size={60} />
                                            </View>
                                        </>
                                    )} */}
                                </ScrollView>

                                <View style={{ position: 'relative', bottom: 0, justifyContent: 'center', alignItems: 'center' }}>
                                    <View style={{ width: windowWidth * 0.9, flexDirection: 'row', alignItems: 'center', justifyContent: 'center', marginTop: 5, marginBottom: 5, }}>
                                        <View style={{
                                            borderTop: "2px dashed #bbbbbb",
                                            color: "#fff",
                                            width: windowWidth * 0.9,
                                            height: "0",
                                        }}></View>
                                        <View style={{
                                            width: 50,
                                            height: 50,
                                            backgroundColor: Colors.secondaryColor,
                                            borderRadius: 50,
                                            position: 'absolute',
                                            left: -25,
                                        }}></View>
                                        <View style={{
                                            width: 50,
                                            height: 50,
                                            backgroundColor: Colors.secondaryColor,
                                            borderRadius: 40,
                                            position: 'absolute',
                                            right: -25,
                                        }}></View>
                                    </View>

                                    <TouchableOpacity
                                        disabled={isLoading}
                                        onPress={async () => {
                                            // proceedOrderAfterUserInfo();
                                            registerUser();
                                        }}>
                                        <View
                                            style={{
                                                backgroundColor: Colors.primaryColor,
                                                borderRadius: 20,
                                                cursor: "pointer",
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                                border: "none",
                                                marginBottom: 20,
                                                marginTop: 20,
                                            }}
                                        >
                                            {isLoading ?
                                                <View style={{
                                                    justifyContent: 'center', alignSelf: 'center', alignItems: 'center', paddingHorizontal: selectedOutlet.cvWa ? 10 : 70,
                                                    paddingVertical: 10,
                                                }}>
                                                    <ActivityIndicator color={Colors.whiteColor} size={'small'} />
                                                </View>
                                                :
                                                <Text style={{
                                                    fontSize: 16,
                                                    fontFamily: "NunitoSans-SemiBold",
                                                    color: Colors.whiteColor,
                                                    paddingHorizontal: selectedOutlet.cvWa ? 10 : 70,
                                                    paddingVertical: 10,
                                                }}>{isLoading ? `PROCESSING...` : (selectedOutlet.cvWa ? `CLAIM VIA WHATSAPP` : `CLAIM NOW`)}
                                                </Text>
                                            }
                                        </View>
                                    </TouchableOpacity>
                                </View>

                            </View>
                            <TouchableOpacity
                                onPress={() => {
                                    TempStore.update(s => {
                                        s.showVoucherInfo = null;
                                    });

                                    placeUserOrder();
                                }}
                                style={{ justifyContent: 'flex-start', width: '100%', height: windowHeight * 0.055, opacity: 0, backgroundColor: 'transparent' }}>
                            </TouchableOpacity>
                        </View>
                    </Modal>
                    :
                    <Modal
                        style={{ flex: 1 }}
                        visible={showVoucherPromotionInfoModal}
                        transparent={true}
                    >
                        <View
                            style={{
                                backgroundColor: "rgba(0,0,0,0.5)",
                                // flex: 1,
                                justifyContent: "center",
                                alignItems: "center",
                                minHeight: windowHeight,
                            }}
                        >
                            <View
                                style={{
                                    width: isMobile() ? windowWidth * 0.9 : windowWidth * 0.35,
                                    // height: isMobile() ? windowHeight * 0.4 : windowHeight * 0.35,
                                    backgroundColor: Colors.whiteColor,
                                    borderRadius: 8,
                                    padding: isMobile()
                                        ? windowWidth * 0.03
                                        : windowWidth * 0.04,
                                    alignItems: "center",
                                    justifyContent: "space-between",
                                    paddingTop: isMobile()
                                        ? windowWidth * 0.05
                                        : windowWidth * 0.03,
                                    paddingBottom: isMobile()
                                        ? windowWidth * 0.04
                                        : windowWidth * 0.02,
                                    ...isMobile() && {
                                        width: '90%',
                                        height: windowHeight * 0.6,
                                    },

                                    ...showGRIframe && {
                                        paddingTop: isMobile()
                                            ? windowWidth * 0.15
                                            : windowWidth * 0.03,
                                    },
                                }}
                            >
                                <TouchableOpacity
                                    disabled={selectedOutlet && selectedOutlet.forceSignUp}
                                    style={[
                                        styles.closeButton,
                                        {
                                            right: isMobile()
                                                ? windowWidth * 0.04
                                                : windowWidth * 0.01,
                                            top: isMobile()
                                                ? windowWidth * 0.04
                                                : windowWidth * 0.01,
                                            opacity: selectedOutlet && selectedOutlet.forceSignUp ? 0 : 100
                                        },
                                    ]}
                                    onPress={async () => {
                                        // setShowUserInfoModal(false);

                                        // if want to hide, just place the order

                                        setShowVoucherPromotionInfoModal(false);

                                        placeUserOrder();
                                    }}
                                >
                                    <AntDesign
                                        testID='closeButton'
                                        name="closecircle"
                                        size={25}
                                        color={Colors.fieldtTxtColor}
                                    />

                                    {/* <SimpleLineIcons name="close" size={25}
                                    color={Colors.fieldtTxtColor}
                                /> */}
                                </TouchableOpacity>

                                {
                                    (
                                        currVoucherPromotionInfoPage === VOUCHER_PROMOTION_INFO_PAGE.REGISTER_USER
                                        &&
                                        !showGRIframe
                                    )
                                        ?
                                        <>
                                            <View
                                                style={[
                                                    styles.modalTitle,
                                                    {
                                                        // backgroundColor: 'red'
                                                    },
                                                    isMobile ? {
                                                        marginTop: 25,
                                                    } : {

                                                    },
                                                ]}
                                            >
                                                <Text style={[styles.modalTitleText, {
                                                    textAlign: 'center',
                                                    marginTop: isMobile() ? 0 : 10,
                                                }, isMobile() ? {
                                                    // width: '80%',
                                                    // backgroundColor: 'red',
                                                } : {

                                                }]}>
                                                    {
                                                        showVoucherInfo
                                                            ?
                                                            (
                                                                (userName && userNumber)
                                                                    ?
                                                                    ((selectedOutlet && selectedOutlet.placeUrl) ? 'Leave the review to claim the voucher' : 'Enter name/number to claim the voucher')
                                                                    :
                                                                    'Enter name/number to claim the voucher'
                                                            )
                                                            :
                                                            ''
                                                    }
                                                    {
                                                        showVoucherPromotionInterestedInfo
                                                            ?
                                                            (
                                                                (userName && userNumber)
                                                                    ?
                                                                    ((selectedOutlet && selectedOutlet.placeUrl) ? 'Leave the review to receive promotional products in the future' : (showNamePhoneFieldsForRegister === false ? 'Fill in the following fields to receive your e-invoice' : 'Enter name/number to receive promotional products in the future'))
                                                                    :
                                                                    (showNamePhoneFieldsForRegister === false ? 'Fill in the following fields to receive your e-invoice' : 'Enter name/number to receive promotional products in the future')
                                                            )
                                                            // 'Enter name/number to receive promotional products in the future'
                                                            // 'Register Now to Get More\nVoucher/Promotion!'
                                                            :
                                                            ''
                                                    }
                                                </Text>
                                            </View>
                                            <ScrollView style={{}}>
                                                <View
                                                    style={[
                                                        styles.modalBody,
                                                        {
                                                            width: "95%",
                                                            alignItems: "center",
                                                            // alignItems: 'flex-start',
                                                            justifyContent: "flex-start",
                                                            // backgroundColor: 'blue',

                                                            marginTop: 10,
                                                        },
                                                    ]}
                                                >
                                                    {
                                                        showVoucherInfo
                                                            ?
                                                            <Text style={{
                                                                fontFamily: 'NunitoSans-SemiBold',
                                                                fontSize: 18,
                                                                marginBottom: 5,
                                                            }}>
                                                                {showVoucherInfo.campaignName ? showVoucherInfo.campaignName : 'N/A'}
                                                            </Text>
                                                            :
                                                            <></>
                                                    }

                                                    {
                                                        showVoucherInfo
                                                            ?
                                                            <View style={{ width: '50%', height: 60 }}>
                                                                <Text numberOfLines={2} style={{
                                                                    fontFamily: 'NunitoSans-Regular',
                                                                    fontSize: 18,
                                                                    marginBottom: 15,
                                                                }}>
                                                                    {showVoucherInfo.campaignDescription ? showVoucherInfo.campaignDescription : 'N/A'}
                                                                </Text>
                                                            </View>
                                                            :
                                                            <></>
                                                    }

                                                    {
                                                        showNamePhoneFieldsForRegister
                                                            ?
                                                            <>
                                                                <View
                                                                    style={{
                                                                        flexDirection: "row",
                                                                        alignItems: "center",
                                                                        justifyContent: "space-between",
                                                                        width: "100%",
                                                                        // backgroundColor: 'green',
                                                                    }}
                                                                >
                                                                    <Text
                                                                        style={[
                                                                            styles.modalBodyText,
                                                                            { fontSize: 16, fontFamily: "NunitoSans-Bold", width: '20%' },
                                                                        ]}
                                                                    >
                                                                        Name
                                                                    </Text>

                                                                    <View style={{
                                                                        flexDirection: "row",
                                                                        alignItems: "center",
                                                                        justifyContent: "space-between",
                                                                        width: "80%",
                                                                        // backgroundColor: 'green',
                                                                    }}>
                                                                        <TextInput
                                                                            editable={userName ? false : true}
                                                                            style={[
                                                                                styles.textInput,
                                                                                {
                                                                                    // marginHorizontal: 24,
                                                                                    width: "100%",

                                                                                    ...userNumber && {
                                                                                        color: Colors.fieldtTxtColor,
                                                                                    },
                                                                                },
                                                                            ]}
                                                                            // editable={!userEmail}
                                                                            multiline={false}
                                                                            clearButtonMode="while-editing"
                                                                            placeholder="John Smith"
                                                                            onChangeText={(text) => {
                                                                                // setUserInfoName(text)

                                                                                // UserStore.update((s) => {
                                                                                //     s.userInfoName = text;
                                                                                // });
                                                                                setName(text);
                                                                            }}
                                                                            onFocus={() => {
                                                                                setCooldownActive(false);
                                                                            }}
                                                                            value={name}
                                                                            defaultValue={name}
                                                                        />
                                                                    </View>
                                                                </View>

                                                                <View
                                                                    style={{
                                                                        flexDirection: "row",
                                                                        alignItems: "center",
                                                                        justifyContent: "space-between",
                                                                        width: "100%",
                                                                        // backgroundColor: 'green',
                                                                    }}
                                                                >
                                                                    <Text
                                                                        style={[
                                                                            styles.modalBodyText,
                                                                            { fontSize: 16, fontFamily: "NunitoSans-Bold", width: '20%' },
                                                                        ]}
                                                                    >
                                                                        Phone
                                                                    </Text>

                                                                    <View style={{
                                                                        flexDirection: "row",
                                                                        alignItems: "center",
                                                                        justifyContent: "space-between",
                                                                        width: "80%",
                                                                        // backgroundColor: 'green',
                                                                    }}>
                                                                        <TextInput
                                                                            editable={userNumber ? false : true}
                                                                            // editable={!isVerified && !userEmail}
                                                                            style={[
                                                                                styles.textInput,
                                                                                {
                                                                                    // marginHorizontal: 24,
                                                                                    width: "75%",
                                                                                    // ...isVerified && {
                                                                                    //     color: Colors.fieldtTxtColor,
                                                                                    // }
                                                                                    ...userNumber && {
                                                                                        color: Colors.fieldtTxtColor,
                                                                                    },
                                                                                },
                                                                            ]}
                                                                            multiline={false}
                                                                            clearButtonMode="while-editing"
                                                                            placeholder="60123456789"
                                                                            onChangeText={(text) => {
                                                                                // setUserInfoPhone(text)

                                                                                // UserStore.update((s) => {
                                                                                //     s.userInfoPhone = text;
                                                                                // });

                                                                                setPhone(text);
                                                                            }}
                                                                            onFocus={() => {
                                                                                setCooldownActive(false);
                                                                            }}
                                                                            value={phone}
                                                                            defaultValue={phone}
                                                                            keyboardType={"decimal-pad"}
                                                                        />

                                                                        <TouchableOpacity
                                                                            disabled={true}
                                                                            onPress={async () => {
                                                                                if (isVerified) {
                                                                                    window.confirm(
                                                                                        "This phone number is already verified."
                                                                                    );
                                                                                }
                                                                                else {
                                                                                    var userPhone = phone.replaceAll('-', '');

                                                                                    if (!userPhone) {
                                                                                        setVerifySMSModal(false);
                                                                                        // CommonStore.update((s) => {
                                                                                        //     s.alertObj = {
                                                                                        //         title: "Error",
                                                                                        //         message: "Please key in your contact.",
                                                                                        //     };
                                                                                        // });

                                                                                        window.confirm(
                                                                                            "Please key in your phone number."
                                                                                        );
                                                                                        return;
                                                                                    }

                                                                                    if (userPhone && /\D/.test(userPhone)) {
                                                                                        setVerifySMSModal(false);
                                                                                        // CommonStore.update((s) => {
                                                                                        //     s.alertObj = {
                                                                                        //         title: "Error",
                                                                                        //         message: "Only digits allowed for contact.",
                                                                                        //     };
                                                                                        // });
                                                                                        window.confirm(
                                                                                            "Only digits allowed for phone number."
                                                                                        );
                                                                                        return;
                                                                                    }

                                                                                    if (userPhone && !(userPhone.length === 10 || userPhone.length === 11)) {
                                                                                        if (
                                                                                            (userPhone.startsWith('011') && userPhone.length === 11)
                                                                                            ||
                                                                                            (userPhone.startsWith('6011') && userPhone.length === 12)
                                                                                        ) {
                                                                                            // still valid, do nothing
                                                                                        }
                                                                                        else {
                                                                                            setVerifySMSModal(false);
                                                                                            // CommonStore.update((s) => {
                                                                                            //     s.alertObj = {
                                                                                            //         title: "Error",
                                                                                            //         message: "Invalid contact format.\neg: 60123456789.",
                                                                                            //     };
                                                                                            // });
                                                                                            window.confirm(
                                                                                                "Invalid phone number format.\neg: 60123456789."
                                                                                            );
                                                                                            return;
                                                                                        }
                                                                                    }

                                                                                    const lastSentTime = await AsyncStorage.getItem('lastSentTime');

                                                                                    var isValid = false;

                                                                                    if (lastSentTime) {
                                                                                        const lastSentTimeParsed = moment(parseInt(lastSentTime)).valueOf();

                                                                                        if (moment().diff(lastSentTimeParsed, 'minute') >= 3) {
                                                                                            isValid = true;
                                                                                        }
                                                                                    }
                                                                                    else {
                                                                                        // means new one

                                                                                        isValid = true;
                                                                                    }

                                                                                    if (isValid) {
                                                                                        setSentCode(false);
                                                                                    }

                                                                                    setVerifySMSModal(true);
                                                                                }
                                                                            }}
                                                                            style={{
                                                                                backgroundColor:
                                                                                    Colors.primaryColor,
                                                                                alignSelf: "center",
                                                                                alignItems: 'center',
                                                                                // width: isMobile() ? windowWidth * 0.2 : 110,
                                                                                paddingHorizontal: 20,
                                                                                height: isMobile() ? 35 : 40,
                                                                                borderRadius: 10,
                                                                                justifyContent: "center",

                                                                                marginLeft: 20,

                                                                                opacity: 0,
                                                                            }}
                                                                        >
                                                                            <Text
                                                                                style={{
                                                                                    alignSelf: 'center',
                                                                                    color: Colors.whiteColor,
                                                                                    fontFamily: 'NunitoSans-Bold',
                                                                                    fontSize: 14

                                                                                }}>
                                                                                VERIFY
                                                                            </Text>
                                                                        </TouchableOpacity>
                                                                    </View>
                                                                </View>
                                                            </>
                                                            :
                                                            <></>
                                                    }

                                                    {/* 2024-08-20 - hide e-invoice checkbox first */}
                                                    {
                                                        selectedOutlet.ei
                                                            ?
                                                            <View style={{
                                                                flexDirection: 'row', alignSelf: 'flex-start',
                                                                marginTop: 10, left: -2,
                                                                marginBottom: 10,
                                                            }}>
                                                                <Checkbox
                                                                    checked={eInvoiceInfo}
                                                                    onChange={(e) => {
                                                                        setCooldownActive(false);

                                                                        setEInvoiceInfo(!eInvoiceInfo);
                                                                    }}
                                                                    style={{
                                                                        marginRight: eInvoiceInfo ? 4 : 5,
                                                                        marginTop: 1,
                                                                    }}
                                                                />
                                                                <Text
                                                                    style={[
                                                                        styles.modalBodyText,
                                                                        { fontSize: 16, fontFamily: "NunitoSans-Bold", width: '100%' },
                                                                    ]}
                                                                >
                                                                    Register for e-invoice
                                                                </Text>
                                                            </View>
                                                            :
                                                            <></>
                                                    }

                                                    {eInvoiceInfo ?
                                                        <>
                                                            {/* email */}
                                                            {
                                                                (
                                                                    selectedOutlet.eiAskFields === undefined
                                                                    ||
                                                                    (
                                                                        selectedOutlet.eiAskFields.length > 0
                                                                        &&
                                                                        selectedOutlet.eiAskFields.includes(EI_USER_FIELDS.EMAIL)
                                                                    )
                                                                )
                                                                    ?
                                                                    <View
                                                                        style={{
                                                                            flexDirection: "row",
                                                                            alignItems: "center",
                                                                            justifyContent: "space-between",
                                                                            width: "100%",
                                                                            // backgroundColor: 'green',
                                                                        }}
                                                                    >
                                                                        <Text
                                                                            style={[
                                                                                styles.modalBodyText,
                                                                                { fontSize: 16, fontFamily: "NunitoSans-Bold", width: '20%' },
                                                                            ]}
                                                                        >
                                                                            Email
                                                                        </Text>

                                                                        <View style={{
                                                                            flexDirection: "row",
                                                                            alignItems: "center",
                                                                            justifyContent: "space-between",
                                                                            width: "80%",
                                                                            // backgroundColor: 'green',
                                                                        }}>
                                                                            <TextInput
                                                                                style={[
                                                                                    styles.textInput,
                                                                                    {
                                                                                        // marginHorizontal: 24,
                                                                                        width: "100%",
                                                                                    },
                                                                                ]}
                                                                                // editable={!userEmail}
                                                                                multiline={false}
                                                                                clearButtonMode="while-editing"
                                                                                placeholder="Email"
                                                                                onChangeText={(text) => { setEpEmailToTemp(text) }}
                                                                                onFocus={() => {
                                                                                    setCooldownActive(false);
                                                                                }}
                                                                                value={epEmailToTemp}
                                                                                defaultValue={epEmailToTemp}
                                                                            />
                                                                        </View>
                                                                    </View>
                                                                    :
                                                                    <></>
                                                            }

                                                            {
                                                                (
                                                                    selectedOutlet.eiAskFields === undefined
                                                                    ||
                                                                    (
                                                                        selectedOutlet.eiAskFields.length > 0
                                                                        &&
                                                                        selectedOutlet.eiAskFields.includes(EI_USER_FIELDS.ID
                                                                        )
                                                                    )
                                                                )
                                                                    ?
                                                                    <>
                                                                        {/* id type & id */}
                                                                        <View
                                                                            style={{
                                                                                flexDirection: "row",
                                                                                alignItems: "center",
                                                                                justifyContent: "space-between",
                                                                                width: "100%",
                                                                                // backgroundColor: 'green',
                                                                            }}
                                                                        >
                                                                            <View style={{ width: '20%' }}>
                                                                                <Text
                                                                                    style={[
                                                                                        styles.modalBodyText,
                                                                                        { fontSize: 16, fontFamily: "NunitoSans-Bold", width: "100%" },
                                                                                    ]}
                                                                                >
                                                                                    ID Type
                                                                                </Text>
                                                                            </View>

                                                                            <View style={{ width: '86%' }}>
                                                                                <DropDownPicker
                                                                                    style={{
                                                                                        backgroundColor: Colors.fieldtBgColor,
                                                                                        width: '93%',
                                                                                        height: 50,
                                                                                        borderRadius: 10,
                                                                                        borderWidth: 1,
                                                                                        borderColor: "#E5E5E5",
                                                                                        flexDirection: "row",

                                                                                    }}
                                                                                    dropDownContainerStyle={{
                                                                                        width: '93%',
                                                                                        backgroundColor: Colors.fieldtBgColor,
                                                                                        borderColor: "#E5E5E5",
                                                                                    }}
                                                                                    labelStyle={{
                                                                                        marginLeft: 5,
                                                                                        flexDirection: "row",
                                                                                    }}
                                                                                    textStyle={{
                                                                                        fontSize: windowWidth <= 360 ? 14 : 16,
                                                                                        fontFamily: 'NunitoSans-Regular',

                                                                                        marginLeft: 5,
                                                                                        paddingVertical: 15,
                                                                                        paddingLeft: 15,
                                                                                        flexDirection: "row",
                                                                                    }}
                                                                                    selectedItemContainerStyle={{
                                                                                        flexDirection: "row",
                                                                                    }}

                                                                                    showArrowIcon={true}
                                                                                    ArrowDownIconComponent={({ style }) => (
                                                                                        <Ionicons
                                                                                            size={25}
                                                                                            color={Colors.fieldtTxtColor}
                                                                                            style={{ paddingHorizontal: 5, marginTop: 7.5 }}
                                                                                            name="chevron-down-outline"
                                                                                        />
                                                                                    )}
                                                                                    ArrowUpIconComponent={({ style }) => (
                                                                                        <Ionicons
                                                                                            size={25}
                                                                                            color={Colors.fieldtTxtColor}
                                                                                            style={{ paddingHorizontal: 5, marginTop: 7.5 }}
                                                                                            name="chevron-up-outline"
                                                                                        />
                                                                                    )}

                                                                                    showTickIcon={true}
                                                                                    TickIconComponent={({ press }) => (
                                                                                        <Ionicons
                                                                                            style={{ paddingHorizontal: 5, marginTop: 7.5 }}
                                                                                            color={
                                                                                                press ? Colors.fieldtBgColor : Colors.primaryColor
                                                                                            }
                                                                                            name={'md-checkbox'}
                                                                                            size={25}
                                                                                        />
                                                                                    )}
                                                                                    placeholder={'Select an ID Type'}
                                                                                    placeholderStyle={{
                                                                                        color: Colors.fieldtTxtColor
                                                                                    }}
                                                                                    searchable
                                                                                    searchableStyle={{
                                                                                        paddingHorizontal: windowWidth * 0.0079,
                                                                                    }}
                                                                                    value={epIdTypeToTemp}
                                                                                    items={idtypeOption}
                                                                                    onSelectItem={(item) => {
                                                                                        setEpIdTypeToTemp(item.value);
                                                                                    }}
                                                                                    open={openIdType}
                                                                                    setOpen={setOpenIdType}
                                                                                />
                                                                            </View>
                                                                        </View>
                                                                        <View
                                                                            style={{
                                                                                flexDirection: "row",
                                                                                alignItems: "center",
                                                                                justifyContent: "space-between",
                                                                                width: "100%",
                                                                                // backgroundColor: 'green',
                                                                                zIndex: -1,
                                                                            }}
                                                                        >
                                                                            <Text
                                                                                style={[
                                                                                    styles.modalBodyText,
                                                                                    { fontSize: 16, fontFamily: "NunitoSans-Bold", width: '20%' },
                                                                                ]}
                                                                            >
                                                                                ID
                                                                            </Text>

                                                                            <View style={{
                                                                                flexDirection: "row",
                                                                                alignItems: "center",
                                                                                justifyContent: "space-between",
                                                                                width: "80%",
                                                                                // backgroundColor: 'green',
                                                                            }}>
                                                                                <TextInput
                                                                                    style={[
                                                                                        styles.textInput,
                                                                                        {
                                                                                            // marginHorizontal: 24,
                                                                                            width: "100%",
                                                                                        },
                                                                                    ]}
                                                                                    // editable={!userEmail}
                                                                                    multiline={false}
                                                                                    clearButtonMode="while-editing"
                                                                                    placeholder="ID"
                                                                                    onChangeText={(text) => { setEpIdToTemp(text) }}
                                                                                    onFocus={() => {
                                                                                        setCooldownActive(false);
                                                                                    }}
                                                                                    value={epIdToTemp}
                                                                                    defaultValue={epIdToTemp}
                                                                                />
                                                                            </View>
                                                                        </View>
                                                                    </>
                                                                    :
                                                                    <></>
                                                            }

                                                            {
                                                                (
                                                                    selectedOutlet.eiAskFields === undefined
                                                                    ||
                                                                    (
                                                                        selectedOutlet.eiAskFields.length > 0
                                                                        &&
                                                                        selectedOutlet.eiAskFields.includes(EI_USER_FIELDS.TIN)
                                                                    )
                                                                )
                                                                    ?
                                                                    <>
                                                                        {/* tin */}
                                                                        <View
                                                                            style={{
                                                                                flexDirection: "row",
                                                                                alignItems: "center",
                                                                                justifyContent: "space-between",
                                                                                width: "100%",
                                                                                // backgroundColor: 'green',
                                                                                zIndex: -1,
                                                                            }}
                                                                        >
                                                                            <Text
                                                                                style={[
                                                                                    styles.modalBodyText,
                                                                                    { fontSize: 16, fontFamily: "NunitoSans-Bold", width: '20%' },
                                                                                ]}
                                                                            >
                                                                                TIN
                                                                            </Text>

                                                                            <View style={{
                                                                                flexDirection: "row",
                                                                                alignItems: "center",
                                                                                justifyContent: "space-between",
                                                                                width: "80%",
                                                                                // backgroundColor: 'green',
                                                                            }}>
                                                                                <TextInput
                                                                                    style={[
                                                                                        styles.textInput,
                                                                                        {
                                                                                            // marginHorizontal: 24,
                                                                                            width: "100%",
                                                                                        },
                                                                                    ]}
                                                                                    // editable={!userEmail}
                                                                                    multiline={false}
                                                                                    clearButtonMode="while-editing"
                                                                                    placeholder="TIN"
                                                                                    onChangeText={(text) => { setEpTinToTemp(text) }}
                                                                                    onFocus={() => {
                                                                                        setCooldownActive(false);
                                                                                    }}
                                                                                    value={epTinToTemp}
                                                                                    defaultValue={epTinToTemp}
                                                                                />
                                                                            </View>
                                                                        </View>
                                                                    </>
                                                                    :
                                                                    <></>
                                                            }

                                                            {
                                                                (
                                                                    selectedOutlet.eiAskFields === undefined
                                                                    ||
                                                                    (
                                                                        selectedOutlet.eiAskFields.length > 0
                                                                        &&
                                                                        selectedOutlet.eiAskFields.includes(EI_USER_FIELDS.ADDRESS)
                                                                    )
                                                                )
                                                                    ?
                                                                    <>
                                                                        {/* address */}
                                                                        <View
                                                                            style={{
                                                                                flexDirection: "row",
                                                                                alignItems: "center",
                                                                                justifyContent: "space-between",
                                                                                width: "100%",
                                                                                // backgroundColor: 'green',
                                                                                zIndex: -1,
                                                                            }}
                                                                        >
                                                                            <Text
                                                                                style={[
                                                                                    styles.modalBodyText,
                                                                                    { fontSize: 15, fontFamily: "NunitoSans-Bold", width: '20%' },
                                                                                ]}
                                                                            >
                                                                                Address
                                                                            </Text>

                                                                            <View style={{
                                                                                flexDirection: "row",
                                                                                alignItems: "center",
                                                                                justifyContent: "space-between",
                                                                                width: "80%",
                                                                                // backgroundColor: 'green',
                                                                            }}>
                                                                                <TextInput
                                                                                    style={[
                                                                                        styles.textInput,
                                                                                        {
                                                                                            // marginHorizontal: 24,
                                                                                            width: "100%",
                                                                                        },
                                                                                    ]}
                                                                                    // editable={!userEmail}
                                                                                    multiline={false}
                                                                                    clearButtonMode="while-editing"
                                                                                    placeholder="Address"
                                                                                    onChangeText={(text) => { setEpAddr1ToTemp(text) }}
                                                                                    onFocus={() => {
                                                                                        setCooldownActive(false);
                                                                                    }}
                                                                                    value={epAddr1ToTemp}
                                                                                    defaultValue={epAddr1ToTemp}
                                                                                />
                                                                            </View>
                                                                        </View>

                                                                        {/* city & zip code */}
                                                                        <View
                                                                            style={{
                                                                                flexDirection: "row",
                                                                                alignItems: "center",
                                                                                justifyContent: "space-between",
                                                                                width: "100%",
                                                                                // backgroundColor: 'green',
                                                                                zIndex: -1,
                                                                            }}
                                                                        >
                                                                            <Text
                                                                                style={[
                                                                                    styles.modalBodyText,
                                                                                    { fontSize: 16, fontFamily: "NunitoSans-Bold", width: '20%' },
                                                                                ]}
                                                                            >
                                                                                City
                                                                            </Text>

                                                                            <View style={{
                                                                                flexDirection: "row",
                                                                                alignItems: "center",
                                                                                justifyContent: "space-between",
                                                                                width: "80%",
                                                                                // backgroundColor: 'green',
                                                                            }}>
                                                                                <TextInput
                                                                                    style={[
                                                                                        styles.textInput,
                                                                                        {
                                                                                            // marginHorizontal: 24,
                                                                                            width: "100%",
                                                                                        },
                                                                                    ]}
                                                                                    // editable={!userEmail}
                                                                                    multiline={false}
                                                                                    clearButtonMode="while-editing"
                                                                                    placeholder="City"
                                                                                    onChangeText={(text) => { setEpCityToTemp(text) }}
                                                                                    onFocus={() => {
                                                                                        setCooldownActive(false);
                                                                                    }}
                                                                                    value={epCityToTemp}
                                                                                    defaultValue={epCityToTemp}
                                                                                />
                                                                            </View>
                                                                        </View>
                                                                        <View
                                                                            style={{
                                                                                flexDirection: "row",
                                                                                alignItems: "center",
                                                                                justifyContent: "space-between",
                                                                                width: "100%",
                                                                                // backgroundColor: 'green',
                                                                                zIndex: -1,
                                                                            }}
                                                                        >
                                                                            <Text
                                                                                style={[
                                                                                    styles.modalBodyText,
                                                                                    { fontSize: 16, fontFamily: "NunitoSans-Bold", width: '20%' },
                                                                                ]}
                                                                            >
                                                                                Postcode
                                                                            </Text>

                                                                            <View style={{
                                                                                flexDirection: "row",
                                                                                alignItems: "center",
                                                                                justifyContent: "space-between",
                                                                                width: "80%",
                                                                                // backgroundColor: 'green',
                                                                            }}>
                                                                                <TextInput
                                                                                    style={[
                                                                                        styles.textInput,
                                                                                        {
                                                                                            // marginHorizontal: 24,
                                                                                            width: "100%",

                                                                                        },
                                                                                    ]}
                                                                                    // editable={!userEmail}
                                                                                    multiline={false}
                                                                                    clearButtonMode="while-editing"
                                                                                    placeholder="Zip Code"
                                                                                    onChangeText={(text) => { setEpCodeToTemp(text) }}
                                                                                    onFocus={() => {
                                                                                        setCooldownActive(false);
                                                                                    }}
                                                                                    value={epCodeToTemp}
                                                                                    defaultValue={epCodeToTemp}
                                                                                />
                                                                            </View>
                                                                        </View>

                                                                        {/* province */}
                                                                        <View
                                                                            style={{
                                                                                flexDirection: "row",
                                                                                alignItems: "center",
                                                                                justifyContent: "space-between",
                                                                                width: "100%",
                                                                                // backgroundColor: 'green',
                                                                                zIndex: -1,
                                                                                marginTop: 10,
                                                                            }}
                                                                        >
                                                                            <View style={{ width: '20%' }}>
                                                                                <Text
                                                                                    style={[
                                                                                        styles.modalBodyText,
                                                                                        { fontSize: 16, fontFamily: "NunitoSans-Bold", width: "100%" },
                                                                                    ]}
                                                                                >
                                                                                    State
                                                                                </Text>
                                                                            </View>

                                                                            <View style={{ width: '86%' }}>
                                                                                <DropDownPicker
                                                                                    style={{
                                                                                        backgroundColor: Colors.fieldtBgColor,
                                                                                        width: '93%',
                                                                                        height: 50,
                                                                                        borderRadius: 10,
                                                                                        borderWidth: 1,
                                                                                        borderColor: "#E5E5E5",
                                                                                        flexDirection: "row",
                                                                                    }}
                                                                                    dropDownContainerStyle={{
                                                                                        width: '93%',
                                                                                        backgroundColor: Colors.fieldtBgColor,
                                                                                        borderColor: "#E5E5E5",
                                                                                    }}
                                                                                    labelStyle={{
                                                                                        marginLeft: 5,
                                                                                        flexDirection: "row",
                                                                                    }}
                                                                                    textStyle={{
                                                                                        fontSize: windowWidth <= 360 ? 14 : 16,
                                                                                        fontFamily: 'NunitoSans-Regular',

                                                                                        marginLeft: 5,
                                                                                        paddingVertical: 15,
                                                                                        paddingLeft: 15,
                                                                                        flexDirection: "row",
                                                                                    }}
                                                                                    selectedItemContainerStyle={{
                                                                                        flexDirection: "row",
                                                                                    }}

                                                                                    showArrowIcon={true}
                                                                                    ArrowDownIconComponent={({ style }) => (
                                                                                        <Ionicons
                                                                                            size={25}
                                                                                            color={Colors.fieldtTxtColor}
                                                                                            style={{ paddingHorizontal: 5, marginTop: 7.5 }}
                                                                                            name="chevron-down-outline"
                                                                                        />
                                                                                    )}
                                                                                    ArrowUpIconComponent={({ style }) => (
                                                                                        <Ionicons
                                                                                            size={25}
                                                                                            color={Colors.fieldtTxtColor}
                                                                                            style={{ paddingHorizontal: 5, marginTop: 7.5 }}
                                                                                            name="chevron-up-outline"
                                                                                        />
                                                                                    )}

                                                                                    showTickIcon={true}
                                                                                    TickIconComponent={({ press }) => (
                                                                                        <Ionicons
                                                                                            style={{ paddingHorizontal: 5, marginTop: 7.5 }}
                                                                                            color={
                                                                                                press ? Colors.fieldtBgColor : Colors.primaryColor
                                                                                            }
                                                                                            name={'md-checkbox'}
                                                                                            size={25}
                                                                                        />
                                                                                    )}
                                                                                    placeholder={'Select a Province'}
                                                                                    placeholderStyle={{
                                                                                        color: Colors.fieldtTxtColor,
                                                                                        // marginTop: 15,
                                                                                    }}
                                                                                    searchable
                                                                                    searchableStyle={{
                                                                                        paddingHorizontal: windowWidth * 0.0079,
                                                                                    }}
                                                                                    value={epStateToTemp}
                                                                                    items={provinceOption}
                                                                                    onSelectItem={(item) => {
                                                                                        setEpStateToTemp(item.value);
                                                                                    }}
                                                                                    open={openProvince}
                                                                                    setOpen={setOpenProvince}
                                                                                />
                                                                            </View>
                                                                        </View>
                                                                    </>
                                                                    :
                                                                    <></>
                                                            }
                                                        </>
                                                        : null}

                                                    {
                                                        (!gReview && selectedOutlet && selectedOutlet.placeUrl)
                                                            ?
                                                            <>
                                                                <View
                                                                    style={{
                                                                        flexDirection: "row",
                                                                        alignItems: "center",
                                                                        justifyContent: "space-between",
                                                                        width: "100%",
                                                                        // backgroundColor: 'green',

                                                                        marginVertical: 5,
                                                                    }}
                                                                >
                                                                    <Text
                                                                        style={[
                                                                            styles.modalBodyText,
                                                                            { fontSize: 16, fontFamily: "NunitoSans-Bold", width: '20%' },
                                                                        ]}
                                                                    >
                                                                        Rating
                                                                    </Text>

                                                                    <View style={{
                                                                        flexDirection: "row",
                                                                        alignItems: "center",
                                                                        justifyContent: "space-around",
                                                                        width: "80%",
                                                                        // backgroundColor: 'green',
                                                                    }}>
                                                                        {
                                                                            Array.from(Array(5)).map((item, index) => {
                                                                                return (
                                                                                    <TouchableOpacity onPress={() => {
                                                                                        setRating(index + 1);
                                                                                    }}>
                                                                                        <FontAwesome name={rating > index ? "star" : "star-o"} color={Colors.primaryColor} size={30} />
                                                                                    </TouchableOpacity>
                                                                                );
                                                                            })
                                                                        }
                                                                    </View>
                                                                </View>

                                                                <View
                                                                    style={{
                                                                        flexDirection: "row",
                                                                        alignItems: "center",
                                                                        justifyContent: "space-between",
                                                                        width: "100%",
                                                                        // backgroundColor: 'green',

                                                                        marginBottom: 20,
                                                                        alignItems: 'flex-start',
                                                                    }}
                                                                >
                                                                    <Text
                                                                        style={[
                                                                            styles.modalBodyText,
                                                                            { fontSize: 16, fontFamily: "NunitoSans-Bold", width: '20%', paddingTop: 15, },
                                                                        ]}
                                                                    >
                                                                        Review
                                                                    </Text>

                                                                    <View style={{
                                                                        flexDirection: "row",
                                                                        alignItems: "center",
                                                                        justifyContent: "space-between",
                                                                        width: "80%",
                                                                        // backgroundColor: 'green',
                                                                    }}>
                                                                        <TextInput
                                                                            // editable={!isVerified && !userEmail}
                                                                            style={[
                                                                                styles.textInput,
                                                                                {
                                                                                    // marginHorizontal: 24,
                                                                                    // width: "75%",
                                                                                    width: '100%',
                                                                                    // ...isVerified && {
                                                                                    //     color: Colors.fieldtTxtColor,
                                                                                    // },

                                                                                    height: 75,

                                                                                    paddingHorizontal: 20,
                                                                                    paddingTop: 10,
                                                                                },
                                                                            ]}
                                                                            multiline={true}
                                                                            clearButtonMode="while-editing"
                                                                            placeholder="A very enjoyable experience"
                                                                            onChangeText={(text) => {
                                                                                // setUserInfoPhone(text)

                                                                                // UserStore.update((s) => {
                                                                                //     s.userInfoPhone = text;
                                                                                // });

                                                                                setReview(text);
                                                                            }}
                                                                            onFocus={() => {
                                                                                setCooldownActive(false);
                                                                            }}
                                                                            value={review}
                                                                            defaultValue={review}
                                                                        // keyboardType={""}
                                                                        />
                                                                    </View>
                                                                </View>
                                                            </>
                                                            :
                                                            <></>
                                                    }
                                                </View>
                                            </ScrollView>

                                            {(selectedOutlet && selectedOutlet.tickTerms) ? (
                                                <View
                                                    style={{
                                                        marginVertical: 5,
                                                        marginHorizontal: 25,
                                                        flexDirection: "row",
                                                        justifyContent: 'center',
                                                    }}
                                                >
                                                    <CheckBox
                                                        style={{
                                                            marginVertical: 2,
                                                        }}
                                                        value={
                                                            tickboxConsent
                                                        }
                                                        onValueChange={() => {
                                                            if (tickboxConsent == true) {
                                                                TempStore.update(s => { s.tickboxConsent = false; })
                                                            } else {
                                                                TempStore.update(s => { s.tickboxConsent = true; })
                                                            }
                                                        }}
                                                    />

                                                    <Text style={{ paddingHorizontal: 10 }}>
                                                        {'By logging in, you agree to MyKoodoo '}
                                                        <Text style={{ color: 'blue', textDecorationLine: 'underline' }}
                                                            onPress={() => { Linking.openURL('https://mykoodoo.com/terms/') }}>{'Terms of Use'}</Text>
                                                        {' & '}
                                                        <Text style={{ color: 'blue', textDecorationLine: 'underline' }}
                                                            onPress={() => { Linking.openURL('https://mykoodoo.com/privacy-policy/') }}>{'Privacy Policy'}</Text>
                                                    </Text>
                                                </View>
                                            ) : (
                                                <></>
                                            )}

                                            <View
                                                style={{
                                                    alignItems: "center",
                                                    flexDirection: "row",
                                                    // justifyContent: 'space-between',
                                                    justifyContent: "space-around",
                                                    width: "100%",
                                                }}
                                            >


                                                <TouchableOpacity
                                                    disabled={isLoading}
                                                    style={[
                                                        styles.modalSaveButton,
                                                        {
                                                            // width: isMobile() ? windowWidth * 0.3 : windowWidth * 0.1,
                                                            marginTop: windowHeight * 0.04,
                                                            paddingHorizontal: windowWidth * 0.05,

                                                            // width: windowWidth * 0.01,
                                                            width: '40%',
                                                        },
                                                    ]}
                                                    onPress={() => {
                                                        // proceedOrderAfterUserInfo();
                                                        registerUser();
                                                    }}
                                                >
                                                    <Text
                                                        style={[
                                                            styles.modalDescText,
                                                            { color: Colors.primaryColor },
                                                        ]}
                                                    >
                                                        {isLoading ? 'LOADING...' : 'PROCEED'}
                                                    </Text>
                                                </TouchableOpacity>


                                            </View>

                                            {
                                                cooldownActive
                                                    ?
                                                    <View style={{
                                                        display: 'flex',
                                                        flexDirection: 'row',
                                                        justifyContent: 'center',
                                                        alignItems: 'center',
                                                        marginTop: 5,
                                                    }}>
                                                        <Text style={{
                                                            fontSize: 20,
                                                            fontFamily: 'NunitoSans-SemiBold',
                                                        }}>
                                                            {`Auto hide in ${Math.abs(cooldownTimerTimeState)}s`}
                                                        </Text>
                                                    </View>
                                                    :
                                                    <>
                                                    </>
                                            }
                                        </>
                                        :
                                        <></>
                                }

                                {/* {
                            showGRIframe
                                ?
                                <View style={{
                                    height: windowHeight * 0.8,
                                    width: windowWidth * 0.9,

                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                }}>
                                    <iframe
                                        id={'idGR'}
                                        style={{
                                            height: windowHeight * 0.8,
                                            width: windowWidth * 0.88,
                                            
                                            transform: 'scale(0.28,0.28) translate(1258px,-865px)',
                                        }}
                                        // src="https://www.google.com/search?igu=1"
                                        // src={`https://search.google.com/local/writereview?placeid=${(selectedOutlet && selectedOutlet.placeId) ? selectedOutlet.placeId : ''}&embedded=true`}
                                        // src={`https://www.google.com/search?igu=1&hl=zh-CN&gl=my&q=Lot+GF-03,+Ground+Floor,+%E6%98%9F%E5%B7%B4%E5%85%8B+The+Waterfront+@,+5,+Persiaran+Residen,+Desa+Parkcity,+52200+Kuala+Lumpur,+Federal+Territory+of+Kuala+Lumpur&ludocid=11654901566829695126&lsig=AB86z5UoB9Gid3IvzxgIYtYlsaD0#lrd=0x31cc48a60902f459:0xa1be873987071096,3`}
                                        src={(selectedOutlet && selectedOutlet.placeUrl) ? selectedOutlet.placeUrl : ''}
                                    >

                                    </iframe>
                                </View>
                                :
                                <></>
                        } */}
                            </View>
                        </View>
                    </Modal>
            }

            <Modal
                visible={verifySMSModal}
                transparent={true}
                supportedOrientations={['portrait', 'landscape']}
                animationType="fade"
                onRequestClose={() => { setVerifySMSModal(false) }}
            >
                <View style={{
                    backgroundColor: 'rgba(0,0,0,0.5)',
                    height: windowHeight,
                    justifyContent: 'center',
                }}>
                    <View style={{
                        alignSelf: 'center',
                        width: isMobile() ? '80%' : (windowWidth * 0.5),
                        height: 320,
                        backgroundColor: 'white',

                        borderRadius: 10,
                        borderColor: '#E5E5E5',

                        shadowOffset: {
                            width: 0,
                            height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,

                        marginTop: 40,
                    }}>
                        <TouchableOpacity
                            onPress={() => {
                                setVerifySMSModal(false);
                            }}
                        >
                            <View style={{
                                marginTop: 10,
                                marginRight: 10,
                                alignSelf: 'flex-end',
                                height: 20,
                            }}>
                                <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                            </View>
                        </TouchableOpacity>
                        <Text style={{
                            fontFamily: 'NunitoSans-Bold',
                            color: 'black',
                            fontSize: 20,
                            textAlign: 'center',
                            marginTop: 0,
                            // width: '100%',
                        }}>
                            SMS Verification
                        </Text>
                        <View style={{
                            width: '80%',
                            alignSelf: 'center',
                            marginTop: 20,
                        }}>
                            {sentCode == true ?
                                <Text
                                    style={{
                                        alignSelf: 'center',
                                        //color: Colors.whiteColor,
                                        fontFamily: 'NunitoSans-Regular',
                                        fontSize: 16,
                                        textAlign: 'center',
                                    }}
                                >
                                    Verification code has been sent to <b>+6{phone}</b>, please check your device if you have received the code, or wait for 3 minutes to send a new code.
                                </Text>
                                :
                                <Text
                                    style={{
                                        alignSelf: 'center',
                                        //color: Colors.whiteColor,
                                        fontFamily: 'NunitoSans-Regular',
                                        fontSize: 16
                                    }}
                                >
                                    Click <b>SEND</b> to send verification code to your device.
                                </Text>
                            }
                        </View>
                        <View
                            style={{
                                flexDirection: 'row',
                                alignSelf: 'center',
                            }}>
                            <View style={{
                                //justifyContent: 'center',
                                alignItems: 'center',
                                flexDirection: 'row',
                                marginTop: isMobile() ? '10%' : '15%',
                                //marginLeft: '-1%',
                                //width: isMobile() ? windowWidth : windowWidth * 1,
                                //backgroundColor: 'red'
                            }}>
                                <Text style={{
                                    fontFamily: 'NunitoSans-Regular',
                                    color: 'black',
                                    fontSize: 16,
                                    //textAlign: 'center',
                                    marginTop: 0,
                                    marginLeft: 5,
                                    // width: '100%',
                                }}>
                                    Code:
                                </Text>
                                <TextInput
                                    underlineColorAndroid={Colors.fieldtBgColor}
                                    clearButtonMode="while-editing"
                                    style={{
                                        height: 40,
                                        width: '60%',
                                        maxWidth: 150,
                                        paddingHorizontal: 20,
                                        backgroundColor: Colors.fieldtBgColor,
                                        paddingLeft: 15,
                                        marginLeft: 10,
                                        fontSize: 16,
                                        fontFamily: 'NunitoSans-Regular',
                                        textAlignVertical: 'center',
                                        alignItems: 'center',
                                        alignContent: 'center',

                                        borderRadius: 10,
                                        borderColor: '#E5E5E5',
                                        borderWidth: 1,
                                        shadowOffset: {
                                            width: 0,
                                            height: 2,
                                        },
                                        shadowOpacity: 0.22,
                                        shadowRadius: 3.22,
                                        elevation: 1,
                                    }}
                                    placeholder="123456"
                                    placeholderTextColor={Colors.descriptionColor}
                                    keyboardType="decimal-pad"
                                    onChangeText={(text) => {
                                        setVerificationCode(text);
                                    }}
                                    value={verificationCode}
                                    clearTextOnFocus={true}
                                />
                                {sentCode == false ?
                                    <TouchableOpacity
                                        style={{
                                            backgroundColor:
                                                Colors.primaryColor,
                                            width: isMobile() ? windowWidth * 0.17 : 110,
                                            height: isMobile() ? 35 : 40,
                                            // height: 60,
                                            alignSelf: "center",
                                            alignItems: 'center',
                                            borderRadius: 10,
                                            justifyContent: "center",
                                            marginLeft: 20,
                                        }}
                                        onPress={async () => {
                                            const lastSentTime = await AsyncStorage.getItem('lastSentTime');

                                            var isValid = false;

                                            if (lastSentTime) {
                                                const lastSentTimeParsed = moment(parseInt(lastSentTime)).valueOf();

                                                if (moment().diff(lastSentTimeParsed, 'minute') >= 3) {
                                                    isValid = true;
                                                }
                                            }
                                            else {
                                                // means new one

                                                isValid = true;
                                            }

                                            if (isValid) {
                                                setSentCode(true);
                                                sendVerifyOTP();
                                            }
                                        }}>
                                        <Text
                                            style={{
                                                color: '#ffffff',
                                                fontSize: 14,
                                                fontFamily: 'NunitoSans-Bold',
                                            }}>
                                            SEND
                                        </Text>
                                    </TouchableOpacity>
                                    :
                                    <></>
                                }

                            </View>
                        </View>
                        <View
                            style={{
                                flexDirection: 'row',
                                alignSelf: 'center',
                                marginTop: '5%',
                            }}>
                            <TouchableOpacity
                                style={{
                                    backgroundColor:
                                        Colors.primaryColor,
                                    alignSelf: "center",
                                    alignItems: 'center',
                                    // width: isMobile() ? windowWidth * 0.2 : 110,
                                    paddingHorizontal: 20,
                                    height: isMobile() ? 35 : 40,
                                    borderRadius: 10,
                                    justifyContent: "center",
                                    ...isMobile() && {
                                        marginTop: 10,
                                    },
                                }}
                                onPress={() => verifyRegisterOTP()}>
                                <Text
                                    style={{
                                        color: '#ffffff',
                                        fontSize: 14,
                                        fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    VERIFY
                                </Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </Modal>
        </>
    );
};

const styles = StyleSheet.create({
    modalContainer: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: "center",
        justifyContent: "center",
    },
    modalView: {
        height: Dimensions.get("window").width * 1,
        width: Dimensions.get("window").width * 0.8,
        backgroundColor: Colors.whiteColor,
        borderRadius: Dimensions.get("window").width * 0.07,
        padding: Dimensions.get("window").width * 0.07,
        alignItems: "center",
        justifyContent: "space-between",
    },
    closeButton: {
        position: "absolute",
        right: isMobile()
            ? Dimensions.get("window").width * 0.04
            : Dimensions.get("window").width * 0.01,
        top: isMobile()
            ? Dimensions.get("window").width * 0.04
            : Dimensions.get("window").width * 0.01,
    },
    modalTitle: {
        alignItems: "center",
    },
    modalBody: {
        flex: 0.8,
        alignItems: "center",
        justifyContent: "center",
    },
    modalTitleText: {
        fontFamily: "NunitoSans-Bold",
        // marginBottom: 10,
        fontSize: 20,
    },
    modalDescText: {
        fontFamily: "NunitoSans-SemiBold",
        fontSize: 16,
        color: Colors.fieldtTxtColor,
    },
    modalBodyText: {
        // flex: 1,
        fontFamily: "NunitoSans-SemiBold",
        fontSize: 16,
        width: "20%",
    },
    modalSaveButton: {
        width: isMobile()
            ? Dimensions.get("window").width * 0.3
            : Dimensions.get("window").width * 0.1,
        backgroundColor: Colors.fieldtBgColor,
        height: 45,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 8,

        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,
    },
    textInput: {
        height: 50,
        paddingHorizontal: 20,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        marginTop: 10,
        marginBottom: 10,

        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 2.22,
        elevation: 2,
    },
});

export default VoucherPromotionInfo;
import React, { Component, useReducer, useState, useEffect, useRef } from 'react';
import {
    StyleSheet,
    ScrollView,
    Image,
    View,
    Text,
    TouchableOpacity,
    TextInput,
    FlatList,
    RefreshControl,
    Alert,
    Modal,
    Dimensions,
    Platform,
    useWindowDimensions
} from 'react-native';
import DropDownPicker from 'react-native-dropdown-picker';
// import firebase from 'firebase';
import { getFirestore, collection, query, where, getDocs, limit } from "firebase/firestore";
import { signInAnonymously } from "firebase/auth";
import Colors from '../constant/Colors';
// import Autocomplete from 'react-google-autocomplete'
import AntDesign from 'react-native-vector-icons/AntDesign';
import DatePicker from "react-datepicker";
import { ReactComponent as GCalendar } from '../svg/GCalendar.svg'
import "react-datepicker/dist/react-datepicker.css";
import "../constant/datePicker.css";
import "../constant/datePickerP.css";
import * as Cart from '../util/Cart';
import * as User from '../util/User';
import Styles from '../constant/Styles';
// import ActionSheet from 'react-native-actionsheet';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Icon from 'react-native-vector-icons/Ionicons';
import Icons from 'react-native-vector-icons/Feather';
// import NumericInput from 'react-native-numeric-input';
// import { color } from 'react-native-reanimated';
// import QRCode from 'react-native-qrcode-svg';
import Close from 'react-native-vector-icons/AntDesign';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import moment from 'moment';
import AsyncImage from '../components/asyncImage';
// import LinearGradient from 'react-native-linear-gradient';
import LinearGradient from 'react-native-web-linear-gradient';
import { googleCloudApiKey, prefix } from "../constant/env";
import { useFocusEffect, useLinkTo } from "@react-navigation/native";
import { DataStore } from '../store/dataStore';
import Hashids from 'hashids';
import { ORDER_REGISTER_QR_SALT, REWARD_SECTION } from '../constant/common';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Collections } from '../constant/firebase';
import IconAnt from 'react-native-vector-icons/AntDesign';
import { isMobile, setRouteFrom, mondayFirst } from "../util/commonFuncs";
import imgLogo from '../asset/image/logo.png';

import { ReactComponent as Arrow } from '../svg/arrow.svg';
import { Switch } from 'react-native-web';
import { LOYALTY_CAMPAIGN_CREDIT_TRANSACTION_TYPE_PARSED } from '../constant/loyalty';
import { TempStore } from "../store/tempStore";
import FontAwesome from 'react-native-vector-icons/FontAwesome';

import OR_StampCards from './OR_StampCards';
import { PaymentStore } from '../store/paymentStore';
import { TableStore } from '../store/tableStore';
import { idbGet } from '../util/db';
import MaterialIcons from "react-native-vector-icons/MaterialIcons";
import { OutletStore } from '../store/outlet';
import { v4 as uuidv4 } from 'uuid';
import BigNumber from 'bignumber.js';


const hashids = new Hashids(ORDER_REGISTER_QR_SALT);


const OutletRewardScreen = (props) => {
    const { navigation, route } = props;
    const { height: windowHeight, width: windowWidth } = useWindowDimensions();

    const linkTo = useLinkTo();

    const [blockSMS, setBlockSMS] = useState(false);
    const [blockEmail, setBlockEmail] = useState(false);

    const crmUsers = OutletStore.useState((s) => s.crmUsers);

    const loyaltyHistoryCrmUser = CommonStore.useState(s => s.loyaltyHistoryCrmUser);
    const loyaltyHistoryLCCTransactions = CommonStore.useState(s => s.loyaltyHistoryLCCTransactions);
    const loyaltyHistoryLCCBalance = CommonStore.useState(s => s.loyaltyHistoryLCCBalance);
    const email = UserStore.useState((s) => s.email);
    const emailSecond = UserStore.useState((s) => s.emailSecond);
    const userNumber = UserStore.useState((s) => s.number);
    const name = UserStore.useState((s) => s.name);
    const dob = UserStore.useState((s) => s.dob);
    const gender = UserStore.useState((s) => s.gender);
    const epAddr1To = UserStore.useState((s) => s.epAddr1To);
    const epCityTo = UserStore.useState((s) => s.epCityTo);
    const epCodeTo = UserStore.useState((s) => s.epCodeTo);
    const epStateTo = UserStore.useState((s) => s.epStateTo);
    const tin = UserStore.useState((s) => s.epTinTo);
    const eiIdType = UserStore.useState((s) => s.epIdTypeTo);
    const eiId = UserStore.useState((s) => s.epIdTo);
    const address = UserStore.useState((s) => s.userAddresses);
    const levelName = UserStore.useState((s) => s.levelName);

    const userPointsTransactions = UserStore.useState(s => s.userPointsTransactions)
    const userPointsBalance = UserStore.useState(s => s.userPointsBalance);

    const userPointsBalanceLCC = CommonStore.useState(s => s.selectedOutletLCCBalance);

    const selectedOutlet = CommonStore.useState((s) => s.selectedOutlet);

    const availableTaggableVouchers = CommonStore.useState((s) => s.availableTaggableVouchers);

    const availableUserTaggableVouchers = CommonStore.useState((s) => s.availableUserTaggableVouchers,);
    const [searchText, setSearchText] = useState('');

    const isLoading = CommonStore.useState(s => s.isLoading);

    // const [rewardSection, setRewardSection] = useState(REWARD_SECTION.REWARDS);
    const rewardSection = TempStore.useState(s => s.rewardSection);

    const claimVoucherList = TempStore.useState((s) => s.claimVoucherList);
    const selectedMerchant = CommonStore.useState(s => s.selectedMerchant);

    const topupCreditTypes = CommonStore.useState((s) => s.topupCreditTypes);

    const [epNameToTemp, setEpNameToTemp] = useState('');
    const [epPhoneToTemp, setEpPhoneToTemp] = useState('');
    const [epEmailToTemp, setEpEmailToTemp] = useState('');
    const [epIdTypeToTemp, setEpIdTypeToTemp] = useState('NRIC');
    const [epIdToTemp, setEpIdToTemp] = useState('');
    const [epTinToTemp, setEpTinToTemp] = useState('');
    const [epAddr1ToTemp, setEpAddr1ToTemp] = useState('');
    const [epCityToTemp, setEpCityToTemp] = useState('');
    const [epCodeToTemp, setEpCodeToTemp] = useState('');
    const [openIdType, setOpenIdType] = useState(false);

    const [userName, setUserName] = useState('');
    const [userEmail, setUserEmail] = useState('');
    const [userDob, setUserDob] = useState(null);
    //state variable - editFlag
    const [editFlag, setEditFlag] = useState(false);
    const [customerEmailSecond, setCustomerEmailSecond] = useState('');


    const idtypeOption = [
        {
            label: 'IC',
            value: 'NRIC',
        },
        {
            label: 'Passport',
            value: 'PASSPORT',
        },
        {
            label: 'MyTentera',
            value: 'ARMY',
        },
    ];

    navigation.setOptions({
        headerLeft: () => (
            <TouchableOpacity
                style={{}}
                onPress={() => {
                    props.navigation.goBack();
                    // link&&linkTo(``);
                }}>
                <View
                    style={{
                        marginLeft: 10,
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                    }}>
                    <Ionicons
                        name="chevron-back"
                        size={26}
                        color={Colors.fieldtTxtColor}
                        style={{}}
                    />

                    <Text
                        style={{
                            color: Colors.fieldtTxtColor,
                            fontSize: 16,
                            textAlign: 'center',
                            fontFamily: 'NunitoSans-Regular',
                            lineHeight: 22,
                            marginTop: -1,
                        }}>
                        Back
                    </Text>
                </View>
            </TouchableOpacity>
        ),
        headerRight: () => (
            <TouchableOpacity
                onPress={() => {
                    // props.navigation.navigate('Profile');
                }}
                style={{}}>
                <View style={{ marginRight: 15 }}>
                    {/* <Ionicons name="menu" size={30} color={Colors.primaryColor} /> */}
                </View>
            </TouchableOpacity>
        ),
        headerTitle: () => (
            <View
                style={{
                    justifyContent: 'center',
                    alignItems: 'center',
                    bottom: -1,
                }}>
                <Text
                    style={{
                        fontSize: 20,
                        lineHeight: 25,
                        textAlign: 'center',
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.mainTxtColor,
                    }}>
                    Outlet Rewards
                </Text>
            </View>
        ),
    });

    const [outletId, setOutletId] = useState('');
    const [outletName, setOutletName] = useState('');
    const [outletCover, setOutletCover] = useState('');
    const [merchantId, setMerchantId] = useState('');
    const [merchantName, setMerchantName] = useState('');

    const temporarydata = [
        { point: 2000, name: 'product1', time: '13.00-15.00' },
        { point: 2000, name: 'product2', time: '13.00-15.00' },
        { point: 1000, name: 'product3', time: '15.00-17.00' },
        // { point: 1000, name: 'product4', time: '15.00-17.00' },
    ]

    const temp_availableTaggableVouchers = [
        { point: 2000, campaignName: 'product1', promoDateEnd: '2023-10-27' },
        { point: 2000, campaignName: 'product2', promoDateEnd: '2023-10-27' },
        // { point: 1000, campaignName: 'product3', promoDateEnd: '2023-10-27' },
        // { point: 1000, campaignName: 'product4', promoDateEnd: '2023-10-27' },
    ];

    // test code to apply stamp rewards

    // const redeemableStackedLoyaltyStamps = CommonStore.useState(s => s.redeemableStackedLoyaltyStamps);

    // useEffect(() => {
    //     if (redeemableStackedLoyaltyStamps.length > 0) {
    //         CommonStore.update((s) => {
    //             s.toRedeemStackedLoyaltyStamp = redeemableStackedLoyaltyStamps[0];
    //         });

    //         // alert('Please proceed with the payment method of Pay Now to redeem your voucher');

    //         const subdomain = await AsyncStorage.getItem("latestSubdomain");

    //         if (!subdomain) {
    //             linkTo && linkTo(`${prefix}/outlet/cart`)
    //         } else {
    //             linkTo && linkTo(`${prefix}/outlet/${subdomain}/cart`)
    //         }

    //         CommonStore.update((s) => {
    //             s.isFromCartPage = false;
    //         });
    //     }
    // }, []);


    const handleDateChange = (date) => {
        setUserDob(date.getTime());
    };

    useEffect(() => {
        if (name && userNumber) {
            setUserName(name ? name : '');
            setUserEmail(email ? email : '');
            setUserDob(dob ? dob : null);
            setCustomerEmailSecond(emailSecond ? emailSecond : '');
        }
    }, [editFlag,])



    const userLoyaltyStamps = CommonStore.useState((s) => s.userLoyaltyStamps);
    const selectedOutletLoyaltyStamps = CommonStore.useState((s) => s.selectedOutletLoyaltyStamps);

    const filteredUserLoyaltyStamps =
        selectedOutletLoyaltyStamps.slice().filter(item1 => {
            let isExpired = false;
            let currentTime = new Date();
            let currentHours = currentTime.getHours();
            let currentMinutes = currentTime.getMinutes();
            let currentSeconds = currentTime.getSeconds();

            let endHours = new Date(item1.endTime).getHours();
            let endMinutes = new Date(item1.endTime).getMinutes();
            let endSeconds = new Date(item1.endTime).getSeconds();

            if (Date.now() > item1.endDate)
                isExpired = true;
            else if (Date.now() === item1.endDate) {
                if (currentHours > endHours)
                    isExpired = true;
                else if (currentHours === endHours) {
                    if (currentMinutes > endMinutes)
                        isExpired = true;
                    else if (currentMinutes === endMinutes) {
                        if (currentSeconds >= endSeconds)
                            isExpired = true;
                    }
                }
            }

            return (
                userLoyaltyStamps.some(item2 =>
                    !isExpired &&
                    item2.loyaltyStampId === item1.uniqueId &&
                    item2.phone === userNumber
                )
            )
        });

    useEffect(() => {
        if (linkTo) {
            DataStore.update(s => {
                s.linkToFunc = linkTo;
            });

            global.linkToFunc = linkTo;
        }

        console.log('route');
        console.log(route);
        console.log('window.location.href');
        console.log(window.location.href);

        if (route.params === undefined ||
            // route.params.outletId === undefined ||
            // route.params.orderId === undefined ||
            // route.params.tableCode === undefined ||
            // route.params.tablePax === undefined ||
            // route.params.waiterId === undefined ||
            // route.params.outletId.length !== 36 ||
            // route.params.tableId.length !== 36 ||
            // route.params.qrDateTimeEncrypted === undefined
            route.params.userIdHuman == undefined
        ) {
            // still valid, can proceed to register user

            // linkTo && linkTo(`${prefix}/error`);

            console.log('general');
        }
        else {
            // firebase.auth().signInAnonymously()
            signInAnonymously(global.auth)
                .then((result) => {
                    // TempStore.update(s => {
                    //     s.firebaseAuth = true;
                    // });

                    const firebaseUid = result.user.uid;

                    ApiClient.GET(API.getTokenKWeb + firebaseUid).then(async (result) => {
                        console.log('getTokenKWeb');
                        console.log(result);

                        if (result && result.token) {
                            await AsyncStorage.setItem('accessToken', result.token);
                            await AsyncStorage.setItem('refreshToken', result.refreshToken);

                            global.accessToken = result.token;

                            UserStore.update(s => {
                                s.firebaseUid = result.userId;
                                s.userId = result.userId;
                                s.role = result.role;
                                s.refreshToken = result.refreshToken;
                                s.token = result.token;
                                s.name = '';
                                s.email = '';
                                s.number = '';
                                s.emailSecond = '';
                            });

                            // CommonStore.update(s => {
                            //   s.selectedOutletTableQRUrl = window.location.href;
                            // });

                            // const crmUserSnapshot = await firebase.firestore().collection(Collections.CRMUser)
                            //   .where('userIdHuman', '==', route.params.userIdHuman)
                            //   .limit(1)
                            //   .get();

                            const crmUserSnapshot = await getDocs(
                                query(
                                    collection(global.db, Collections.CRMUser),
                                    where('userIdHuman', '==', route.params.userIdHuman),
                                    limit(1),
                                )
                            );

                            var crmUser = null;

                            if (!crmUserSnapshot.empty) {
                                crmUser = crmUserSnapshot.docs[0].data();
                            }

                            if (crmUser) {
                                await AsyncStorage.setItem('loyaltyHistoryCrmUserIdHuman', route.params.userIdHuman);

                                setBlockSMS(crmUser.blockSMS || false);
                                setBlockEmail(crmUser.blockEmail || false);

                                // const outletSnapshot = await firebase.firestore().collection(Collections.Outlet)
                                //   .where('uniqueId', '==', crmUser.outletId)
                                //   .limit(1)
                                //   .get();

                                const outletSnapshot = await getDocs(
                                    query(
                                        collection(global.db, Collections.Outlet),
                                        where('uniqueId', '==', crmUser.outletId),
                                        limit(1),
                                    )
                                );

                                var outlet = {};
                                if (!outletSnapshot.empty) {
                                    outlet = outletSnapshot.docs[0].data();
                                }

                                if (outlet) {
                                    // valid, can proceed to register user

                                    setOutletId(outlet.uniqueId);
                                    setOutletName(outlet.name);
                                    setOutletCover(outlet.cover);
                                    setMerchantId(outlet.merchantId);
                                    setMerchantName(crmUser.merchantName);

                                    CommonStore.update(s => {
                                        s.loyaltyHistoryCrmUser = crmUser;
                                    });

                                    //////////////////////////////////////////

                                    // const loyaltyCampaignCreditTransactionSnapshot = await firebase.firestore()
                                    //   .collection(Collections.LoyaltyCampaignCreditTransaction)
                                    //   .where('email', '==', crmUser.email)
                                    //   .where('outletId', '==', outlet.uniqueId)
                                    //   .where('deletedAt', '==', null)
                                    //   .get();

                                    // const userPointsTransactionsSnapshot = await getDocs(
                                    //     query(
                                    //         collection(global.db, Collections.UserPointsTransaction),
                                    //         where('phone', '==', crmUser.number),
                                    //         where('outletId', '==', outlet.uniqueId),
                                    //         where('deletedAt', '==', null),
                                    //     )
                                    // );

                                    // var userPointsTransactionsTemp = [];
                                    // var userPointsBalanceTemp = 0;

                                    // if (!userPointsTransactionsSnapshot.empty) {
                                    //     for (var i = 0; i < userPointsTransactionsSnapshot.size; i++) {
                                    //         const record = userPointsTransactionsSnapshot.docs[i].data();

                                    //         userPointsTransactionsTemp.push(record);

                                    //         userPointsBalanceTemp += record.amount;
                                    //     }
                                    // }

                                    // userPointsTransactions.sort((a, b) => b.createdAt - a.createdAt);

                                    // CommonStore.update(s => {
                                    //     s.selectedOutlet = outlet;
                                    // });

                                    // UserStore.update(s => {
                                    //     s.userPointsTransactions = userPointsTransactionsTemp;
                                    //     s.userPointsBalance = userPointsBalanceTemp;
                                    // });

                                    //////////////////////////////////////////

                                    // CommonStore.update(s => {
                                    //   s.registerUserOrder = userOrder;
                                    // });

                                    // await AsyncStorage.setItem('latestOutletId', outlet.uniqueId);

                                    // await updateUserCart(
                                    //   {
                                    //     ...route.params,
                                    //     outletId: outlet.uniqueId,
                                    //     tableId: tableId,
                                    //     tablePax: outletTable.seated,
                                    //     tableCode: outletTable.code,
                                    //   }
                                    //   , outlet, firebaseUid);

                                    // linkTo && linkTo('/web/outlet');
                                }
                                else {
                                    // linkTo && linkTo(`${prefix}/error`);
                                }
                            }
                            else {
                                // linkTo && linkTo(`${prefix}/error`);
                            }
                        }
                        else {
                            CommonStore.update(s => {
                                s.alertObj = {
                                    title: 'Error',
                                    message: 'Unauthorized access',
                                };

                                // s.isAuthenticating = false;
                            });

                            // linkTo && linkTo(`${prefix}/error`);
                        }
                    });
                });
        }
    }, [linkTo, route]);

    useEffect(() => {
        var tempVoucherCheckList = [];

        // claimVoucherList.filter((item) => {
        //     if (item.voucherPointsRequired <= userPointsBalance) {
        //         tempVoucherCheckList.push(item);
        //     }
        // });

        if (tempVoucherCheckList.length == 0) {
            TempStore.update(s => {
                s.showClaimVoucher = false;
            });
        }
        else {
            // TempStore.update(s => {
            //     s.claimVoucherList = tempVoucherCheckList;
            // });

            TempStore.update(s => {
                s.showClaimVoucher = false;
            });

        }
    }, [claimVoucherList]);

    useEffect(() => {
        CommonStore.update(s => {
            s.availableTaggableVouchers = availableUserTaggableVouchers;
        });
    }, [availableUserTaggableVouchers]);

    useEffect(() => {
        if (linkTo) {
            DataStore.update((s) => {
                s.linkToFunc = linkTo;
            });

            global.linkToFunc = linkTo;
        }
    }, [linkTo]);

    useEffect(() => {
        if (selectedOutlet) {
            const subdomain = AsyncStorage.getItem("latestSubdomain");
            if ((
                selectedOutlet.subdomain === 'hominsanttdi' ||
                selectedOutlet.subdomain === 'hominsan-ss15' ||
                selectedOutlet.subdomain === 'dream-coffee-pastry' ||
                selectedOutlet.subdomain === 'kopi-empat-petang' ||
                selectedOutlet.subdomain === 'melvin-cafe' ||
                selectedOutlet.subdomain === 'seafarer-cr' ||
                (selectedOutlet.subdomain && selectedOutlet.subdomain.startsWith('demo-cafe')) ||
                selectedOutlet.pOr
            )
            ) { // now only show for ttdi
                if (!email || !userNumber) {
                    if (!subdomain) {
                        linkTo && linkTo(`${prefix}/outlet/menu`);
                    }
                    else {
                        linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu`);
                    }
                }
            }
            else {
                if (!subdomain) {
                    linkTo && linkTo(`${prefix}/outlet/menu`);
                }
                else {
                    linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu`);
                }
            }
        }
    }, [selectedOutlet, email, userNumber])

    useEffect(() => {
        if (selectedOutlet === null) {
            readStates();
        }

        readCommonStates();
    }, [selectedOutlet]);




    const readStates = async () => {
        console.log('global.selectedoutlet = readStates (1) (==test==)');

        if (selectedOutlet === null) {
            console.log('global.selectedoutlet = readStates (2) (==test==)');

            // const commonStoreDataRaw = await AsyncStorage.getItem("@commonStore");
            const commonStoreDataRaw = await idbGet('@commonStore');
            if (commonStoreDataRaw !== undefined) {
                console.log('global.selectedoutlet = readStates (3) (==test==)');

                const commonStoreData = JSON.parse(commonStoreDataRaw);

                const latestOutletId = await AsyncStorage.getItem("latestOutletId");

                console.log('latestOutletId');
                console.log(latestOutletId);
                console.log('commonStoreData.selectedOutlet');
                console.log(commonStoreData.selectedOutlet);

                if (
                    commonStoreData.selectedOutlet &&
                    latestOutletId === commonStoreData.selectedOutlet.uniqueId
                ) {
                    // check if it's the same outlet user scanned

                    console.log('global.selectedoutlet = readStates (4) (==test==)');

                    //   if (isPlacingReservation) {
                    //   } else {
                    //     // if (
                    //     //   commonStoreData.orderType === ORDER_TYPE.DINEIN 
                    //     //   // 2022-10-08 - Try to disable this
                    //     //   // &&
                    //     //   // commonStoreData.userCart.uniqueId === undefined
                    //     // ) {
                    //     //   // logout the user

                    //     //   linkTo && linkTo(`${prefix}/scan`);
                    //     // }
                    //   }

                    console.log('global.selectedoutlet = commonStoreData.selectedOutlet (3) (==test==)');

                    global.selectedOutlet = commonStoreData.selectedOutlet;

                    CommonStore.replace(commonStoreData);

                    // const userStoreDataRaw = await AsyncStorage.getItem("@userStore");
                    const userStoreDataRaw = await idbGet('@userStore');
                    if (userStoreDataRaw !== undefined) {
                        const userStoreData = JSON.parse(userStoreDataRaw);

                        UserStore.replace(userStoreData);
                    }

                    // const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
                    const dataStoreDataRaw = await idbGet('@dataStore');
                    if (dataStoreDataRaw !== undefined) {
                        const dataStoreData = JSON.parse(dataStoreDataRaw);

                        DataStore.replace(dataStoreData);
                        // DataStore.replace({
                        //   ...dataStoreData,
                        //   ...dataStoreData.linkToFunc !== undefined && {
                        //     linkToFunc: dataStoreData.linkToFunc,
                        //   },
                        // });
                    }

                    // const tableStoreDataRaw = await AsyncStorage.getItem("@tableStore");
                    const tableStoreDataRaw = await idbGet('@tableStore');
                    if (tableStoreDataRaw !== undefined) {
                        const tableStoreData = JSON.parse(tableStoreDataRaw);

                        TableStore.replace(tableStoreData);
                    }

                    // const paymentStoreDataRaw = await AsyncStorage.getItem("@paymentStore");
                    const paymentStoreDataRaw = await idbGet('@paymentStore');
                    if (paymentStoreDataRaw !== undefined) {
                        const paymentStoreData = JSON.parse(paymentStoreDataRaw);

                        PaymentStore.replace(paymentStoreData);
                    }

                    // const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
                    // if (dataStoreDataRaw !== undefined) {
                    //   const dataStoreData = JSON.parse(dataStoreDataRaw);

                    //   DataStore.replace(dataStoreData);
                    // }
                }
            }
        }
    };

    const renderViewHistory = ({ item }) => {
        return (
            <View
                style={{
                    flexDirection: 'row',
                    paddingVertical: 10,
                    alignItems: 'center',
                    paddingHorizontal: 10,
                }}>
                <View style={{ flexDirection: 'column', width: '42.5%' }}>
                    <Text style={{ fontSize: isMobile() ? 10 : 16 }}>
                        {
                            LOYALTY_CAMPAIGN_CREDIT_TRANSACTION_TYPE_PARSED[
                            item.transactionType
                            ]
                        }
                    </Text>
                    <Text style={{ fontSize: isMobile() ? 10 : 16 }}>
                        {item.amount.toFixed(2)}
                    </Text>
                </View>
                <View style={{ flexDirection: 'column', width: '42.5%' }}>
                    <Text style={{ fontSize: isMobile() ? 10 : 16 }}>
                        {moment(item.createdAt).format('DD MMM YYYY')}
                    </Text>
                    <Text style={{ fontSize: isMobile() ? 10 : 16 }}>
                        {moment(item.createdAt).format('hh:mm A')}
                    </Text>
                </View>
                <View style={{ width: '15%', alignItems: 'center', justifyContent: 'center' }}>
                    <Text
                        style={{
                            fontSize: isMobile() ? 10 : 16,
                            fontFamily: 'NunitoSans-SemiBold',
                            backgroundColor: Colors.primaryColor,
                            color: Colors.whiteColor,
                            padding: 8,
                            borderRadius: 5,
                        }}>
                        Success
                    </Text>
                </View>
            </View>
        );
    };

    const readCommonStates = async () => {
        // if (!linkToFunc) {
        //   const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
        //   if (dataStoreDataRaw !== undefined) {
        //     const dataStoreData = JSON.parse(dataStoreDataRaw);
        //     DataStore.replace(dataStoreData);
        //   }
        // }
    };

    const flatListRef = useRef(null);
    const [flatListHeight, setFlatListHeight] = useState(0);

    const handleFlatListLayout = (event) => {
        const { height } = event.nativeEvent.layout;
        if (height < 50) {
            setFlatListHeight(60);
        }
        else {
            setFlatListHeight(height);
        }
        // console.log('flatListHeight', flatListHeight)
    };

    const [numUserStampsAcquired, setNumUserStampsAcquired] = useState({});

    const renderStamp = ({ item, index }) => {
        return (
            <>
                <View
                    style={{
                        // position: 'absolute',
                        // top: 10 + Math.floor(index / 5) * 50, // Adjust the positioning for rows
                        // left: 10 + (index % 5) * 65, // Adjust the positioning for columns
                        marginLeft: index % 5 === 0 ? 0 : windowWidth * 0.05,
                        marginBottom: windowHeight * 0.015,
                        borderWidth: 1,
                        borderRadius: 100,
                        width: 40,
                        height: 40,
                        backgroundColor: 'white',

                        justifyContent: 'center',
                        alignItems: 'center',
                    }}
                >
                    <FontAwesome
                        key={index}
                        name="star"
                        size={30}
                        color={index < numUserStampsAcquired[item.uniqueId] ? Colors.primaryColor : Colors.whiteColor}
                    />
                </View>
            </>
        )
    }

    const renderItem = ({ item, index }) => {
        return (
            <>
                <TouchableOpacity onPress={() => {
                    CommonStore.update(s => {
                        s.selectedTaggableVoucherView = item;
                    });
                    linkTo && linkTo(`${prefix}/outlet/outlet-reward-details`)
                }}
                    style={{
                        backgroundColor: 'white',
                        // flex: 0.5,
                        // width: windowWidth * 0.35,
                        width: '45%',
                        borderRadius: 5,
                        padding: 10,
                        margin: 10,
                    }}>
                    <View style={{ alignItems: 'center' }}>
                        {item.image ? (
                            <>
                                <AsyncImage
                                    source={{ uri: item.image }}
                                    item={item}
                                    style={{
                                        width: windowWidth * 0.3,
                                        height: windowHeight * 0.15,
                                        borderRadius: 5,
                                    }}
                                />
                            </>
                        ) :
                            <View style={{ backgroundColor: Colors.secondaryColor, width: windowWidth * 0.3, height: windowHeight * 0.15, alignItems: 'center', alignSelf: 'center', justifyContent: 'center', borderRadius: 5, }}>
                                <Ionicons
                                    name="fast-food-outline"
                                    // size={45}
                                    size={windowHeight * 0.05}
                                />
                            </View>
                        }
                    </View>
                    <View>
                        <View style={{ height: 50, }}>
                            <Text
                                style={{
                                    marginTop: 10,
                                    marginLeft: 5,
                                    marginBottom: 10,
                                    fontSize: 12,
                                    fontFamily: 'NunitoSans-Bold',
                                    //color: Colors.whiteColor,
                                    // borderBottomWidth: 1,
                                    // paddingBottom: 7,
                                    // height: windowHeight * 0.07,
                                }}
                                numberOfLines={2}
                            >
                                {item.campaignName}
                            </Text>
                        </View>

                        {/* <Text
                            style={{
                                marginTop: 5,
                                // marginLeft: 10,
                                fontSize: 12,
                                fontFamily: 'NunitoSans-SemiBold',
                                //color: Colors.whiteColor,
                            }}>
                            time:
                        </Text> */}
                        <View style={{ flexDirection: 'row', paddingTop: 5, alignItems: 'center', borderTopWidth: 1, }}>
                            <AntDesign name='clockcircleo' />

                            <Text
                                style={{
                                    //marginTop: 10,
                                    // marginLeft: 10,
                                    fontSize: 12,
                                    fontFamily: 'NunitoSans-Regular',
                                    //color: Colors.whiteColor,
                                    marginLeft: 8,
                                    marginTop: 5,
                                }}>
                                Exp. by {moment(item.promoDateEnd).format('dddd, Do MMM YYYY')}, {moment(item.promoTimeEnd).format('hh:mm A')}
                            </Text>
                        </View>
                    </View>
                </TouchableOpacity >
            </>
        )
    };

    useEffect(() => {
        // Iterate through each item in filteredUserLoyaltyStamps
        if (Object.keys(numUserStampsAcquired).length == filteredUserLoyaltyStamps.length)
            return;

        let tempStampsAcquired = {};

        filteredUserLoyaltyStamps.forEach((item) => {
            // The logic that was previously inside renderRewards
            // temp is used to find the stamp
            // console.log("item", item);

            let temp = userLoyaltyStamps.find(filterStamp => filterStamp.loyaltyStampId === item.uniqueId)
            // console.log('TEMMP', temp)
            var totalGetStamps = temp.stampCount;

            // for (var i = 0; i < temp.getHistory.length; i++) {
            //     totalGetStamps -= temp.getHistory[i].noOfStamp
            // }

            let noOfStampToDeduct = 0;
            // for (var i = 0; i < temp.getHistory.length; i++) {
            //     // remainingStamps -= selectedStampUserRecord.getHistory[i].noOfStamp;

            //     if (temp.getHistory[i].noOfStamp > noOfStampToDeduct) {
            //         noOfStampToDeduct = temp.getHistory[i].noOfStamp;
            //     }
            // }

            totalGetStamps -= noOfStampToDeduct;

            // console.log("totalGetStamps", totalGetStamps);
            tempStampsAcquired = { ...tempStampsAcquired, [item.uniqueId]: totalGetStamps };
            // console.log("tempStampsAcquired", tempStampsAcquired);
            setNumUserStampsAcquired(tempStampsAcquired);
            // console.log("numUserStampsAcquired", numUserStampsAcquired);
        });
    }, [filteredUserLoyaltyStamps, userLoyaltyStamps, numUserStampsAcquired]); // Add dependencies as needed

    const renderItem2 = ({ item, index }) => {
        const innerData = Array.from({ length: item.totalNumberOfStamp }, (_, key) => ({ key: String(key), index: index, uniqueId: item.uniqueId }));
        let noOfCol = Math.ceil(innerData.slice(0, 100).length / 5);
        if (noOfCol < 2)
            noOfCol = 2;
        const height = noOfCol * 55;
        return (
            <>
                <View
                    style={{
                        backgroundColor: 'white',
                        // flex: 0.5,
                        width: windowWidth * 0.85,
                        // marginRight: 5,
                        borderRadius: 5,
                        paddingHorizontal: 10,
                        paddingBottom: 10,
                        margin: 10,
                    }}>
                    <View
                        style={{
                            justifyContent: 'center',
                            alignItems: 'center',
                        }}>
                        <View
                            style={{
                                position: 'absolute',
                                zIndex: 100,
                            }}>
                            <FlatList
                                style={{
                                    marginTop: windowHeight * 0.015,
                                }}
                                ref={flatListRef}
                                showsVerticalScrollIndicator={false}
                                data={innerData.slice(0, 100)}
                                numColumns={5}
                                renderItem={renderStamp}
                                keyExtractor={(item) => item.key}
                            // onLayout={handleFlatListLayout}
                            />
                        </View>
                        {item.merchantLogo ? (
                            <>
                                <View
                                    style={{
                                        backgroundColor: Colors.secondaryColor,
                                        width: windowWidth * 0.85,
                                        height: height,
                                        alignItems: 'center',
                                        alignSelf: 'center',
                                        justifyContent: 'center',
                                        borderRadius: 5,
                                        zIndex: -1
                                    }}>
                                    <View
                                        style={{
                                            height: windowWidth * 0.3,
                                            justifyContent: 'center',
                                            alignItems: 'center'
                                        }}>
                                        <AsyncImage
                                            source={{ uri: item.merchantLogo }}
                                            item={item}
                                            style={{
                                                width: windowWidth * 0.25,
                                                height: windowWidth * 0.25,
                                                borderRadius: 5,
                                                zIndex: -1,
                                            }}
                                        />
                                    </View>
                                </View>
                            </>
                        ) :
                            <View
                                style={{
                                    backgroundColor: Colors.secondaryColor,
                                    width: windowWidth * 0.85,
                                    height: height,
                                    alignItems: 'center',
                                    alignSelf: 'center',
                                    justifyContent: 'center',
                                    borderRadius: 5,
                                    zIndex: -1
                                }}>
                                <View
                                    style={{
                                        height: windowWidth * 0.3,
                                        justifyContent: 'center',
                                        alignItems: 'center'
                                    }}>
                                    <Ionicons
                                        name="fast-food-outline"
                                        // size={45}
                                        size={windowWidth * 0.22}
                                    />
                                </View>
                            </View>
                        }
                    </View>


                    <View>
                        <Text
                            style={{
                                marginTop: 10,
                                marginLeft: 5,
                                // marginBottom: 10,
                                fontSize: 14,
                                fontFamily: 'NunitoSans-Bold',
                                //color: Colors.whiteColor,
                                // borderBottomWidth: 1,
                                // paddingBottom: 7,
                                // height: windowHeight * 0.07,
                            }}
                        // numberOfLines={2}
                        >
                            {item.name}
                        </Text>

                        <Text
                            style={{
                                marginTop: 10,
                                marginLeft: 5,
                                fontSize: 14,
                                fontFamily: 'NunitoSans-SemiBold',
                                //color: Colors.whiteColor,
                            }}>
                            Expires by {moment(item.endDate).format('dddd, Do MMM YYYY')}, {moment(item.endTime).format('hh:mm A')}
                        </Text>

                        <TouchableOpacity
                            onPress={() => {
                                CommonStore.update(s => {
                                    s.selectedStamp = item;
                                    s.userStampAmountSelected = numUserStampsAcquired[item.uniqueId];
                                });
                                console.log('selectedStamp', item)
                                linkTo && linkTo(`${prefix}/outlet/outlet-stamp-details`)
                                // linkTo && linkTo(`${prefix}/outlet-stamp-details`)
                            }}>
                            <Text
                                style={{
                                    //marginTop: 10,
                                    // marginLeft: 10,
                                    fontSize: 16,
                                    fontFamily: 'NunitoSans-Bold',
                                    color: Colors.primaryColor,
                                    marginLeft: 5,
                                    marginTop: 10,
                                }}>
                                CHECK REWARDS
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </>
        )
    };

    const renderRewards = ({ item, index }) => {
        return (
            <>
                <TouchableOpacity onPress={() => {
                    CommonStore.update(s => {
                        s.selectedTaggableVoucherView = item;
                    });
                    linkTo && linkTo(`${prefix}/outlet/outlet-reward-details`)
                }}
                    style={{
                        backgroundColor: 'white',
                        // flex: 0.5,
                        width: '45%',
                        // marginRight: 5,
                        borderRadius: 5,
                        padding: 10,
                        margin: 10,
                    }}>
                    <View style={{ alignItems: 'center' }}>
                        {item.image ? (
                            <>
                                <AsyncImage
                                    source={{ uri: item.image }}
                                    item={item}
                                    style={{
                                        width: windowWidth * 0.3,
                                        height: windowHeight * 0.15,
                                        borderRadius: 5,
                                    }}
                                />
                            </>
                        ) :
                            <View style={{ backgroundColor: Colors.secondaryColor, width: windowWidth * 0.3, height: windowHeight * 0.15, alignItems: 'center', alignSelf: 'center', justifyContent: 'center', borderRadius: 5, }}>
                                <Ionicons
                                    name="fast-food-outline"
                                    // size={45}
                                    size={windowHeight * 0.05}
                                />
                            </View>
                        }
                    </View>
                    <View stlye={{}}>
                        <Text style={{
                            position: 'absolute', bottom: 5, left: 9, backgroundColor: 'white', paddingVertical: 1, paddingHorizontal: 4, borderRadius: 5, fontSize: 14, fontFamily: 'NunitoSans-Bold',
                            shadowOffset: {
                                width: 0,
                                height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                        }}>
                            {item.voucherPointsRequired} pts
                        </Text>
                    </View>
                    <View>
                        <View style={{ height: 50, }}>
                            <Text
                                style={{
                                    marginTop: 10,
                                    marginLeft: 5,
                                    marginBottom: 10,
                                    fontSize: 12,
                                    fontFamily: 'NunitoSans-Bold',
                                    //color: Colors.whiteColor,
                                    // borderBottomWidth: 1,
                                    // paddingBottom: 7,
                                    // height: windowHeight * 0.07,
                                }}
                                numberOfLines={2}
                            >
                                {item.campaignName}
                            </Text>
                        </View>

                        {/* <Text
                            style={{
                                marginTop: 5,
                                // marginLeft: 10,
                                fontSize: 12,
                                fontFamily: 'NunitoSans-SemiBold',
                                //color: Colors.whiteColor,
                            }}>
                            time:
                        </Text> */}
                        <View style={{ flexDirection: 'row', paddingTop: 5, alignItems: 'center', borderTopWidth: 1, }}>
                            <AntDesign name='clockcircleo' />

                            <Text
                                style={{
                                    //marginTop: 10,
                                    // marginLeft: 10,
                                    fontSize: 12,
                                    fontFamily: 'NunitoSans-Regular',
                                    //color: Colors.whiteColor,
                                    marginLeft: 8,
                                    marginTop: 5,
                                }}>
                                Exp. by {moment(item.promoDateEnd).format('dddd, Do MMM YYYY')}, {moment(item.promoTimeEnd).format('hh:mm A')}
                            </Text>
                        </View>
                    </View>
                </TouchableOpacity >
            </>
        )
    };

    useEffect(() => {
        CommonStore.update(s => {
            s.routeName = route.name;
        });
    }, [route]);

    const renderSearch = (item) => {
        return (
            <View style={{ flexDirection: "column" }}>
                <Text style={{ fontWeight: "700", fontSize: 14, marginBottom: 5 }}>
                    {item.structured_formatting.main_text}
                </Text>

                <Text style={{ fontSize: 12, color: "grey" }}>{item.description}</Text>
            </View>
        );
    };

    const toggleEditComponent = () => {
        setEditFlag(!editFlag);
    };

    function renderEditButton() {
        return (
            <View style={{ flexDirection: 'row', justifyContent: 'flex-end', alignItems: 'center' }}>
                <TouchableOpacity
                    style={{
                        alignitems: 'center',
                        justifyContent: 'flex-end',
                        borderWidth: 1,
                        borderRadius: 10,
                        borderColor: Colors.whiteColor,
                        backgroundColor: Colors.primaryColor,
                        padding: 7,
                        marginTop: 10,
                    }}
                    //Redirect to Edit Page
                    onPress={() => toggleEditComponent()}>
                    {/* <Text
                        style={{
                            fontSize: 14,
                            fontFamily: 'NunitoSans-SemiBold',
                            color: Colors.whiteColor,
                            textAlign: 'left',
                        }}>
                        Edit
                    </Text> */}
                    <MaterialIcons
                        name="edit"
                        size={20}
                        color={Colors.whiteColor}
                    />
                </TouchableOpacity>
            </View>
        );
    }

    function renderSaveButton() {
        return (
            <>
                <View style={{ flexDirection: 'row', justifyContent: 'flex-end', alignItems: 'center' }}>
                    <TouchableOpacity
                        style={{
                            alignitems: 'center',
                            justifyContent: 'center',
                            borderWidth: 1,
                            borderRadius: 10,
                            borderColor: Colors.whiteColor,
                            backgroundColor: Colors.primaryColor,
                            padding: 10,
                            marginRight: 5,
                        }}
                        onPress={() => {
                            console.log("[Waiting for Implementation]Save Info")
                            editUserDetailsWeb();
                        }}>
                        {/* <Text
                            style={{
                                fontSize: 14,
                                fontFamily: 'NunitoSans-SemiBold',
                                color: Colors.whiteColor,
                                textAlign: 'left',
                            }}>
                            Save
                        </Text> */}
                        <MaterialIcons
                            name="save"
                            size={20}
                            color={Colors.whiteColor}
                        />

                    </TouchableOpacity>

                    <TouchableOpacity
                        style={{
                            alignitems: 'center',
                            justifyContent: 'center',
                            borderWidth: 1,
                            borderRadius: 10,
                            borderColor: Colors.whiteColor,
                            backgroundColor: Colors.secondaryColor,
                            padding: 10,
                            marginVertical: '10%'
                        }}
                        onPress={() => toggleEditComponent()}>

                        {/* <Text
                            style={{
                                fontSize: 14,
                                fontFamily: 'NunitoSans-SemiBold',
                                color: Colors.whiteColor,
                                textAlign: 'left',
                            }}>
                            Cancel
                        </Text> */}
                        <MaterialIcons
                            name="keyboard-backspace"
                            size={20}
                            color={Colors.whiteColor}
                        />
                    </TouchableOpacity>
                </View>
            </>
        )
    }

    function renderRewardsComponent() {
        return (
            <>
                <View style={{ marginTop: '2%', alignContent: 'center', width: isMobile() ? '100%' : '80%', alignSelf: 'center', height: windowHeight * 0.27, paddingBottom: 10, }}>
                    <View
                        style={{
                            flexDirection: 'row',
                            padding: 10,
                            marginTop: 20,
                            borderRadius: 5,
                        }}>
                        <Text
                            style={{
                                fontSize: isMobile() ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                                width: '42.5%',
                            }}>
                            Action
                        </Text>
                        <Text
                            style={{
                                fontSize: isMobile() ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                                width: '42.5%',
                            }}>
                            Date
                        </Text>
                        <Text
                            style={{
                                fontSize: isMobile() ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                                width: '15%',
                                textAlign: 'center',
                            }}>
                            Status
                        </Text>
                    </View>
                    {/* List */}

                    <FlatList
                        showsVerticalScrollIndicator={false}
                        data={userPointsTransactions}
                        // extraData={selectedCustomerLCCTransactions}
                        renderItem={renderViewHistory}
                        keyExtractor={(item, index) => index}
                        contentContainerStyle={{}}
                    />
                </View>

                {/* <View style={{ marginTop: '5%', alignItems: 'center', justifyContent: 'center', width: isMobile() ? '100%' : '80%', alignSelf: 'center', height: windowHeight * 0.2, }}>
                        <Text style={{
                            fontFamily: 'NunitoSans-Bold',
                            color: 'black',
                            fontSize: 18,
                            textAlign: 'center',
                        }}>
                            Available Rewards
                        </Text>
                        {availableTaggableVouchers && availableTaggableVouchers.length > 0 ? (
                            <>
                                {availableTaggableVouchers && availableTaggableVouchers.length > 0 && (
                                    <View style={{ height: '100%', width: '100%', paddingBottom: 10, marginTop: 10, }}>
                                        <FlatList
                                            showsVerticalScrollIndicator={false}
                                            data={availableTaggableVouchers}
                                            style={{}}
                                            //extraData={availableTaggableVouchers}
                                            renderItem={renderItem}
                                            keyExtractor={(item, index) => String(index)}
                                        />
                                    </View>
                                )}
                            </>
                        ) : (
                            <View
                                style={{
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    // marginTop: 30,
                                    alignSelf: 'center',
                                }}>
                                <Text style={{
                                    fontFamily: 'NunitoSans-Regular',
                                    color: Colors.descriptionColor,
                                    fontSize: 14,
                                    textAlign: 'center',
                                    marginTop: '2%',
                                }}>
                                    This outlet currently don't have any rewards available
                                </Text>
                            </View>
                        )}

                    </View> */}
                <View style={{
                    flexDirection: 'row',
                    marginTop: '5%',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    width: isMobile() ? '100%' : '80%',
                    alignSelf: 'center',
                }}>
                    <View style={{
                        flexDirection: 'column',
                        marginTop: '2%',
                    }}>
                        <View style={{ justifyContent: 'flex-start' }}>
                            <Text style={{
                                fontFamily: 'NunitoSans-Bold',
                                color: 'black',
                                fontSize: 14,
                                textAlign: 'left',
                            }}>
                                Receive SMS Rewards
                            </Text>
                        </View>
                    </View>
                    <View style={{
                        flexDirection: 'column',
                        marginTop: '2%',
                        //justifyContent: 'space-evenly'
                    }}>
                        <View style={{ justifyContent: 'flex-end' }}>
                            <Switch
                                width={35}
                                height={20}
                                handleDiameter={30}
                                uncheckedIcon={false}
                                checkedIcon={false}
                                boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                                value={!blockSMS}
                                onValueChange={async value => {
                                    setBlockSMS(!value);

                                    if (loyaltyHistoryCrmUser && loyaltyHistoryCrmUser.uniqueId) {
                                        const body = {
                                            crmUserId: loyaltyHistoryCrmUser.uniqueId,
                                            blockSMS: !value,

                                            blockSMSFromUserSide: !value,
                                        };

                                        ApiClient.POST(API.switchCRMUserBlockSMSFromUserWeb, body).then((result) => {
                                            if (result && result.status === "success") {

                                            }
                                        });
                                    }
                                }}
                            // onChange={async () => {

                            // }}
                            />
                        </View>
                    </View>
                </View>

                <View style={{
                    flexDirection: 'row',
                    // marginTop: '2%',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    width: isMobile() ? '100%' : '80%',
                    alignSelf: 'center',
                }}>
                    {/* <View style={{
                            flexDirection: 'column',
                            marginTop: '2%',
                        }}>
                            <View style={{ justifyContent: 'flex-start' }}>
                                <Text style={{
                                    fontFamily: 'NunitoSans-Bold',
                                    color: 'black',
                                    fontSize: 14,
                                    textAlign: 'left',
                                }}>
                                    Receive Email Rewards
                                </Text>
                            </View>
                        </View> */}
                    {/* <View style={{
                            flexDirection: 'column',
                            marginTop: '2%',
                            //justifyContent: 'space-evenly'
                        }}>
                            <View style={{ justifyContent: 'flex-end' }}>
                                <Switch
                                    width={35}
                                    height={20}
                                    handleDiameter={30}
                                    uncheckedIcon={false}
                                    checkedIcon={false}
                                    boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                    activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                                    value={!blockEmail}
                                    onValueChange={async value => {
                                        setBlockEmail(!value);

                                        if (loyaltyHistoryCrmUser && loyaltyHistoryCrmUser.uniqueId) {
                                            const body = {
                                                crmUserId: loyaltyHistoryCrmUser.uniqueId,
                                                blockEmail: !value,

                                                blockEmailFromUserSide: !value,
                                            };

                                            ApiClient.POST(API.switchCRMUserBlockEmailFromUserWeb, body).then((result) => {
                                                if (result && result.status === "success") {

                                                }
                                            });
                                        }
                                    }}
                                // onChange={async () => {

                                // }}
                                />
                            </View>
                        </View> */}
                </View>
                <View style={{ flexDirection: 'row', marginTop: '5%', paddingTop: 10, }}>
                    <TouchableOpacity
                        style={{
                            alignItems: 'flex-start',
                            justifyContent: 'center',
                            borderWidth: 1,
                            borderRadius: 10,
                            borderColor: Colors.primaryColor,
                            backgroundColor: rewardSection === REWARD_SECTION.REWARDS ? Colors.primaryColor : 'white',
                            padding: 6,
                            marginRight: 5,
                        }}
                        onPress={() =>
                            // setRewardSection(REWARD_SECTION.REWARDS)
                            TempStore.update(s => {
                                s.rewardSection = REWARD_SECTION.REWARDS;
                            })
                        }>
                        <Text
                            style={{
                                fontSize: 14,
                                fontFamily: 'NunitoSans-SemiBold',
                                backgroundColor: rewardSection === REWARD_SECTION.REWARDS ? Colors.primaryColor : Colors.whiteColor,
                                color: rewardSection === REWARD_SECTION.REWARDS ? Colors.whiteColor : Colors.primaryColor,
                                textAlign: 'left',
                            }}>
                            Rewards
                        </Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={{
                            alignItems: 'flex-start',
                            justifyContent: 'center',
                            borderWidth: 1,
                            borderRadius: 10,
                            borderColor: Colors.primaryColor,
                            backgroundColor: rewardSection === REWARD_SECTION.STAMP_CARDS ? Colors.primaryColor : 'white',
                            padding: 6,
                            marginRight: 5,
                        }}
                        onPress={() =>
                            // setRewardSection(REWARD_SECTION.STAMP_CARDS)
                            TempStore.update(s => {
                                s.rewardSection = REWARD_SECTION.STAMP_CARDS;
                            })
                        }>
                        <Text
                            style={{
                                fontSize: 14,
                                fontFamily: 'NunitoSans-SemiBold',
                                backgroundColor: rewardSection === REWARD_SECTION.STAMP_CARDS ? Colors.primaryColor : Colors.whiteColor,
                                color: rewardSection === REWARD_SECTION.STAMP_CARDS ? Colors.whiteColor : Colors.primaryColor,
                                textAlign: 'left',
                            }}>
                            Stamps
                        </Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={{
                            alignItems: 'flex-start',
                            justifyContent: 'center',
                            borderWidth: 1,
                            borderRadius: 10,
                            borderColor: Colors.primaryColor,
                            backgroundColor: rewardSection === REWARD_SECTION.CREDIT ? Colors.primaryColor : 'white',
                            padding: 6,
                            marginRight: 5,
                        }}
                        onPress={() =>
                            // setRewardSection(REWARD_SECTION.CREDIT)
                            TempStore.update(s => {
                                s.rewardSection = REWARD_SECTION.CREDIT;
                            })
                        }>
                        <Text
                            style={{
                                fontSize: 14,
                                fontFamily: 'NunitoSans-SemiBold',
                                backgroundColor: rewardSection === REWARD_SECTION.CREDIT ? Colors.primaryColor : Colors.whiteColor,
                                color: rewardSection === REWARD_SECTION.CREDIT ? Colors.whiteColor : Colors.primaryColor,
                                textAlign: 'left',
                            }}>
                            Topup Credit
                        </Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={{
                            alignItems: 'flex-start',
                            justifyContent: 'center',
                            borderWidth: 1,
                            borderRadius: 10,
                            borderColor: Colors.primaryColor,
                            backgroundColor: rewardSection === REWARD_SECTION.AVAILABLE_REWARDS ? Colors.primaryColor : 'white',
                            padding: 6,
                        }}
                        onPress={() =>
                            // setRewardSection(REWARD_SECTION.AVAILABLE_REWARDS)
                            TempStore.update(s => {
                                s.rewardSection = REWARD_SECTION.AVAILABLE_REWARDS;
                            })
                        }>
                        <Text
                            style={{
                                fontSize: 14,
                                fontFamily: 'NunitoSans-SemiBold',
                                color: rewardSection === REWARD_SECTION.AVAILABLE_REWARDS ? Colors.whiteColor : Colors.primaryColor,
                                textAlign: 'left',
                            }}>
                            My Rewards
                        </Text>
                    </TouchableOpacity>
                </View>

                {rewardSection === REWARD_SECTION.REWARDS ?
                    <View style={{ alignItems: 'center', justifyContent: 'center', width: isMobile() ? '100%' : '80%', alignSelf: 'center', }}>
                        {claimVoucherList && claimVoucherList.length > 0 && claimVoucherList.some(item => item.voucherPointsRequired === undefined || item.voucherPointsRequired >= 0) ? (
                            <>
                                {claimVoucherList && claimVoucherList.length > 0 && (
                                    <View style={{ height: '100%', width: '100%', marginTop: 10, marginBottom: 10, }}>
                                        <FlatList
                                            showsVerticalScrollIndicator={false}
                                            data={claimVoucherList.filter(item => item.voucherPointsRequired === undefined || item.voucherPointsRequired >= 0)}
                                            style={{}}
                                            //extraData={availableTaggableVouchers}
                                            renderItem={renderItem}
                                            keyExtractor={(item, index) => String(index)}
                                            numColumns={2}
                                            contentContainerStyle={{
                                                width: windowWidth * 0.9,
                                                // backgroundColor: 'blue'
                                            }}
                                        />
                                    </View>
                                )}
                            </>
                        ) : (
                            <View
                                style={{
                                    width: windowWidth * 0.9,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    paddingTop: 20,
                                    alignSelf: 'center',
                                }}>
                                <Text style={{
                                    fontFamily: 'NunitoSans-Regular',
                                    color: Colors.descriptionColor,
                                    fontSize: 14,
                                    textAlign: 'center',
                                    marginTop: '8%',
                                    marginBottom: '20%'
                                }}>
                                    This outlet currently don't have any rewards available
                                </Text>
                            </View>
                        )}

                    </View>
                    :
                    <></>}

                {rewardSection === REWARD_SECTION.STAMP_CARDS ?
                    <View
                        style={{
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: isMobile() ? '100%' : '80%',
                            alignSelf: 'center',
                        }}>
                        {selectedOutletLoyaltyStamps && selectedOutletLoyaltyStamps.length > 0 ? (
                            <>
                                {selectedOutletLoyaltyStamps && selectedOutletLoyaltyStamps.length > 0 && filteredUserLoyaltyStamps.length > 0 ? (
                                    <View
                                        style={{
                                            height: '100%',
                                            width: '100%',
                                            marginTop: 10,
                                            marginBottom: 10,
                                        }}>
                                        <FlatList
                                            showsVerticalScrollIndicator={false}
                                            data={filteredUserLoyaltyStamps}
                                            style={{}}
                                            //extraData={availableTaggableVouchers}
                                            renderItem={renderItem2}
                                            keyExtractor={(item, index) => String(index)}
                                            // numColumns={2}
                                            contentContainerStyle={{
                                                width: windowWidth * 0.9,
                                                // backgroundColor: 'blue'
                                            }}
                                        />
                                    </View>
                                )
                                    :
                                    (
                                        <View
                                            style={{
                                                width: windowWidth * 0.9,
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                                // marginTop: 30,
                                                alignSelf: 'center',
                                            }}>
                                            <Text style={{
                                                fontFamily: 'NunitoSans-Regular',
                                                color: Colors.descriptionColor,
                                                fontSize: 14,
                                                textAlign: 'center',
                                                marginTop: '8%',
                                                marginBottom: '20%'
                                            }}>
                                                You do not own any stamp cards for this outlet.
                                            </Text>
                                        </View>
                                    )}
                            </>
                        ) : (
                            <View
                                style={{
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    // marginTop: 30,
                                    alignSelf: 'center',

                                    width: windowWidth * 0.9,
                                }}>
                                <Text style={{
                                    fontFamily: 'NunitoSans-Regular',
                                    color: Colors.descriptionColor,
                                    fontSize: 14,
                                    textAlign: 'center',
                                    marginTop: '8%',
                                    marginBottom: '20%'
                                }}>
                                    {'There are no stamp cards available \nfor this outlet at the moment.'}
                                </Text>
                            </View>
                        )}

                    </View>
                    :
                    <></>}

                {/* ////////////////////////////////////////////// */}

                {/* 2024-10-17 - credit */}

                {rewardSection === REWARD_SECTION.CREDIT ?
                    <View style={{ alignItems: 'center', justifyContent: 'center', width: isMobile() ? '100%' : '80%', alignSelf: 'center', }}>
                        {topupCreditTypes && topupCreditTypes.length > 0 ? (
                            <>
                                {topupCreditTypes && topupCreditTypes.length > 0 && (
                                    <View style={{ height: '100%', width: '100%', marginTop: 10, marginBottom: 10, }}>
                                        <FlatList
                                            showsVerticalScrollIndicator={false}
                                            data={topupCreditTypes}
                                            style={{}}
                                            //extraData={availableTaggableVouchers}
                                            renderItem={renderTopupCredit}
                                            keyExtractor={(item, index) => String(index)}
                                            numColumns={2}
                                            contentContainerStyle={{
                                                width: windowWidth * 0.9,
                                                // backgroundColor: 'blue'
                                            }}
                                        />
                                    </View>
                                )}
                            </>
                        ) : (
                            <View
                                style={{
                                    width: windowWidth * 0.9,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    paddingTop: 20,
                                    alignSelf: 'center',
                                }}>
                                <Text style={{
                                    fontFamily: 'NunitoSans-Regular',
                                    color: Colors.descriptionColor,
                                    fontSize: 14,
                                    textAlign: 'center',
                                    marginTop: '8%',
                                    marginBottom: '20%'
                                }}>
                                    As of now credit is not available for this outlet.
                                </Text>
                            </View>
                        )}

                    </View>
                    :
                    <></>}

                {/* ////////////////////////////////////////////// */}

                {rewardSection === REWARD_SECTION.AVAILABLE_REWARDS ?
                    <View style={{ alignItems: 'center', justifyContent: 'center', width: isMobile() ? '100%' : '80%', alignSelf: 'center', }}>
                        {availableTaggableVouchers && availableTaggableVouchers.length > 0 ? (
                            <>
                                {availableTaggableVouchers && availableTaggableVouchers.length > 0 && (
                                    <View style={{ height: '100%', width: '100%', marginTop: 10, marginBottom: 10, }}>
                                        <FlatList
                                            showsVerticalScrollIndicator={false}
                                            data={[...availableTaggableVouchers].sort((a, b) => {
                                                const dateA = moment.unix(a.promoDateEnd).format('YYYY-MM-DD');
                                                const timeA = moment.unix(a.promoTimeEnd).format('HH:mm:ss');

                                                const dateB = moment.unix(b.promoDateEnd).format('YYYY-MM-DD');
                                                const timeB = moment.unix(b.promoTimeEnd).format('HH:mm:ss');

                                                const momentA = moment(`${dateA} ${timeA}`, 'YYYY-MM-DD HH:mm:ss');
                                                const momentB = moment(`${dateB} ${timeB}`, 'YYYY-MM-DD HH:mm:ss');

                                                return momentA.diff(momentB);
                                            })}
                                            style={{}}
                                            //extraData={availableTaggableVouchers}
                                            renderItem={renderItem}
                                            keyExtractor={(item, index) => String(index)}
                                            numColumns={2}
                                            contentContainerStyle={{
                                                width: windowWidth * 0.9,
                                                // backgroundColor: 'blue'
                                            }}
                                        />
                                    </View>
                                )}
                            </>
                        ) : (
                            <View
                                style={{
                                    width: windowWidth * 0.9,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    paddingTop: 20,
                                    alignSelf: 'center',
                                }}>
                                <Text style={{
                                    fontFamily: 'NunitoSans-Regular',
                                    color: Colors.descriptionColor,
                                    fontSize: 14,
                                    textAlign: 'center',
                                    marginTop: '8%',
                                    marginBottom: '20%'
                                }}>
                                    This outlet currently don't have any rewards available
                                </Text>
                            </View>
                        )}

                    </View>
                    :
                    <></>}

                <View style={{ height: 20 }} />
            </>
        );
    }

    function renderEditComponent() {

        return (
            <>
                <View style={{ marginTop: '2%', alignContent: 'center', width: isMobile() ? '100%' : '80%', alignSelf: 'center', height: windowHeight * 0.27, paddingBottom: 10, }}>

                    <View style={{ width: windowWidth * 0.9, marginVertical: 10, borderTopWidth: 0.3, opacity: 0.2, borderColor: Colors.descriptionColor }}></View>
                    {/* name & phone */}
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                        <View style={{ width: '100%' }}>
                            <Text style={[
                                styles.payment
                            ]}>
                                Name
                            </Text>

                            <TextInput
                                onChangeText={(text) => { setUserName(text) }}
                                style={[styles.textInput, { width: '93%' }]}
                                placeholder="Name"
                                value={userName}
                            />
                        </View>

                        {/* <View style={{ width: '50%' }}>
                            <Text style={[
                                styles.payment
                            ]}>
                                Phone
                            </Text>

                            <TextInput
                                onChangeText={(text) => { setEpPhoneToTemp(text) }}
                                style={[styles.textInput, , { width: '85%' }]}
                                placeholder="Phone"
                                value={epPhoneToTemp}
                            />
                        </View> */}
                    </View>

                    {/* email */}
                    <View>
                        <Text
                            style={[
                                styles.payment
                            ]}>
                            Email Second
                        </Text>

                        <TextInput
                            onChangeText={(text) => { setCustomerEmailSecond(text) }}
                            style={[styles.textInput, { width: '93%' }]}
                            placeholder="Email"
                            value={customerEmailSecond}
                        />
                    </View>

                    <View>
                        <Text
                            style={[
                                styles.payment
                            ]}>
                            Date of birth
                        </Text>

                        <View style={[styles.textInput, { width: '93%', paddingTop: 10, paddingLeft: 0, }]} >
                            <DatePicker
                                selected={userDob}
                                onChange={handleDateChange}
                                showYearDropdown
                                showMonthDropdown
                                dropdownMode='select'

                                dateFormat="MMM d, yyyy"
                                placeholderText="Date of birth"
                                maxDate={new Date()}
                            />
                        </View>
                    </View>

                    {/* id type & id */}
                    {/* <View style={{ flexDirection: 'row', justifyContent: 'space-between', }}>
                        <View style={{ width: '35%' }}>
                            <Text style={[
                            styles.payment,
                            {
                                // paddingHorizontal: 24,
                                marginBottom:10,
                            },
                            ]}>
                            ID Type
                            </Text>
                            <DropDownPicker
                            style={{
                                backgroundColor: Colors.fieldtBgColor,
                                marginRight:'20%',
                                height: 50,
                                borderRadius: 5,
                                borderWidth: 1,
                                borderColor: "#E5E5E5",
                                flexDirection: "row",

                                shadowColor: "#000",
                                shadowOffset: {
                                width: 0,
                                height: 1,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 2.22,
                                elevation: 2,
                                
                            }}
                            dropDownContainerStyle={{
                                width: '93%',
                                backgroundColor: Colors.fieldtBgColor,
                                borderColor: "#E5E5E5",
                            }}
                            labelStyle={{
                                marginLeft: 5,
                                flexDirection: "row",
                            }}
                            textStyle={{
                                fontFamily: 'NunitoSans-Regular',

                                marginLeft: 5,
                                paddingVertical: 15,
                                paddingLeft: 15,
                                flexDirection: "row",
                            }}
                            selectedItemContainerStyle={{
                                flexDirection: "row",
                            }}

                            showArrowIcon={true}
                            ArrowDownIconComponent={({ style }) => (
                                <Ionicons
                                size={25}
                                color={Colors.fieldtTxtColor}
                                style={{ paddingHorizontal: 5, marginTop: 10 }}
                                name="chevron-down-outline"
                                />
                            )}
                            ArrowUpIconComponent={({ style }) => (
                                <Ionicons
                                size={25}
                                color={Colors.fieldtTxtColor}
                                style={{ paddingHorizontal: 5, marginTop: 10 }}
                                name="chevron-up-outline"
                                />
                            )}

                            showTickIcon={true}
                            TickIconComponent={({ press }) => (
                                <Ionicons
                                style={{ paddingHorizontal: 5, marginTop: 7.5 }}
                                color={
                                press ? Colors.fieldtBgColor : Colors.primaryColor
                                }
                                name={'md-checkbox'}
                                size={25}
                                />
                                )}
                                placeholder={'Select an ID Type'}
                                placeholderStyle={{
                                color: Colors.fieldtTxtColor
                                }}
                                searchable
                                searchableStyle={{
                                paddingHorizontal: windowWidth * 0.0079,
                                }}
                                value={epIdTypeToTemp}
                                items={idtypeOption}
                                onSelectItem={(item) => {
                                setEpIdTypeToTemp(item.value);
                                }}
                                open={openIdType}
                                setOpen={setOpenIdType}
                            />
                        </View>

                        <View style={{ width: '65%' }}>
                        <Text style={[
                            styles.payment,
                        ]}>
                            ID
                        </Text>
                        <TextInput
                            onChangeText={(text) => { setEpIdToTemp(text) }}
                            style={[styles.textInput, , { width: '90%' }]}
                            placeholder="ID"
                            value={epIdToTemp}
                        />
                        </View>
                    </View> */}

                    {/* tin */}
                    {/* <View style={{ marginTop: 5, zIndex: -1, }}>
                        <Text
                        style={[
                            styles.payment
                        ]}
                        >
                        TIN
                        </Text>
                        <TextInput
                        onChangeText={(text) => { setEpTinToTemp(text) }}
                        style={[styles.textInput, , { width: '93%' }]}
                        placeholder="TIN"
                        value={epTinToTemp}
                        />
                    </View> */}

                    {/* address */}
                    {/* <View style={{ marginTop: 5, zIndex: -1, }}>
                        <Text
                        style={[
                            styles.payment,
                        ]}
                        >
                        Address
                        </Text>
                        <TextInput
                        onChangeText={(text) => { setEpAddr1ToTemp(text) }}
                        style={[styles.textInput, , { width: '93%' }]}
                        placeholder="Address"
                        value={epAddr1ToTemp}
                        />
                    </View> */}

                    {/* city & zip code */}
                    {/* <View style={{ flexDirection: 'row', justifyContent: 'space-between', zIndex: -1, }}>
                        <View style={{ width: '50%' }}>
                            <Text style={[
                                styles.payment,
                            ]}>
                                City
                            </Text>
                            <TextInput
                                onChangeText={(text) => { setEpCityToTemp(text) }}
                                style={[styles.textInput, { width: '85%' }]}
                                placeholder="City"
                                value={epCityToTemp}
                            />
                        </View>

                        <View style={{ width: '50%' }}>
                            <Text style={[
                                styles.payment
                            ]}>
                                Zip Code
                            </Text>

                            <TextInput
                                onChangeText={(text) => { setEpCodeToTemp(text) }}
                                style={[styles.textInput, , { width: '85%' }]}
                                placeholder="Zip Code"
                                value={epCodeToTemp}
                            />
                        </View>
                        <View style={{ height: 200 }} />
                    </View> */}
                </View>
            </>
        );
    }

    /* member */
    const editUserDetailsWeb = async (isAutoPush = false) => {
        if (
            // !customerAvatar ||
            // !customerName ||
            !userNumber ||
            !name
            // !customerEmail
            // !customerAddress
        ) {
            Alert.alert(
                'Error',
                'Please fill in all required information:\nName\nContact number',
                [{ text: 'OK', onPress: () => { } }],
                { cancelable: false },
            );

        } else {
            ///////////////////////////////////

            var parsedPhone = userNumber.startsWith('+') ? userNumber.slice(1) : userNumber;
            if (parsedPhone.length > 0 && !parsedPhone.startsWith('6')) {
                parsedPhone = `6${parsedPhone}`;
            }

            var sameUser = crmUsers.find(crmUser => {
                if (email && crmUser.email === email) {
                    return true;
                }

                if (parsedPhone && crmUser.number === parsedPhone) {
                    return true;
                }
            });

            if (sameUser) {
                Alert.alert(
                    'Info',
                    'Existing email and/or phone number found, please try another one.',
                    [{ text: 'OK', onPress: () => { } }],
                    { cancelable: false },
                );
                return;
            }

            // if (
            //     // currOutlet.ei
            //     true
            // ) {
            //     if (
            //         customerEmail === '' ||
            //         customerIDNum === '' ||
            //         customerTIN === '' ||
            //         customerEINStreet === '' ||
            //         customerEINCity === '' ||
            //         customerEINState === '' ||
            //         customerEINPostcode === ''
            //     ) {
            //         Alert.alert(
            //             'Error',
            //             'Please fill in all required information:\nName\nContact number\nID\nTIN\nStreet\nCity\nState\nPost code',
            //             [{ text: 'OK', onPress: () => { } }],
            //             { cancelable: false },
            //         );
            //         return;
            //     }
            // }



            CommonStore.update((s) => {
                s.isLoading = true;
            });
            // upload image

            var profileImageImagePath = '';
            var profileImageCommonIdLocal = uuidv4();

            // if (image && imageType) {
            //   // promotionCommonIdLocal = uuidv4();

            //   profileImageImagePath = await uploadImageToFirebaseStorage(
            //     {
            //       uri: image,
            //       type: imageType,
            //     },
            //     `/merchant/${merchantId}/crm/user/${profileImageCommonIdLocal}/image${imageType}`,
            //   );
            // }

            // means new item
            var body = {
                merchantId: selectedMerchant.uniqueId,
                merchantName: selectedMerchant.name,
                outletId: selectedOutlet.uniqueId,

                avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(
                    name
                ).replace(/%20/g, "+")}`,
                isImageChanged: false,
                dob: userDob, // save edit
                email: userEmail || '', //save edit
                gender: gender,
                name: userName, // save edit
                number: parsedPhone,
                uniqueName: name || '',

                epNameTo: name || '',
                epPhoneTo: userNumber || '',

                epAddr1To: epAddr1To || '',
                epCityTo: epCityTo || '',
                epCodeTo: epCodeTo || '',
                epStateTo: epStateTo || '',

                // emailSecond: customerEmail || '',
                tin: tin || '',
                eiIdType: eiIdType || '',
                eiId: eiId || '',

                address: address,
                // lat: parseFloat(customerAddressLat),
                // lng: parseFloat(customerAddressLng),

                emailSecond: customerEmailSecond,
                // numberSecond: customerPhoneSecond,
                // addressSecond: customerAddressSecond,
                // latSecond: parseFloat(customerAddressLatSecond),
                // lngSecond: parseFloat(customerAddressLngSecond),

                timeline: {},

                commonId: profileImageCommonIdLocal,
            };

            // console.log(body, 'here' + isLoading);

            ApiClient.POST(API.editUserDetailsWeb, body, false)
                .then((result) => {
                    if (result && result.status === 'success') {
                        alert(
                            'Success, Customer has been saved.',
                            [
                            ],
                        );
                        toggleEditComponent();
                    }
                })
                .catch((err) => {
                    // console.log(err, 'here here');
                });
            CommonStore.update((s) => {
                s.isLoading = false;
            });
            UserStore.update((s) => {
                s.name = userName;
                s.email = userEmail;
                s.dob = userDob;
                s.emailSecond = customerEmailSecond;
            });
        }
    };

    //////////////////////////////////////////

    // 2024-10-17 - topup credit

    const renderTopupCredit = ({ item, index }) => {
        return (
            <>
                <TouchableOpacity onPress={() => {
                    CommonStore.update(s => {
                        s.selectedTopupCreditView = item;
                    });
                    linkTo && linkTo(`${prefix}/outlet/outlet-credit-details`)
                }}
                    style={{
                        backgroundColor: 'white',
                        // flex: 0.5,
                        // width: windowWidth * 0.35,
                        width: '45%',
                        borderRadius: 5,
                        padding: 10,
                        margin: 10,
                    }}>
                    <View style={{ alignItems: 'center' }}>
                        {item.image ? (
                            <>
                                <AsyncImage
                                    source={{ uri: item.image }}
                                    item={item}
                                    style={{
                                        width: windowWidth * 0.3,
                                        height: windowHeight * 0.15,
                                        borderRadius: 5,
                                    }}
                                />
                            </>
                        ) :
                            <View style={{ backgroundColor: Colors.secondaryColor, width: windowWidth * 0.3, height: windowHeight * 0.15, alignItems: 'center', alignSelf: 'center', justifyContent: 'center', borderRadius: 5, }}>
                                <Ionicons
                                    name="fast-food-outline"
                                    // size={45}
                                    size={windowHeight * 0.05}
                                />
                            </View>
                        }
                    </View>
                    <View>
                        <View style={{ height: 50, }}>
                            <Text
                                style={{
                                    marginTop: 10,
                                    marginLeft: 5,
                                    marginBottom: 10,
                                    fontSize: 12,
                                    fontFamily: 'NunitoSans-Bold',
                                    //color: Colors.whiteColor,
                                    // borderBottomWidth: 1,
                                    // paddingBottom: 7,
                                    // height: windowHeight * 0.07,
                                }}
                                numberOfLines={1}
                            >
                                {item.name}
                            </Text>
                        </View>

                        {/* <Text
                            style={{
                                marginTop: 5,
                                // marginLeft: 10,
                                fontSize: 12,
                                fontFamily: 'NunitoSans-SemiBold',
                                //color: Colors.whiteColor,
                            }}>
                            time:
                        </Text> */}
                        <View style={{ flexDirection: 'row', paddingTop: 5, alignItems: 'center', borderTopWidth: 1, }}>
                            <View style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',

                                paddingTop: 5,
                            }}>
                                <AntDesign name='tago' />
                            </View>

                            <Text
                                style={{
                                    //marginTop: 10,
                                    // marginLeft: 10,
                                    fontSize: 12,
                                    fontFamily: 'NunitoSans-Regular',
                                    //color: Colors.whiteColor,
                                    marginLeft: 8,
                                    marginTop: 5,
                                }}>
                                {/* Exp. by {moment(item.promoDateEnd).format('dddd, Do MMM YYYY')}, {moment(item.promoTimeEnd).format('hh:mm A')} */}
                                RM {BigNumber(item.price).dp(2).toFixed(2)}
                            </Text>
                        </View>
                    </View>
                </TouchableOpacity >
            </>
        )
    };

    const formatMalaysianNumber = (number) => {
        if (!number) {
            throw new Error("Phone number is required.");
        }

        // Remove any non-numeric characters
        let formattedNumber = number.replace(/[^0-9]/g, "");

        // Ensure the number starts with "60"
        if (formattedNumber.startsWith("0")) {
            formattedNumber = `60${formattedNumber.slice(1)}`;
        } else if (!formattedNumber.startsWith("60")) {
            throw new Error("Invalid Malaysian number. Must start with 60 or 0.");
        }

        return formattedNumber;
    };

    const sendMessageWA = async ({ to, message }) => {
        try {
            if (!to || !message) {
                console.error("Recipient and message are required.");
                return;
            }

            // Format the phone number
            const formattedNumber = formatMalaysianNumber(to);

            const body = {
                to: formattedNumber,
                message,
            };

            await ApiClient.POST(API.sendMessage, body, false)
                .then((result) => {
                    console.log("Message sent successfully:", result);
                    return result;
                })
                .catch((error) => {
                    console.error("Failed to send message:", error);
                    throw error;
                });
        } catch (error) {
            console.error("Error:", error.message);
        }
    };

    //////////////////////////////////////////

    return (
        <View style={{ height: windowHeight * 0.9, alignItems: 'center', justifyContent: 'center', backgroundColor: 'light-gray', }}>
            {/* <View style={{
                width: isMobile() ? windowWidth : windowWidth,
                height: 160,
                backgroundColor: Colors.darkBgColor,
            }}>
                <Image style={{
                    width: 100,
                    height: 47,
                    alignSelf: 'center',
                    //marginBottom: 25.
                }} resizeMode="contain" source={imgLogo} />
                <Text style={{
                    fontFamily: 'NunitoSans-Bold',
                    color: Colors.whiteColor,
                    fontSize: 30,
                    textAlign: 'center',
                    marginTop: 30,
                    // width: '100%',
                }}>
                    {loyaltyHistoryCrmUser.number ? loyaltyHistoryCrmUser.number : 'N/A'}
                </Text>
            </View> */}
            {selectedOutlet ?
                <>
                    <View style={{
                        justifyContent: 'center',
                        alignContent: 'center',
                        alignItems: 'center',
                        borderRadius: 10,
                        borderColor: '#E5E5E5',

                        // shadowOffset: {
                        //     width: 0,
                        //     height: 2,
                        // },
                        // shadowOpacity: 0.22,
                        // shadowRadius: 3.22,
                        // elevation: 1,
                        padding: 20,
                        marginTop: '25%',
                        marginBottom: '10%',

                        height: windowHeight * 1,
                        width: isMobile() ? windowWidth * 0.9 : windowWidth * 0.4,
                    }}>
                        <ScrollView showsVerticalScrollIndicator={false}>
                            <View style={{
                                backgroundColor: '#f7f7f7', borderRadius: 10, padding: 10,
                                shadowOffset: {
                                    width: 0,
                                    height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                            }}>
                                {(selectedMerchant && selectedMerchant.logo) ?
                                    // <TouchableOpacity onPress={() => {
                                    //     sendMessageWA({
                                    //         to: "0122938501", // Local Malaysian number
                                    //         message: "Hello, this is a test message!", // Message content
                                    //     });
                                    // }}>
                                        <View style={{ alignItems: 'center' }}>
                                            <AsyncImage
                                                source={{ uri: selectedMerchant ? selectedMerchant.logo : '' }}
                                                // item={selectedUserOrder}
                                                style={{ width: 90, height: 90, borderRadius: 10 }}
                                            />
                                        </View>
                                    // </TouchableOpacity>
                                    :
                                    <></>}
                                <View style={{ marginTop: '1%', alignItems: 'center', justifyContent: 'center', }}>
                                    <Text style={{
                                        fontFamily: 'NunitoSans-Bold',
                                        color: 'black',
                                        fontSize: 16,
                                        textAlign: 'center',
                                    }}>
                                        {`${selectedOutlet.name}`}
                                    </Text>

                                    <View style={{
                                        display: 'flex',
                                        flexDirection: 'row',

                                        alignItems: 'flex-start',
                                        justifyContent: 'center',
                                        alignSelf: 'flex-start',
                                        paddingLeft: 5,
                                        marginTop: '2%',
                                    }}>
                                        <View style={{
                                            // alignItems: 'flex-start',
                                            // justifyContent: 'center',
                                            // alignSelf: 'flex-start',
                                            // paddingLeft: 5,
                                            // marginTop: '2%',

                                            alignItems: 'flex-start',

                                            marginRight: 20,
                                        }}>
                                            <Text style={{
                                                fontFamily: 'NunitoSans-Bold',
                                                color: 'black',
                                                fontSize: 16,
                                                textAlign: 'center',
                                            }}>
                                                Points
                                            </Text>
                                            <Text style={{
                                                fontFamily: 'NunitoSans-Bold',
                                                color: 'Black',
                                                fontSize: 16,
                                                textAlign: 'center',
                                            }}>
                                                {userPointsBalance ? userPointsBalance.toFixed(0) : 0}
                                            </Text>
                                        </View>

                                        <View style={{
                                            // alignItems: 'flex-start',
                                            // justifyContent: 'center',
                                            // alignSelf: 'flex-start',
                                            // paddingLeft: 5,
                                            // marginTop: '2%',

                                            alignItems: 'flex-start',

                                            marginRight: 15,
                                        }}>
                                            <Text style={{
                                                fontFamily: 'NunitoSans-Bold',
                                                color: 'black',
                                                fontSize: 16,
                                                textAlign: 'center',
                                            }}>
                                                Credit
                                            </Text>
                                            <Text style={{
                                                fontFamily: 'NunitoSans-Bold',
                                                color: 'Black',
                                                fontSize: 16,
                                                textAlign: 'center',
                                            }}>
                                                {`RM ${BigNumber(userPointsBalanceLCC).toFixed(2)}`}
                                            </Text>
                                        </View>

                                        <View style={{
                                            // alignItems: 'flex-start',
                                            // justifyContent: 'center',
                                            // alignSelf: 'flex-start',
                                            // paddingLeft: 5,
                                            // marginTop: '2%',

                                            alignItems: 'flex-start',

                                            // marginRight: 15,
                                        }}>
                                            <Text style={{
                                                fontFamily: 'NunitoSans-Bold',
                                                color: 'black',
                                                fontSize: 16,
                                                textAlign: 'center',
                                            }}>
                                                Tier
                                            </Text>
                                            <Text style={{
                                                fontFamily: 'NunitoSans-Bold',
                                                color: 'Black',
                                                fontSize: 16,
                                                textAlign: 'center',
                                            }}>
                                                {levelName ? levelName : '-'}
                                            </Text>
                                        </View>
                                    </View>
                                </View>
                            </View>
                            <View style={{
                                flexDirection: 'row',
                                marginTop: '3%',
                                justifyContent: 'space-between'
                            }}>
                                <View style={{
                                    flexDirection: 'column',
                                    marginTop: '3%',
                                    // marginRight: '50%'
                                    //justifyContent: 'space-evenly'
                                }}>
                                    <View style={{ justifyContent: 'flex-start' }}>
                                        <Text style={{
                                            fontFamily: 'NunitoSans-Regular',
                                            color: Colors.descriptionColor,
                                            fontSize: 14,
                                            textAlign: 'left',
                                            marginTop: 0,
                                            marginLeft: 5,
                                            // width: '100%',
                                        }}>
                                            Name
                                        </Text>
                                        <Text style={{
                                            fontFamily: 'NunitoSans-Bold',
                                            color: 'black',
                                            fontSize: 16,
                                            textAlign: 'left',
                                            marginTop: 0,
                                            marginLeft: 5,
                                            // width: '100%',
                                        }}>
                                            {name ? name : '-'}
                                        </Text>
                                    </View>
                                </View>
                                <View style={{
                                    flexDirection: 'column',
                                    marginTop: '3%',
                                    //justifyContent: 'space-evenly'
                                }}>
                                    <View style={{ justifyContent: 'flex-end' }}>
                                        <Text style={{
                                            fontFamily: 'NunitoSans-Regular',
                                            color: Colors.descriptionColor,
                                            fontSize: 14,
                                            textAlign: 'left',
                                            marginTop: 0,
                                            marginLeft: 5,
                                            // width: '100%',
                                        }}>
                                            Phone Number
                                        </Text>
                                        <Text style={{
                                            fontFamily: 'NunitoSans-Bold',
                                            color: 'black',
                                            fontSize: 16,
                                            textAlign: 'right',
                                            marginTop: 0,
                                            marginLeft: 5,
                                            // width: '100%',
                                        }}>
                                            {userNumber ? userNumber : '-'}
                                        </Text>
                                    </View>
                                </View>

                                {editFlag ? renderSaveButton() : renderEditButton()}

                            </View>
                            {editFlag ? renderEditComponent() : renderRewardsComponent()}

                        </ScrollView>
                    </View>
                </>
                :
                <>
                    <View
                        style={{
                            justifyContent: 'center',
                            alignItems: 'center',
                            marginTop: 100,
                            alignSelf: 'center',
                        }}>
                        <Text style={{ fontSize: 14, fontFamily: 'NunitoSans-Regular' }}>
                            You have not visited any outlets
                        </Text>
                        <Ionicons
                            name={'sad-outline'}
                            size={30}
                            style={{
                                marginTop: 10,
                                marginBottom: 20
                            }}
                        />
                    </View>
                </>}
            <View style={{ flex: 1 }}></View>
        </View>

    )
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.highlightColor,
        flexDirection: 'row',
        fontFamily: 'NunitoSans-Regular',
    },
    list: {
        backgroundColor: Colors.whiteColor,
        // width: windowWidth * 0.85,
        // height: windowHeight,
        marginTop: 40,
        marginHorizontal: 30,
        //alignSelf: 'center',
        //justifyContent: 'center',
        borderRadius: 5,
        shadowOpacity: 0,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
    },
    listItem: {
        fontFamily: 'NunitoSans-Regular',
        marginLeft: 20,
        color: Colors.descriptionColor,
        fontSize: 16,
    },
    textInput: {
        height: 50,
        paddingHorizontal: 20,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        marginTop: 10,
        marginBottom: 10,

        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 2.22,
        elevation: 2,

        fontFamily: "NunitoSans-Regular",
        // color: Colors.fieldtTxtColor,
    },
    textInputLocation: {
        fontFamily: 'NunitoSans-Regular',
        width: 300,
        height: 100,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 10,
        marginRight: 10,
    },
    textSize: {
        fontSize: 19,
        fontFamily: 'NunitoSans-SemiBold',
    },
    merchantDisplayView: {
        flexDirection: 'row',
        flex: 1,
        marginLeft: '17%',
    },
    shiftText: {
        marginLeft: '15%',
        color: Colors.primaryColor,
        fontFamily: 'NunitoSans-SemiBold',
        fontSize: 25,
    },
    confirmBox: {
        width: '30%',
        height: '30%',
        borderRadius: 30,
        backgroundColor: Colors.whiteColor,
    },
    closeButton: {
        position: 'absolute',
        right: 10,
        top: 10,
        elevation: 1000,
        zIndex: 1000,
    },
    modalContainer: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: 'center',
        justifyContent: 'center',
    },
    modalView: {
        height: 200,
        width: 500,
        backgroundColor: Colors.whiteColor,
        borderRadius: 12,
        padding: 25,
        //alignItems: 'center',
        //justifyContent: 'center'
    },
    payment: {
        fontFamily: 'NunitoSans-Regular',
        color: Colors.descriptionColor,
        fontSize: 14,
        textAlign: 'left',
        marginTop: 0,
        marginLeft: 5,
    }
});

export default OutletRewardScreen;
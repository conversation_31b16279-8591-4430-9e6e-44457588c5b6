import React, { useState, useEffect, useRef, useMemo } from 'react';
import { StyleSheet, ScrollView, Image, Text, View, TextInput, TouchableOpacity, useWindowDimensions, Touchable } from 'react-native';
import { collection, query, where, getDocs, limit } from "firebase/firestore";
import { signInAnonymously } from "firebase/auth";
import AsyncStorage from '@react-native-async-storage/async-storage';
import Colors from '../constant/Colors';
import API from '../constant/API';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import ApiClient from '../util/ApiClient';
import { useLinkTo } from "@react-navigation/native";
import AntDesign from 'react-native-vector-icons/AntDesign';
import "react-datepicker/dist/react-datepicker.css";
import '../constant/reservationDate.css'
import { prefix } from "../constant/env";
import { ReactComponent as Call } from '../svg/call.svg';
import { isMobile, updateWebTokenAnonymous } from '../util/commonFuncs';
import { DataStore } from '../store/dataStore';
import { Collections } from '../constant/firebase';
import { ORDER_REGISTER_QR_SALT } from '../constant/common';
import "../constant/styles.css";
import Ionicons from "react-native-vector-icons/Ionicons";
import Hashids from 'hashids';
import { DEFAULT_USER_QUEUE_CATEGORIES } from "../constant/common";
import { ActivityIndicator } from 'react-native';
import "react-native-get-random-values";
import { v4 as uuidv4 } from "uuid";

const hashids = new Hashids(ORDER_REGISTER_QR_SALT);

global.supplementCategoryIdPrev = [];

const QueueScreen = props => {
  const { route } = props;
  const linkTo = useLinkTo();
  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  ////////////////////////////////////////////////////////////////////////////

  const isMounted = useRef(true);

  useEffect(() => {
    isMounted.current = true;
    return () => { isMounted.current = false; };
  }, []);

  ////////////////////////////////////////////////////////////////////////////

  const userIdAnonymous = UserStore.useState(s => s.userIdAnonymous);

  useEffect(() => {
    console.log('userIdAnonymous', userIdAnonymous);
  }, [userIdAnonymous]);

  useEffect(() => {
    if (linkTo) {
      DataStore.update((s) => {
        s.linkToFunc = linkTo;
      });

      global.linkToFunc = linkTo;
    }

    if (route.params === undefined ||
      route.params.subdomain === undefined) {
      linkTo && linkTo(`${prefix}/error`);
      console.log('rs > err > 1');
    }
    else {
      CommonStore.update(s => {
        s.isLoading = true;
      });

      const subdomain = route.params.subdomain;

      if (subdomain) {
        try {
          signInAnonymously(global.auth)
            .then((result) => {
              const firebaseUid = result.user.uid;

              ApiClient.GET(API.getTokenKWeb + firebaseUid).then(
                async (result) => {
                  console.log("getTokenKWeb");
                  console.log(result);

                  if (result && result.token) {
                    await AsyncStorage.setItem("accessToken", result.token);
                    await AsyncStorage.setItem(
                      "refreshToken",
                      result.refreshToken
                    );

                    global.accessToken = result.token;

                    UserStore.update((s) => {
                      s.firebaseUid = result.userId;
                      s.userId = result.userId;
                      s.role = result.role;
                      s.refreshToken = result.refreshToken;
                      s.token = result.token;
                      s.name = '';
                      s.email = '';
                      s.number = '';
                    });

                    var outletSnapshot = null;

                    if (subdomain) {
                      if (subdomain === "192") {
                        // outletSnapshot = await firebase
                        //   .firestore()
                        //   .collection(Collections.Outlet)
                        //   .where(
                        //     "uniqueId",
                        //     "==",
                        //     "b422c1d9-d30b-4de7-ad49-2e601d950919"
                        //   )
                        //   .limit(1)
                        //   .get();

                        outletSnapshot = await getDocs(
                          query(
                            collection(global.db, Collections.Outlet),
                            where(
                              "uniqueId",
                              "==",
                              "b422c1d9-d30b-4de7-ad49-2e601d950919"
                            ),
                            limit(1),
                          )
                        );
                      } else {
                        // outletSnapshot = await firebase
                        //   .firestore()
                        //   .collection(Collections.Outlet)
                        //   .where("subdomain", "==", subdomain)
                        //   .limit(1)
                        //   .get();

                        outletSnapshot = await getDocs(
                          query(
                            collection(global.db, Collections.Outlet),
                            where("subdomain", "==", subdomain),
                            limit(1),
                          )
                        );
                      }
                    } else {
                      CommonStore.update(s => {
                        s.isLoading = false;
                      });

                      console.log("web scan 3");
                      linkTo && linkTo(`${prefix}/error`);
                      console.log('rs > err > 2');
                    }

                    var outlet = {};
                    if (!outletSnapshot.empty) {
                      outlet = outletSnapshot.docs[0].data();
                    }

                    if (
                      outlet &&
                      (outlet.subdomain === subdomain || subdomain === "192")
                    ) {
                      // show pax modal before proceed

                      global.subdomain = outlet.subdomain ? outlet.subdomain : '';

                      await AsyncStorage.setItem(
                        "latestOutletId",
                        outlet.uniqueId
                      );
                      await AsyncStorage.setItem("latestSubdomain", subdomain);

                      document.title = outlet.name;
                      document.getElementsByTagName("META")[2].content =
                        outlet.address;

                      CommonStore.update(
                        (s) => {
                          s.selectedOutlet = outlet;
                        }
                      );

                      ////////////////////////////////////////

                      // end

                      CommonStore.update(s => {
                        s.isLoading = false;
                      });

                      ////////////////////////////////////////

                      const userIdAnonymousRaw = await AsyncStorage.getItem('userIdAnonymous');
                      if (userIdAnonymousRaw) {
                        UserStore.update(s => {
                          s.userIdAnonymous = userIdAnonymousRaw;
                        });
                      } else {
                        const userIdAnonymousTemp = uuidv4();
                        UserStore.update(s => {
                          s.userIdAnonymous = userIdAnonymousTemp;
                        });
                        await AsyncStorage.setItem('userIdAnonymous', userIdAnonymousTemp);
                      }
                      global.userIdAnonymousLoaded = true;
                    } else {
                      CommonStore.update(s => {
                        s.isLoading = false;
                      });

                      console.log("web scan 3");
                      linkTo && linkTo(`${prefix}/error`);
                      console.log('rs > err > 3');
                    }
                  } else {
                    CommonStore.update((s) => {
                      s.alertObj = {
                        title: "Error",
                        message: "Unauthorized access",
                      };

                      s.isLoading = false;
                    });

                    console.log("web scan 4");
                    linkTo && linkTo(`${prefix}/error`);
                    console.log('rs > err > 4');
                  }
                }
              ).catch(ex => {
                console.error(ex);

                CommonStore.update(s => {
                  s.isLoading = false;
                });
              });
            }).catch(ex => {
              console.error(ex);

              CommonStore.update(s => {
                s.isLoading = false;
              });
            });
        }
        catch (ex) {
          console.error(ex);

          CommonStore.update(s => {
            s.isLoading = false;
          });
        }

        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////

        // 2022-10-12 - Get and set user id anonymous

        setTimeout(async () => {
          const userIdAnonymousRaw = await AsyncStorage.getItem('userIdAnonymous');

          if (userIdAnonymousRaw) {
            // means existed

            UserStore.update(s => {
              s.userIdAnonymous = userIdAnonymousRaw;
            });
          }
          else {
            var userIdAnonymousTemp = uuidv4();
            UserStore.update(s => {
              s.userIdAnonymous = userIdAnonymousTemp;
            });

            await AsyncStorage.setItem('userIdAnonymous', userIdAnonymousTemp);
          }

          global.userIdAnonymousLoaded = true;
        }, 100);

        if (userIdAnonymous === 'none') {
          // means haven't set before
        }
      } else {
        CommonStore.update(s => {
          s.isLoading = false;
        });

        console.log('rs > err > 5');
        console.loe(route);

        if (selectedOutlet && selectedOutlet.subdomain) {
          linkTo && linkTo(`${prefix}/outlet/${selectedOutlet.subdomain}`);
        }
        else {
          linkTo && linkTo(`${prefix}/error`);
        }
      }
    }
  }, [linkTo, route]);

  ////////////////////////////////////////////////////////////////////////////

  const loading = CommonStore.useState(s => s.isLoading);
  const selectedOutlet = CommonStore.useState(s => s.selectedOutlet);
  const outletsOpeningDict = CommonStore.useState((s) => s.outletsOpeningDict);
  const outletQueueNumberDict = CommonStore.useState(s => s.outletQueueNumberDict);
  const selectedMerchant = CommonStore.useState(s => s.selectedMerchant);
  const queueConfig = CommonStore.useState(s => s.queueConfig);

  const queueSizeOptions = useMemo(() => {
    if (queueConfig && queueConfig.queueSizeOptions && queueConfig.queueSizeOptions.length > 0) {
      return queueConfig.queueSizeOptions.filter(option => option.isEnabled);
    }
    return DEFAULT_USER_QUEUE_CATEGORIES;
  }, [queueConfig]);

  ////////////////////////////////////////////////////////////////////////////
  // User Input / Selection

  const [expandedCategory, setExpandedCategory] = useState(null);
  const [userName, setUserName] = useState('');
  const [phoneNo, setPhoneNo] = useState('');
  const [paxCount, setPaxCount] = useState(1);

  // User Input Reset
  useEffect(() => {
    // Reset User Input If Null (Back Button Pressed)
    if (expandedCategory === null) {
      setUserName('');
      setPhoneNo('');
      setPaxCount(1);
    }

    // Set pax Count to queueSizeOption.minPax
    if (expandedCategory && expandedCategory.minPax) {
      setPaxCount(expandedCategory.minPax);
    }
  }, [expandedCategory]);

  // Check if expandedCategory is valid based on queueConfig
  useEffect(() => {
    if (expandedCategory && queueConfig) {
      let isValidCategory = false;

      if (queueConfig && queueConfig.queueSizeOptions) {
        isValidCategory = queueConfig.queueSizeOptions.some(
          option => option.name === expandedCategory.name && option.isEnabled
        );
      }

      if (!isValidCategory) {
        showAlert(
          'Invalid Category',
          'The selected queue category is no longer available. Please select another category.'
        );
        setExpandedCategory(null);
      }
    }
  }, [queueConfig, expandedCategory]);

  ////////////////////////////////////////////////////////////////////////////
  // Operation Hours

  const formatTime = (timeString) => {
    const hours = parseInt(timeString.slice(0, 2));
    const minutes = timeString.slice(2);
    const period = hours >= 12 ? 'pm' : 'am';
    const formattedHours = hours % 12 || 12;
    return `${formattedHours}.${minutes} ${period}`;
  };

  const operationTime = useMemo(() => {
    try {
      if (!selectedOutlet || !selectedOutlet.uniqueId) {
        console.error('No selected outlet or outlet ID');
        return '-';
      }

      const outletOpeningData = outletsOpeningDict[selectedOutlet.uniqueId];

      if (!outletOpeningData) {
        throw new Error('No Outlet Data');
      }

      const today = new Date();
      const dayOfWeek = today.getDay();
      const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
      const currentDay = dayNames[dayOfWeek];

      const operatingHours = outletOpeningData[currentDay];
      if (!operatingHours) {
        console.error('No operating hours found for today');
        return '-';
      }

      const [openTime, closeTime] = operatingHours.split('-');
      const formattedOpenTime = formatTime(openTime);
      const formattedCloseTime = formatTime(closeTime);

      return `${formattedOpenTime} - ${formattedCloseTime}`;
    } catch (ex) {
      console.error('Error getting outlet operation time:', ex);
      return '-';
    }
  }, [outletsOpeningDict, selectedOutlet]);

  ////////////////////////////////////////////////////////////////////////////
  const showAlert = (title, message) => {
    CommonStore.update(s => {
      s.alertObj = {
        title,
        message: message.trim(),
      };
    });
  };

  const handleBackButtonPressed = async () => {
    setExpandedCategory(null);

    const subdomain = await AsyncStorage.getItem('latestSubdomain');
    if (!subdomain) {
      linkTo && linkTo(`${prefix}/outlet/`);
    }
    else {
      linkTo && linkTo(`${prefix}/outlet/${subdomain}/`);
    }
  }

  const handleMinusPax = () => {
    if (!expandedCategory) {
      return;
    }

    const minAllowedPax = expandedCategory.minPax === null ? 1 : expandedCategory.minPax;

    if (paxCount > minAllowedPax) {
      setPaxCount(paxCount - 1);
    }
  }

  const handleAddPax = () => {
    if (!expandedCategory) {
      return;
    }

    const maxAllowedPax = expandedCategory.maxPax === null ? 99 : expandedCategory.maxPax;

    if (paxCount < maxAllowedPax) {
      setPaxCount(paxCount + 1);
    }
  }

  const handleCreateQueue = async () => {
    if(!userName || userName.trim().length < 3){
      showAlert('Invalid Input', 'Please enter a valid name (minimum 3 characters)');
      return;
    }

    // Check if phone number contains only digits
    if (!/^\d+$/.test(phoneNo)) {
      showAlert('Invalid Input', 'Phone number must contain numbers only');
      return;
    }

    if (!phoneNo || phoneNo.trim().length < 8 || phoneNo.trim().length > 10) {
      showAlert('Invalid Input', 'Please enter a valid phone number (8-10 digits)');
      return;
    }

    try {
      CommonStore.update(s => {
        s.isLoading = true;
      });

      let body = {
        userName,
        phoneNo: '60' + phoneNo.replace(/\s/g, ''),
        pax: parseInt(paxCount),
        outletId: selectedOutlet.uniqueId,
        merchantId: selectedMerchant.uniqueId,
        category: expandedCategory.name,

        userIdAnonymous: userIdAnonymous,
      }

      updateWebTokenAnonymous(1000);

      await ApiClient.POST(API.registerQueueV2, body)
        .then((res) => {
          if (res.status === 'success') {
            console.log('successfully created queue');
            console.log(res.data);

            CommonStore.update(s => {
              s.alertObj = {
                title: 'Success',
                message: 'Queue created successfully',
                onConfirmPressed: () => {
                  props.navigation.navigate('Queue Successful - KooDoo Web Order', {
                    subdomain: selectedOutlet.subdomain,
                    queueId: res.data.uniqueId,
                    chat: true,
                  });
                },
              };
            });
          }
          else {
            console.log(res)
            throw new Error('Failed to create queue');
          }
        })
        .catch((error) => {
          console.error('Error creating queue:', error);
          CommonStore.update(s => {
            s.alertObj = {
              title: 'Error',
              message: 'Failed to create queue',
            };
          });
        });
    }
    catch (error) {
      console.error(error);

      CommonStore.update(s => {
        s.alertObj = {
          title: 'Error',
          message: 'Failed to enter queue, Please Try Again Later',
        };
      });
    }
    finally {
      CommonStore.update(s => {
        s.isLoading = false;
      });
    }
  }

  ////////////////////////////////////////////////////////////////////////////

  return (
    <View style={{ height: windowHeight }}>
      <ScrollView showsHorizontalScrollIndicator={false} style={{ flex: 1 }}>
        <View style={{
          width: isMobile() ? windowWidth : windowWidth,
        }}>

          {/* Back Button Container */}
          <View style={{ marginTop: "5%", marginLeft: "5%" }}>
            <TouchableOpacity onPress={() => { handleBackButtonPressed() }}>
              <Ionicons name="arrow-back-circle-outline" size={40} style={{ color: Colors.primaryColor }} />
            </TouchableOpacity>
          </View>

          {/* Outlet Info */}
          <View style={{
            backgroundColor: Colors.primaryColor,
            paddingHorizontal: 10,
            paddingTop: "16%",
            paddingBottom: 20,
            width: "90%",
            marginBottom: 20,
            marginTop: "20%",
            marginHorizontal: 'auto',
            borderRadius: 15,
          }}>
            {/* Logo */}
            <View style={{
              position: 'absolute',
              top: -50,
              left: '50%',
              transform: [{ translateX: -50 }],
              marginBottom: 0,
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.25,
              shadowRadius: 3.5,
              elevation: 5,
            }}>
              <Image
                source={selectedMerchant && selectedMerchant.logo !== ""
                  ? { uri: selectedMerchant.logo }
                  : require('../asset/image/logo.png')}
                style={{
                  width: 100,
                  height: 100,
                  alignSelf: "center",
                }}
                onError={(e) => console.log("Error loading image:", e.nativeEvent.error)}
              />
            </View>

            {/* Outlet Name */}
            <Text style={{
              fontFamily: 'NunitoSans-SemiBold',
              color: Colors.whiteColor,
              fontSize: 22,
              textAlign: 'center',
              marginTop: 0,
              fontWeight: 400,
            }}>
              {selectedOutlet ? selectedOutlet.name : ''}
            </Text>

            {/* Address Line */}
            <View style={{
              justifyContent: 'center',
              alignItems: 'flex-start',
              flexDirection: 'row',
              marginTop: 20,
              marginHorizontal: 'auto',
              width: "85%",
            }}>
              <Ionicons name='location-outline' size={14} color={Colors.whiteColor} />

              <Text style={{
                fontFamily: 'NunitoSans-Regular',
                color: Colors.whiteColor,
                fontSize: 12,
                textAlign: 'center',
                marginTop: 0,
              }}>
                {selectedOutlet ? selectedOutlet.address : ''}
              </Text>
            </View>

            {/* Operation Hours & Phone Number */}
            <View style={{
              justifyContent: 'center',
              alignItems: 'center',
              flexDirection: 'row',
              marginTop: 15,
            }}>
              {/* Operation Hours */}
              <View style={{ backgroundColor: '#284a3a', paddingHorizontal: 8, paddingVertical: 6, borderRadius: 5 }}>
                <TouchableOpacity
                  style={{
                    justifyContent: 'center',
                    alignItems: 'center',
                    flexDirection: 'row',
                  }}
                >
                  <AntDesign name="clockcircleo" size={13} color={Colors.whiteColor} />

                  <Text style={{
                    fontFamily: 'NunitoSans-Regular',
                    color: Colors.whiteColor,
                    fontSize: 13,
                    marginTop: 0,
                    marginLeft: 5,
                  }}>
                    {operationTime}
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Phone Number */}
              <View style={{ backgroundColor: '#284a3a', paddingHorizontal: 8, paddingVertical: 6, borderRadius: 5, marginLeft: 10, }}>
                <TouchableOpacity>
                  <View style={{
                    justifyContent: 'center',
                    alignItems: 'center',
                    flexDirection: 'row',
                    //width: isMobile() ? windowWidth : windowWidth * 1,
                  }}>
                    <Call
                      width={16}
                      height={16}
                      color={Colors.whiteColor}
                    />
                    <Text style={{
                      fontFamily: 'NunitoSans-Regular',
                      color: Colors.whiteColor,
                      fontSize: 13,
                      textAlign: 'center',
                      marginTop: 0,
                      marginLeft: 5,
                      // width: '100%',
                    }}>
                      {selectedOutlet ? selectedOutlet.phone : ''}
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>

          {/* Selected Category Container */}
          <View style={{
            marginHorizontal: 'auto',
            width: "90%",
            paddingTop: 20,
            background: Colors.lightPrimary,
            borderRadius: 15,
          }}>
            <View style={{
              paddingBottom: 20,
              gap: 10,
              paddingLeft: 20,
              borderBottomWidth: 1,
              borderBottomColor: Colors.tabGrey,
            }}>
              <Text style={{
                fontFamily: 'NunitoSans-Bold',
                colors: Colors.tabGrey,
                fontSize: 16,
              }}>
                You have chosen
              </Text>
              <Text style={{
                fontFamily: 'NunitoSans-Bold',
                color: Colors.primaryColor,
                fontSize: 20,
              }}>
                Dine-In
              </Text>
            </View>

            {expandedCategory === null ? (
              // Not Selected View
              <>
                {/* Select a Category */}
                <View style={{
                  padding: 20,
                  paddingLeft: 20,
                  backgroundColor: Colors.whiteColor,
                }}>
                  <Text style={{
                    fontFamily: 'NunitoSans-Bold',
                    colr: Colors.blackColor,
                    fontWeight: 'bold',
                    fontSize: 20,
                    textAlign: 'left',
                  }}>
                    Select a category
                  </Text>
                </View>

                {/* Category List */}
                <View style={{ paddingHorizontal: 15, paddingBottom: 20, backgroundColor: Colors.whiteColor, borderBottomRightRadius: 15, borderBottomLeftRadius: 15, }}>
                  {queueSizeOptions.map((item, index) => (
                    <TouchableOpacity
                      key={index}
                      style={{
                        backgroundColor: '#f0f2f0',
                        borderRadius: 10,
                        padding: 15,
                        marginVertical: 10,
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.2,
                        shadowRadius: 3.84,
                      }}
                      onPress={() => setExpandedCategory(item)}
                    >
                      <Text style={{
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: 16,
                        color: Colors.blackColor,
                      }}>
                        {item.name}
                      </Text>
                      <View style={{
                        flexDirection: 'row',
                        gap: 6,
                        borderWidth: 1,
                        borderColor: Colors.tabGrey,
                        borderRadius: 15,
                        padding: 3,
                        paddingHorizontal: 10,
                        width: '18%',
                        justifyContent: 'center',
                      }}>
                        <Ionicons name="people" size={16} />

                        <Text style={{
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: 16,
                          color: Colors.primaryColor,
                        }}>
                          {(outletQueueNumberDict[item.name] && outletQueueNumberDict[item.name].length) || 0}
                        </Text>
                      </View>
                    </TouchableOpacity>
                  ))}
                </View>
              </>
            ) : (
              // Selected View
              <>
                <View style={{ paddingHorizontal: 15, paddingBottom: 20, backgroundColor: Colors.whiteColor, borderBottomLeftRadius: 15, borderBottomRightRadius: 15, }}>
                  <View style={{ marginVertical: 20 }}>
                    <View style={{ flexDirection: 'row', gap: 5, alignItems: 'center', height: '1' }}>
                      <Text style={{ fontSize: 18, fontWeight: 'bold', }}>({expandedCategory.name})</Text>

                      <View style={{
                        flexDirection: 'row',
                        gap: 6,
                        borderWidth: 1,
                        borderColor: Colors.tabGrey,
                        borderRadius: 15,
                        // padding:3,
                        paddingHorizontal: 10,
                        width: '18%',
                        justifyContent: 'center',
                        alignItems: 'center',
                        marginTop: 4,
                      }}>
                        <Ionicons name="people" size={16} />
                        <Text style={{
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: 16,
                          color: Colors.primaryColor,
                        }}>
                          {(outletQueueNumberDict[expandedCategory.name] && outletQueueNumberDict[expandedCategory.name].length) || 0}
                        </Text>
                      </View>
                    </View>

                    <Text style={{ fontSize: 16, marginTop: 10, }}>Join the line</Text>

                    <TextInput
                      placeholder="First name *"
                      style={{
                        borderWidth: 1,
                        borderColor: '#ccc',
                        borderRadius: 5,
                        padding: 10,
                        marginTop: 10,
                      }}
                      onChangeText={(text) => setUserName(text)}
                    />

                    <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 10 }}>
                      <Text style={{ marginRight: 5, fontWeight: 'bold' }}>+60</Text>
                      {/* <View style={{ flexDirection: 'row', alignItems: 'center', borderWidth: 1, borderColor: '#ccc', borderRadius: 5, padding: 10, flex: 1 }}> */}
                      {/* Country flag */}
                      <TextInput
                        placeholder="Phone *"
                        style={{
                          flex: 1,
                          borderWidth: 1,
                          borderColor: '#ccc',
                          borderRadius: 5,
                          padding: 10,
                        }}
                        onChangeText={(text) => setPhoneNo(text)}
                      />

                    </View>

                    {/* Pax Selector */}
                    <View>
                      <Text style={{ fontSize: 18, fontWeight: 'bold', marginTop: 15 }}>Pax</Text>
                      <Text style={{ fontSize: 14, marginTop: 6 }}>{expandedCategory.minPax}-{expandedCategory.maxPax} people per queue</Text>

                      <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 10, justifyContent: 'center' }}>
                        <TouchableOpacity onPress={() => { handleMinusPax() }}>
                          <Text style={{ fontSize: 40, color: Colors.tabGrey }}>-</Text>
                        </TouchableOpacity>

                        <Text style={{
                          borderWidth: 1,
                          borderRadius: 15,
                          borderColor: Colors.tabGrey,
                          paddingVertical: 14,
                          padding: 20,
                          fontSize: 26,
                          marginHorizontal: 30,
                          fontWeight: 'bold',
                        }}>
                          {paxCount}
                        </Text>

                        <TouchableOpacity onPress={() => { handleAddPax() }}>
                          <Text style={{ fontSize: 40 }}>+</Text>
                        </TouchableOpacity>
                      </View>
                    </View>

                    {/* Button Container */}
                    <View style={{ flexDirection: 'row', width: '100%', marginTop: 15, justifyContent: 'space-between', }}>
                      <TouchableOpacity disabled={loading} onPress={() => setExpandedCategory(null)} style={{
                        backgroundColor: Colors.tabGrey,
                        borderRadius: 5,
                        padding: 10,
                        alignItems: 'center',
                        width: '35%',
                      }}>
                        <Text style={{ color: '#fff', fontWeight: 'bold' }}>Back</Text>
                      </TouchableOpacity>

                      <TouchableOpacity disabled={loading} onPress={() => { handleCreateQueue() }} style={{
                        backgroundColor: '#6A5ACD', // Button color
                        borderRadius: 5,
                        padding: 10,
                        alignItems: 'center',
                        width: '60%',
                      }}>
                        {loading ? <ActivityIndicator size="small" color="#fff" /> : <Text style={{ color: '#fff', fontWeight: 'bold' }}>Get in line now</Text>}
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              </>
            )}
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default QueueScreen;
